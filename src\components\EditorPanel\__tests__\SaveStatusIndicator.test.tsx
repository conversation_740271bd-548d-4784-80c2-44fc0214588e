/**
 * SaveStatusIndicator 测试文件
 * 验证保存状态指示器的显示和交互功能
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SaveStatusIndicator from '../SaveStatusIndicator';
import { SaveState } from '@/types';

describe('SaveStatusIndicator', () => {
  const mockOnRetry = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该在 idle 状态时不显示任何内容', () => {
    const { container } = render(
      <SaveStatusIndicator saveState={SaveState.idle} />
    );
    
    expect(container.firstChild).toBeNull();
  });

  test('应该正确显示 pending 状态', () => {
    render(<SaveStatusIndicator saveState={SaveState.pending} />);
    
    expect(screen.getByText('待保存')).toBeInTheDocument();
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  test('应该正确显示 saving 状态', () => {
    render(<SaveStatusIndicator saveState={SaveState.saving} />);
    
    expect(screen.getByText('保存中...')).toBeInTheDocument();
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  test('应该正确显示 saved 状态并包含时间戳', () => {
    render(<SaveStatusIndicator saveState={SaveState.saved} />);
    
    expect(screen.getByText('已保存')).toBeInTheDocument();
    // 检查是否有时间戳显示
    expect(document.querySelector('svg')).toBeInTheDocument(); // 勾选图标
  });

  test('应该正确显示 error 状态', () => {
    const errorMessage = '网络连接失败';
    
    render(
      <SaveStatusIndicator 
        saveState={SaveState.error} 
        error={errorMessage}
        onRetry={mockOnRetry}
      />
    );
    
    expect(screen.getByText('保存失败')).toBeInTheDocument();
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('重试')).toBeInTheDocument();
  });

  test('应该在点击重试按钮时调用 onRetry 回调', () => {
    render(
      <SaveStatusIndicator 
        saveState={SaveState.error} 
        error="保存失败"
        onRetry={mockOnRetry}
      />
    );
    
    const retryButton = screen.getByText('重试');
    fireEvent.click(retryButton);
    
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  test('应该截断过长的错误信息', () => {
    const longError = '这是一个非常长的错误信息，应该被截断显示以避免界面布局问题';
    
    render(
      <SaveStatusIndicator 
        saveState={SaveState.error} 
        error={longError}
      />
    );
    
    // 检查是否显示了截断的错误信息
    const errorElement = screen.getByText(/这是一个非常长的错误信息/);
    expect(errorElement.textContent).toContain('...');
  });

  test('应该在 saved 状态3秒后自动隐藏', async () => {
    jest.useFakeTimers();
    
    const { container } = render(
      <SaveStatusIndicator saveState={SaveState.saved} />
    );
    
    // 初始应该显示
    expect(screen.getByText('已保存')).toBeInTheDocument();
    
    // 快进3秒
    jest.advanceTimersByTime(3000);
    
    // 等待状态更新
    await waitFor(() => {
      expect(container.firstChild).toBeNull();
    });
    
    jest.useRealTimers();
  });

  test('应该在没有 onRetry 回调时不显示重试按钮', () => {
    render(
      <SaveStatusIndicator 
        saveState={SaveState.error} 
        error="保存失败"
      />
    );
    
    expect(screen.queryByText('重试')).not.toBeInTheDocument();
  });

  test('应该应用自定义样式类名', () => {
    const customClassName = 'custom-save-indicator';
    
    render(
      <SaveStatusIndicator 
        saveState={SaveState.pending} 
        className={customClassName}
      />
    );
    
    expect(document.querySelector(`.${customClassName}`)).toBeInTheDocument();
  });
});

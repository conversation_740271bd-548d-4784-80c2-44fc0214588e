"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts":
/*!*********************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/ClipboardManager.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClipboardManager: function() { return /* binding */ ClipboardManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\n * 剪贴板管理器\n * 负责思维导图节点的复制粘贴功能\n */ \nclass ClipboardManager {\n    /**\n   * 实现ICleanable接口 - 是否已销毁\n   */ get isDestroyed() {\n        return this._cleanupState === _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYED;\n    }\n    /**\n   * 实现ICleanable接口 - 当前清理状态\n   */ get cleanupState() {\n        return this._cleanupState;\n    }\n    /**\n   * 复制节点\n   */ copyNode(node) {\n        try {\n            if (!node || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的节点或思维导图实例\");\n                return false;\n            }\n            // 激活目标节点\n            this.mindMapInstance.renderer.clearActiveNodeList();\n            this.mindMapInstance.renderer.addNodeToActiveList(node);\n            // 使用SimpleMindMap的内部复制方法\n            const copiedData = this.mindMapInstance.renderer.copyNode();\n            if (copiedData && copiedData.length > 0) {\n                this.copiedNodeData = copiedData;\n                console.log(\"✅ 复制节点成功:\", copiedData);\n                return true;\n            } else {\n                console.warn(\"⚠️ 复制节点失败，没有获取到数据\");\n                return false;\n            }\n        } catch (error) {\n            console.error(\"❌ 复制节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 粘贴节点作为子节点\n   */ pasteAsChild(targetNode) {\n        try {\n            if (!this.hasCopiedData()) {\n                console.warn(\"⚠️ 没有可粘贴的数据\");\n                return false;\n            }\n            if (!targetNode || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的目标节点或思维导图实例\");\n                return false;\n            }\n            // 激活目标节点\n            this.mindMapInstance.renderer.clearActiveNodeList();\n            this.mindMapInstance.renderer.addNodeToActiveList(targetNode);\n            // 执行粘贴命令\n            this.mindMapInstance.execCommand(\"PASTE_NODE\", this.copiedNodeData);\n            console.log(\"✅ 粘贴为子节点成功\");\n            return true;\n        } catch (error) {\n            console.error(\"❌ 粘贴为子节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 粘贴节点作为同级节点\n   */ pasteAsSibling(targetNode) {\n        try {\n            if (!this.hasCopiedData()) {\n                console.warn(\"⚠️ 没有可粘贴的数据\");\n                return false;\n            }\n            if (!targetNode || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的目标节点或思维导图实例\");\n                return false;\n            }\n            // 根节点不能粘贴同级节点\n            if (targetNode.isRoot) {\n                console.warn(\"⚠️ 根节点不能粘贴同级节点\");\n                return false;\n            }\n            // 激活目标节点的父节点\n            if (targetNode.parent) {\n                this.mindMapInstance.renderer.clearActiveNodeList();\n                this.mindMapInstance.renderer.addNodeToActiveList(targetNode.parent);\n                // 执行粘贴命令\n                this.mindMapInstance.execCommand(\"PASTE_NODE\", this.copiedNodeData);\n                console.log(\"✅ 粘贴为同级节点成功\");\n                return true;\n            } else {\n                console.warn(\"⚠️ 目标节点没有父节点\");\n                return false;\n            }\n        } catch (error) {\n            console.error(\"❌ 粘贴为同级节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 检查是否有可粘贴的数据\n   */ hasCopiedData() {\n        return this.copiedNodeData !== null && Array.isArray(this.copiedNodeData) && this.copiedNodeData.length > 0;\n    }\n    /**\n   * 获取复制的数据\n   */ getCopiedData() {\n        return this.copiedNodeData;\n    }\n    /**\n   * 清空复制的数据\n   */ clearCopiedData() {\n        this.copiedNodeData = null;\n        console.log(\"✅ 清空复制数据\");\n    }\n    /**\n   * 获取复制数据的摘要信息\n   */ getCopiedDataSummary() {\n        var _this_copiedNodeData__data, _this_copiedNodeData_;\n        if (!this.hasCopiedData()) {\n            return \"无复制数据\";\n        }\n        const nodeCount = this.copiedNodeData.length;\n        const firstNodeText = ((_this_copiedNodeData_ = this.copiedNodeData[0]) === null || _this_copiedNodeData_ === void 0 ? void 0 : (_this_copiedNodeData__data = _this_copiedNodeData_.data) === null || _this_copiedNodeData__data === void 0 ? void 0 : _this_copiedNodeData__data.text) || \"未知节点\";\n        if (nodeCount === 1) {\n            return \"已复制: \".concat(firstNodeText);\n        } else {\n            return \"已复制: \".concat(firstNodeText, \" 等 \").concat(nodeCount, \" 个节点\");\n        }\n    }\n    /**\n   * 销毁剪贴板管理器\n   */ destroy() {\n        this.clearCopiedData();\n        this.mindMapInstance = null;\n        console.log(\"✅ 剪贴板管理器销毁完成\");\n    }\n    constructor(mindMapInstance){\n        this.copiedNodeData = null;\n        this.mindMapInstance = null;\n        // 清理状态管理\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE;\n        this.mindMapInstance = mindMapInstance;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\n"));

/***/ })

});
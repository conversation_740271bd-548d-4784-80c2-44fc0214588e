"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/services/SaveManager.ts":
/*!*************************************!*\
  !*** ./src/services/SaveManager.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SaveManager: function() { return /* binding */ SaveManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\n * SaveManager - 统一保存管理器\n * 负责管理所有文件的保存操作，解决多个独立定时器系统导致的竞态条件问题\n * \n * 功能特性：\n * - 统一的防抖机制\n * - 文件状态管理和锁定\n * - 保存队列处理\n * - 错误处理和重试机制\n * - 保存状态回调通知\n */ \nclass SaveManager {\n    /**\n   * 获取 SaveManager 单例实例\n   */ static getInstance() {\n        if (!SaveManager.instance) {\n            SaveManager.instance = new SaveManager();\n        }\n        return SaveManager.instance;\n    }\n    /**\n   * 设置保存执行回调\n   */ setSaveExecuteCallback(callback) {\n        this.saveExecuteCallback = callback;\n    }\n    /**\n   * 添加保存状态变更监听器\n   */ addStateChangeListener(callback) {\n        this.stateChangeCallbacks.add(callback);\n    }\n    /**\n   * 移除保存状态变更监听器\n   */ removeStateChangeListener(callback) {\n        this.stateChangeCallbacks.delete(callback);\n    }\n    /**\n   * 通知保存状态变更\n   */ notifyStateChange(fileId, state, error) {\n        // 添加详细的状态变更日志\n        const timestamp = new Date().toLocaleTimeString(\"zh-CN\", {\n            hour12: false\n        });\n        console.log(\"\\uD83D\\uDCCA [\".concat(timestamp, \"] 保存状态变更:\"), {\n            fileId,\n            state,\n            error: error || \"无\",\n            callbackCount: this.stateChangeCallbacks.size\n        });\n        this.stateChangeCallbacks.forEach((callback)=>{\n            try {\n                callback(fileId, state, error);\n            } catch (err) {\n                console.error(\"❌ 保存状态回调执行失败:\", err);\n            }\n        });\n    }\n    /**\n   * 获取文件保存统计信息\n   */ getFileSaveStats(fileId) {\n        const fileState = this.fileSaveStates.get(fileId);\n        if (!fileState) return null;\n        return {\n            totalSaves: fileState.retryCount > 0 ? 1 : fileState.lastSaveTime > 0 ? 1 : 0,\n            lastSaveTime: fileState.lastSaveTime,\n            errorCount: fileState.retryCount,\n            currentState: fileState.state\n        };\n    }\n    /**\n   * 强制重试保存\n   */ async retryFileSave(fileId) {\n        const fileState = this.fileSaveStates.get(fileId);\n        if (!fileState || fileState.state !== _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.error) {\n            console.warn(\"⚠️ 无法重试保存，文件状态不正确:\", fileId, fileState === null || fileState === void 0 ? void 0 : fileState.state);\n            return false;\n        }\n        console.log(\"\\uD83D\\uDD04 手动重试保存:\", fileId);\n        fileState.retryCount = 0; // 重置重试计数\n        return await this.executeSave(fileId);\n    }\n    /**\n   * 获取文件保存状态\n   */ getFileSaveState(fileId) {\n        return this.fileSaveStates.get(fileId) || null;\n    }\n    /**\n   * 设置当前活动文件\n   */ setCurrentFile(fileId) {\n        this.currentFileId = fileId;\n        console.log(\"\\uD83D\\uDCC1 设置当前活动文件:\", fileId);\n    }\n    /**\n   * 获取当前活动文件ID\n   */ getCurrentFileId() {\n        return this.currentFileId;\n    }\n    /**\n   * 调度自动保存\n   */ scheduleAutoSave(fileId, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const mergedOptions = {\n            ...this.defaultOptions,\n            ...options\n        };\n        // 获取或创建文件保存状态\n        let fileState = this.fileSaveStates.get(fileId);\n        if (!fileState) {\n            fileState = {\n                fileId,\n                state: _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle,\n                lastSaveTime: 0,\n                retryCount: 0\n            };\n            this.fileSaveStates.set(fileId, fileState);\n        }\n        // 如果当前正在保存，跳过\n        if (fileState.state === _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saving) {\n            console.log(\"⏳ 文件正在保存中，跳过新的保存请求:\", fileId);\n            return;\n        }\n        // 清除现有定时器\n        if (fileState.timer) {\n            clearTimeout(fileState.timer);\n        }\n        // 更新状态\n        fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.pending;\n        fileState.pendingData = data;\n        fileState.options = mergedOptions;\n        this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.pending);\n        if (mergedOptions.immediate) {\n            // 立即执行保存\n            this.executeSave(fileId);\n        } else {\n            // 设置防抖定时器\n            fileState.timer = setTimeout(()=>{\n                this.executeSave(fileId);\n            }, mergedOptions.debounceDelay);\n        }\n        console.log(\"\\uD83D\\uDCBE 调度自动保存 [\".concat(mergedOptions.saveType, \"]:\"), fileId, \"延迟: \".concat(mergedOptions.immediate ? \"立即\" : mergedOptions.debounceDelay + \"ms\"));\n    }\n    /**\n   * 强制立即保存\n   */ async forceSave(fileId) {\n        console.log(\"\\uD83D\\uDE80 强制立即保存:\", fileId);\n        const fileState = this.fileSaveStates.get(fileId);\n        if (!fileState || !fileState.pendingData) {\n            console.log(\"\\uD83D\\uDCDD 没有待保存的数据:\", fileId);\n            return true;\n        }\n        // 清除定时器\n        if (fileState.timer) {\n            clearTimeout(fileState.timer);\n            fileState.timer = undefined;\n        }\n        return await this.executeSave(fileId);\n    }\n    /**\n   * 执行保存操作\n   */ async executeSave(fileId) {\n        const fileState = this.fileSaveStates.get(fileId);\n        if (!fileState || !fileState.pendingData) {\n            console.warn(\"⚠️ 执行保存时找不到文件状态或数据:\", fileId);\n            return false;\n        }\n        // 验证文件是否仍然是当前活动文件（防止文件切换后的延迟保存）\n        if (this.currentFileId && this.currentFileId !== fileId) {\n            console.log(\"\\uD83D\\uDD12 文件已切换，跳过保存:\", fileId, \"当前文件:\", this.currentFileId);\n            fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle;\n            this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle);\n            return false;\n        }\n        try {\n            // 更新状态为保存中\n            fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saving;\n            fileState.error = undefined;\n            this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saving);\n            console.log(\"\\uD83D\\uDCBE 开始执行保存:\", fileId);\n            // 执行保存回调\n            if (!this.saveExecuteCallback) {\n                throw new Error(\"保存执行回调未设置\");\n            }\n            const success = await this.saveExecuteCallback(fileId, fileState.pendingData, fileState.options);\n            if (success) {\n                // 保存成功\n                fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saved;\n                fileState.lastSaveTime = Date.now();\n                fileState.retryCount = 0;\n                fileState.pendingData = undefined;\n                this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saved);\n                console.log(\"✅ 保存成功:\", fileId);\n                // 3秒后自动重置状态为空闲\n                setTimeout(()=>{\n                    if (fileState && fileState.state === _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saved) {\n                        fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle;\n                        this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle);\n                    }\n                }, 3000);\n                return true;\n            } else {\n                throw new Error(\"保存操作返回失败\");\n            }\n        } catch (error) {\n            var _fileState_options;\n            // 保存失败\n            const errorMessage = error instanceof Error ? error.message : \"未知错误\";\n            fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.error;\n            fileState.error = errorMessage;\n            fileState.retryCount++;\n            this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.error, errorMessage);\n            console.error(\"❌ 保存失败:\", fileId, errorMessage);\n            // 如果还有重试次数，安排重试\n            if (fileState.retryCount < (((_fileState_options = fileState.options) === null || _fileState_options === void 0 ? void 0 : _fileState_options.retryCount) || this.defaultOptions.retryCount)) {\n                var _fileState_options1;\n                console.log(\"\\uD83D\\uDD04 安排重试保存 (\".concat(fileState.retryCount, \"/\").concat(((_fileState_options1 = fileState.options) === null || _fileState_options1 === void 0 ? void 0 : _fileState_options1.retryCount) || this.defaultOptions.retryCount, \"):\"), fileId);\n                setTimeout(()=>{\n                    this.executeSave(fileId);\n                }, 2000 * fileState.retryCount); // 递增延迟重试\n            }\n            return false;\n        }\n    }\n    /**\n   * 文件切换处理\n   */ async switchFile(fromFileId, toFileId) {\n        console.log(\"\\uD83D\\uDD04 文件切换:\", fromFileId, \"->\", toFileId);\n        // 强制保存当前文件\n        if (fromFileId) {\n            await this.forceSave(fromFileId);\n        }\n        // 设置新的当前文件\n        this.setCurrentFile(toFileId);\n    }\n    /**\n   * 清理文件保存状态\n   */ clearFileState(fileId) {\n        const fileState = this.fileSaveStates.get(fileId);\n        if (fileState) {\n            // 清除定时器\n            if (fileState.timer) {\n                clearTimeout(fileState.timer);\n            }\n            // 移除状态\n            this.fileSaveStates.delete(fileId);\n            console.log(\"\\uD83D\\uDDD1️ 清理文件保存状态:\", fileId);\n        }\n    }\n    /**\n   * 获取所有文件的保存状态\n   */ getAllFileSaveStates() {\n        return new Map(this.fileSaveStates);\n    }\n    /**\n   * 销毁管理器（清理所有资源）\n   */ destroy() {\n        // 清除所有定时器\n        this.fileSaveStates.forEach((fileState)=>{\n            if (fileState.timer) {\n                clearTimeout(fileState.timer);\n            }\n        });\n        // 清理状态\n        this.fileSaveStates.clear();\n        this.stateChangeCallbacks.clear();\n        this.saveExecuteCallback = null;\n        this.currentFileId = null;\n        console.log(\"\\uD83D\\uDDD1️ SaveManager 已销毁\");\n    }\n    constructor(){\n        /** 文件保存状态映射 */ this.fileSaveStates = new Map();\n        /** 当前活动文件ID */ this.currentFileId = null;\n        /** 保存状态变更回调 */ this.stateChangeCallbacks = new Set();\n        /** 保存执行回调 */ this.saveExecuteCallback = null;\n        /** 默认保存选项 */ this.defaultOptions = {\n            debounceDelay: 1000,\n            saveType: \"editor\",\n            immediate: false,\n            retryCount: 3\n        };\n    // 私有构造函数，确保单例模式\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/SaveManager.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 清理状态管理\n    const isCleaningUpRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源 - 优化版本，支持异步清理协调\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        // 防止重复清理\n        if (isCleaningUpRef.current) {\n            console.log(\"\\uD83D\\uDD12 清理已在进行中，跳过重复清理\");\n            return;\n        }\n        isCleaningUpRef.current = true;\n        console.log(\"\\uD83E\\uDDF9 开始协调清理所有Manager...\");\n        try {\n            // 按优先级顺序清理Manager\n            // 1. 首先清理事件管理器，停止所有事件监听\n            if (eventManagerRef.current) {\n                console.log(\"\\uD83D\\uDD27 清理EventManager...\");\n                await eventManagerRef.current.destroy();\n                eventManagerRef.current = null;\n            }\n            // 2. 清理样式管理器\n            if (styleManagerRef.current) {\n                console.log(\"\\uD83C\\uDFA8 清理StyleManager...\");\n                await styleManagerRef.current.destroy();\n                styleManagerRef.current = null;\n            }\n            // 3. 清理拖拽增强器\n            if (dragEnhancerRef.current) {\n                console.log(\"\\uD83D\\uDDB1️ 清理DragEnhancer...\");\n                await dragEnhancerRef.current.destroy();\n                dragEnhancerRef.current = null;\n            }\n            // 4. 清理剪贴板管理器\n            if (clipboardManagerRef.current) {\n                console.log(\"\\uD83D\\uDCCB 清理ClipboardManager...\");\n                await clipboardManagerRef.current.destroy();\n                clipboardManagerRef.current = null;\n            }\n            // 5. 清理选择管理器（如果存在）\n            if (selectionManagerRef.current) {\n                console.log(\"\\uD83C\\uDFAF 清理SelectionManager...\");\n                await selectionManagerRef.current.destroy();\n                selectionManagerRef.current = null;\n            }\n            // 6. 最后清理核心管理器，销毁SimpleMindMap实例\n            if (coreManagerRef.current) {\n                console.log(\"⚙️ 清理CoreManager...\");\n                await coreManagerRef.current.destroy();\n                coreManagerRef.current = null;\n            }\n            setIsInitialized(false);\n            console.log(\"✅ 所有Manager清理完成\");\n        } catch (error) {\n            console.error(\"❌ Manager清理过程中发生错误:\", error);\n            // 即使清理失败，也要重置状态以允许重试\n            setIsInitialized(false);\n        } finally{\n            isCleaningUpRef.current = false;\n        }\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 暂时禁用拖拽增强器，让SimpleMindMap原生拖拽功能正常工作\n            // TODO: 后续优化拖拽增强器与原生功能的兼容性\n            /*\r\n      if (!readonly && enableDragEnhancement) {\r\n        const enhancedDragConfig = {\r\n          ...dragConfig,\r\n          persistence: {\r\n            autoSave: dragConfig.persistence?.autoSave ?? true,\r\n            saveDelay: dragConfig.persistence?.saveDelay ?? 1000,\r\n            onSave: (data: any) => {\r\n              if (onDataChange) {\r\n                onDataChange(data);\r\n              }\r\n              if (dragConfig.persistence?.onSave) {\r\n                dragConfig.persistence.onSave(data);\r\n              }\r\n            }\r\n          }\r\n        };\r\n\r\n        dragEnhancerRef.current = new DragEnhancer(mindMapInstance, enhancedDragConfig);\r\n        dragEnhancerRef.current.initialize();\r\n      }\r\n      */ setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            }\n        }), [\n        resizeCanvas,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 496,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 495,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 491,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 512,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 508,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 548,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 571,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 531,\n        columnNumber: 5\n    }, undefined);\n}, \"em3CVu2OfmPxSmKOY3HF23MkZ1k=\")), \"em3CVu2OfmPxSmKOY3HF23MkZ1k=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});
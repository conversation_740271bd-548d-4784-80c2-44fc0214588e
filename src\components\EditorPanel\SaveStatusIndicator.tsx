/**
 * SaveStatusIndicator - 保存状态指示器组件
 * 显示文件的保存状态，包括保存中、已保存、保存失败等状态
 */

'use client'

import React, { useState, useEffect } from 'react'
import { SaveState } from '@/types'

interface SaveStatusIndicatorProps {
  /** 当前保存状态 */
  saveState: SaveState
  /** 错误信息 */
  error?: string
  /** 重试回调 */
  onRetry?: () => void
  /** 自定义样式类名 */
  className?: string
}

const SaveStatusIndicator: React.FC<SaveStatusIndicatorProps> = ({
  saveState,
  error,
  onRetry,
  className = ''
}) => {
  const [showStatus, setShowStatus] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null)

  // 控制状态显示的逻辑
  useEffect(() => {
    if (saveState === SaveState.idle) {
      setShowStatus(false)
      return
    }

    setShowStatus(true)

    // 成功状态3秒后自动隐藏
    if (saveState === SaveState.saved) {
      setLastSaveTime(new Date())
      const timer = setTimeout(() => {
        setShowStatus(false)
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [saveState])

  // 如果不显示状态，返回空
  if (!showStatus) {
    return null
  }

  // 渲染不同状态的内容
  const renderStatusContent = () => {
    switch (saveState) {
      case SaveState.pending:
        return (
          <div className="flex items-center gap-2 px-3 py-2 bg-yellow-500/10 border border-yellow-500/30 rounded-md">
            <div className="w-3 h-3 rounded-full bg-yellow-500 animate-pulse"></div>
            <span className="text-yellow-400 text-sm font-medium">待保存</span>
          </div>
        )

      case SaveState.saving:
        return (
          <div className="flex items-center gap-2 px-3 py-2 bg-blue-500/10 border border-blue-500/30 rounded-md">
            <div className="w-3 h-3 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
            <span className="text-blue-400 text-sm font-medium">保存中...</span>
          </div>
        )

      case SaveState.saved:
        return (
          <div className="flex items-center gap-2 px-3 py-2 bg-green-500/10 border border-green-500/30 rounded-md">
            <div className="w-3 h-3 rounded-full bg-green-500 flex items-center justify-center">
              <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-green-400 text-sm font-medium">
              已保存
              {lastSaveTime && (
                <span className="text-green-300 ml-1">
                  {lastSaveTime.toLocaleTimeString('zh-CN', { 
                    hour12: false, 
                    hour: '2-digit', 
                    minute: '2-digit', 
                    second: '2-digit' 
                  })}
                </span>
              )}
            </span>
          </div>
        )

      case SaveState.error:
        return (
          <div className="flex items-center gap-2 px-3 py-2 bg-red-500/10 border border-red-500/30 rounded-md">
            <div className="w-3 h-3 rounded-full bg-red-500 flex items-center justify-center">
              <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-red-400 text-sm font-medium">保存失败</span>
              {error && (
                <span className="text-red-300 text-xs" title={error}>
                  {error.length > 20 ? `${error.substring(0, 20)}...` : error}
                </span>
              )}
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="text-red-300 hover:text-red-200 text-xs underline transition-colors"
                  title="点击重试保存"
                >
                  重试
                </button>
              )}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className={`save-status-indicator ${className}`}>
      {renderStatusContent()}
    </div>
  )
}

export default SaveStatusIndicator

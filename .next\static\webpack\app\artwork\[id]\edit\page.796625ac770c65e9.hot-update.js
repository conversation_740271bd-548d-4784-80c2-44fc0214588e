"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/DragEnhancer */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, fileId, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _dragEnhancerRef_current, _selectionManagerRef_current, _clipboardManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.destroy();\n        dragEnhancerRef.current = null;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_clipboardManagerRef_current = clipboardManagerRef.current) === null || _clipboardManagerRef_current === void 0 ? void 0 : _clipboardManagerRef_current.destroy();\n        clipboardManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 启用拖拽增强器，使用 SaveManager 统一保存机制\n            if (!readonly && enableDragEnhancement) {\n                var _dragConfig_persistence, _dragConfig_persistence1;\n                var _dragConfig_persistence_autoSave, _dragConfig_persistence_saveDelay;\n                const enhancedDragConfig = {\n                    ...dragConfig,\n                    persistence: {\n                        autoSave: (_dragConfig_persistence_autoSave = (_dragConfig_persistence = dragConfig.persistence) === null || _dragConfig_persistence === void 0 ? void 0 : _dragConfig_persistence.autoSave) !== null && _dragConfig_persistence_autoSave !== void 0 ? _dragConfig_persistence_autoSave : true,\n                        saveDelay: (_dragConfig_persistence_saveDelay = (_dragConfig_persistence1 = dragConfig.persistence) === null || _dragConfig_persistence1 === void 0 ? void 0 : _dragConfig_persistence1.saveDelay) !== null && _dragConfig_persistence_saveDelay !== void 0 ? _dragConfig_persistence_saveDelay : 1000\n                    }\n                };\n                dragEnhancerRef.current = new _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__.DragEnhancer(mindMapInstance, enhancedDragConfig);\n                dragEnhancerRef.current.initialize();\n                console.log(\"\\uD83C\\uDFAF DragEnhancer 已启用并集成 SaveManager\");\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            }\n        }), [\n        resizeCanvas,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 443,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 439,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 460,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 456,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 496,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 519,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 479,\n        columnNumber: 5\n    }, undefined);\n}, \"n9iMgar1C7HJsnxniI5k3ycy3s4=\")), \"n9iMgar1C7HJsnxniI5k3ycy3s4=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});
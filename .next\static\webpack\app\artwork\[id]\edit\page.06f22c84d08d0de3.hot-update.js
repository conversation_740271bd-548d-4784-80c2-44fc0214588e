"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 SaveManager 集成 - 替换原有的定时器管理\n    const saveManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(_services_SaveManager__WEBPACK_IMPORTED_MODULE_9__.SaveManager.getInstance());\n    const [saveState, setSaveState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 统一的定时器清理函数\n    const clearSaveTimer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (saveTimeoutRef.current) {\n            clearTimeout(saveTimeoutRef.current);\n            saveTimeoutRef.current = undefined;\n            console.log(\"\\uD83D\\uDD12 清除保存定时器\");\n        }\n    }, []);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 🔧 文件切换时清除所有定时器\n        clearSaveTimer();\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        // 🔧 视图切换时清除所有定时器\n        clearSaveTimer();\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        if (!settings.autoSave || !onContentChange) return;\n        clearSaveTimer();\n        saveTimeoutRef.current = setTimeout(()=>{\n            onContentChange(content);\n        }, settings.autoSaveDelay);\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        onContentChange,\n        clearSaveTimer\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 🔧 增强文件状态验证 - 捕获当前文件ID用于闭包验证\n        const currentFileId = file === null || file === void 0 ? void 0 : file.id;\n        const currentMindMapMode = isMindMapMode;\n        // 基础状态验证\n        if (!file || !currentMindMapMode || !currentFileId) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 🔧 延迟验证，防止异步竞态条件\n        setTimeout(()=>{\n            // 二次验证文件ID一致性，防止跨文件保存\n            if ((file === null || file === void 0 ? void 0 : file.id) !== currentFileId) {\n                console.log(\"⚠️ 文件已切换，跳过保存 (原文件ID:\", currentFileId, \"当前文件ID:\", file === null || file === void 0 ? void 0 : file.id, \")\");\n                return;\n            }\n            // 验证思维导图模式状态\n            if (!isMindMapMode) {\n                console.log(\"⚠️ 已退出思维导图模式，跳过保存\");\n                return;\n            }\n            // 将思维导图数据转换为Markdown格式并保存\n            if (data && onContentChange) {\n                const markdownContent = convertMindMapToMarkdown(data);\n                handleSave(markdownContent);\n                console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存 (文件ID:\", currentFileId, \")\");\n            }\n        }, 0) // 使用微任务延迟，确保状态更新完成\n        ;\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        onContentChange,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearSaveTimer();\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, [\n        clearSaveTimer\n    ]);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 654,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 649,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 648,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 717,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 844,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 860,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 855,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 665,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 873,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 876,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 891,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 902,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 890,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 919,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 932,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 958,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 957,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 975,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 870,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 869,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1000,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1004,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 999,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 998,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1010,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1011,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1012,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1013,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1009,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1008,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1022,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 996,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1037,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1038,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1040,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1035,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1034,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1046,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1048,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1057,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1049,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1065,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1045,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1044,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1075,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1092,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1093,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1094,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1091,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1090,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1032,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1113,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1111,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1110,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1109,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1102,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1101,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 663,\n        columnNumber: 5\n    }, undefined);\n}, \"G+/LVGSPwTjGZWGjX9kDLf7qrZA=\")), \"G+/LVGSPwTjGZWGjX9kDLf7qrZA=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/SaveManager.ts":
/*!*************************************!*\
  !*** ./src/services/SaveManager.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SaveManager: function() { return /* binding */ SaveManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\n * SaveManager - 统一保存管理器\n * 负责管理所有文件的保存操作，解决多个独立定时器系统导致的竞态条件问题\n * \n * 功能特性：\n * - 统一的防抖机制\n * - 文件状态管理和锁定\n * - 保存队列处理\n * - 错误处理和重试机制\n * - 保存状态回调通知\n */ \nclass SaveManager {\n    /**\n   * 获取 SaveManager 单例实例\n   */ static getInstance() {\n        if (!SaveManager.instance) {\n            SaveManager.instance = new SaveManager();\n        }\n        return SaveManager.instance;\n    }\n    /**\n   * 设置保存执行回调\n   */ setSaveExecuteCallback(callback) {\n        this.saveExecuteCallback = callback;\n    }\n    /**\n   * 添加保存状态变更监听器\n   */ addStateChangeListener(callback) {\n        this.stateChangeCallbacks.add(callback);\n    }\n    /**\n   * 移除保存状态变更监听器\n   */ removeStateChangeListener(callback) {\n        this.stateChangeCallbacks.delete(callback);\n    }\n    /**\n   * 通知保存状态变更\n   */ notifyStateChange(fileId, state, error) {\n        this.stateChangeCallbacks.forEach((callback)=>{\n            try {\n                callback(fileId, state, error);\n            } catch (err) {\n                console.error(\"❌ 保存状态回调执行失败:\", err);\n            }\n        });\n    }\n    /**\n   * 获取文件保存状态\n   */ getFileSaveState(fileId) {\n        return this.fileSaveStates.get(fileId) || null;\n    }\n    /**\n   * 设置当前活动文件\n   */ setCurrentFile(fileId) {\n        this.currentFileId = fileId;\n        console.log(\"\\uD83D\\uDCC1 设置当前活动文件:\", fileId);\n    }\n    /**\n   * 获取当前活动文件ID\n   */ getCurrentFileId() {\n        return this.currentFileId;\n    }\n    /**\n   * 调度自动保存\n   */ scheduleAutoSave(fileId, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const mergedOptions = {\n            ...this.defaultOptions,\n            ...options\n        };\n        // 获取或创建文件保存状态\n        let fileState = this.fileSaveStates.get(fileId);\n        if (!fileState) {\n            fileState = {\n                fileId,\n                state: _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle,\n                lastSaveTime: 0,\n                retryCount: 0\n            };\n            this.fileSaveStates.set(fileId, fileState);\n        }\n        // 如果当前正在保存，跳过\n        if (fileState.state === _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saving) {\n            console.log(\"⏳ 文件正在保存中，跳过新的保存请求:\", fileId);\n            return;\n        }\n        // 清除现有定时器\n        if (fileState.timer) {\n            clearTimeout(fileState.timer);\n        }\n        // 更新状态\n        fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.pending;\n        fileState.pendingData = data;\n        fileState.options = mergedOptions;\n        this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.pending);\n        if (mergedOptions.immediate) {\n            // 立即执行保存\n            this.executeSave(fileId);\n        } else {\n            // 设置防抖定时器\n            fileState.timer = setTimeout(()=>{\n                this.executeSave(fileId);\n            }, mergedOptions.debounceDelay);\n        }\n        console.log(\"\\uD83D\\uDCBE 调度自动保存 [\".concat(mergedOptions.saveType, \"]:\"), fileId, \"延迟: \".concat(mergedOptions.immediate ? \"立即\" : mergedOptions.debounceDelay + \"ms\"));\n    }\n    /**\n   * 强制立即保存\n   */ async forceSave(fileId) {\n        console.log(\"\\uD83D\\uDE80 强制立即保存:\", fileId);\n        const fileState = this.fileSaveStates.get(fileId);\n        if (!fileState || !fileState.pendingData) {\n            console.log(\"\\uD83D\\uDCDD 没有待保存的数据:\", fileId);\n            return true;\n        }\n        // 清除定时器\n        if (fileState.timer) {\n            clearTimeout(fileState.timer);\n            fileState.timer = undefined;\n        }\n        return await this.executeSave(fileId);\n    }\n    /**\n   * 执行保存操作\n   */ async executeSave(fileId) {\n        const fileState = this.fileSaveStates.get(fileId);\n        if (!fileState || !fileState.pendingData) {\n            console.warn(\"⚠️ 执行保存时找不到文件状态或数据:\", fileId);\n            return false;\n        }\n        // 验证文件是否仍然是当前活动文件（防止文件切换后的延迟保存）\n        if (this.currentFileId && this.currentFileId !== fileId) {\n            console.log(\"\\uD83D\\uDD12 文件已切换，跳过保存:\", fileId, \"当前文件:\", this.currentFileId);\n            fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle;\n            this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle);\n            return false;\n        }\n        try {\n            // 更新状态为保存中\n            fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saving;\n            fileState.error = undefined;\n            this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saving);\n            console.log(\"\\uD83D\\uDCBE 开始执行保存:\", fileId);\n            // 执行保存回调\n            if (!this.saveExecuteCallback) {\n                throw new Error(\"保存执行回调未设置\");\n            }\n            const success = await this.saveExecuteCallback(fileId, fileState.pendingData, fileState.options);\n            if (success) {\n                // 保存成功\n                fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saved;\n                fileState.lastSaveTime = Date.now();\n                fileState.retryCount = 0;\n                fileState.pendingData = undefined;\n                this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saved);\n                console.log(\"✅ 保存成功:\", fileId);\n                // 3秒后自动重置状态为空闲\n                setTimeout(()=>{\n                    if (fileState && fileState.state === _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.saved) {\n                        fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle;\n                        this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.idle);\n                    }\n                }, 3000);\n                return true;\n            } else {\n                throw new Error(\"保存操作返回失败\");\n            }\n        } catch (error) {\n            var _fileState_options;\n            // 保存失败\n            const errorMessage = error instanceof Error ? error.message : \"未知错误\";\n            fileState.state = _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.error;\n            fileState.error = errorMessage;\n            fileState.retryCount++;\n            this.notifyStateChange(fileId, _types__WEBPACK_IMPORTED_MODULE_0__.SaveState.error, errorMessage);\n            console.error(\"❌ 保存失败:\", fileId, errorMessage);\n            // 如果还有重试次数，安排重试\n            if (fileState.retryCount < (((_fileState_options = fileState.options) === null || _fileState_options === void 0 ? void 0 : _fileState_options.retryCount) || this.defaultOptions.retryCount)) {\n                var _fileState_options1;\n                console.log(\"\\uD83D\\uDD04 安排重试保存 (\".concat(fileState.retryCount, \"/\").concat(((_fileState_options1 = fileState.options) === null || _fileState_options1 === void 0 ? void 0 : _fileState_options1.retryCount) || this.defaultOptions.retryCount, \"):\"), fileId);\n                setTimeout(()=>{\n                    this.executeSave(fileId);\n                }, 2000 * fileState.retryCount); // 递增延迟重试\n            }\n            return false;\n        }\n    }\n    /**\n   * 文件切换处理\n   */ async switchFile(fromFileId, toFileId) {\n        console.log(\"\\uD83D\\uDD04 文件切换:\", fromFileId, \"->\", toFileId);\n        // 强制保存当前文件\n        if (fromFileId) {\n            await this.forceSave(fromFileId);\n        }\n        // 设置新的当前文件\n        this.setCurrentFile(toFileId);\n    }\n    /**\n   * 清理文件保存状态\n   */ clearFileState(fileId) {\n        const fileState = this.fileSaveStates.get(fileId);\n        if (fileState) {\n            // 清除定时器\n            if (fileState.timer) {\n                clearTimeout(fileState.timer);\n            }\n            // 移除状态\n            this.fileSaveStates.delete(fileId);\n            console.log(\"\\uD83D\\uDDD1️ 清理文件保存状态:\", fileId);\n        }\n    }\n    /**\n   * 获取所有文件的保存状态\n   */ getAllFileSaveStates() {\n        return new Map(this.fileSaveStates);\n    }\n    /**\n   * 销毁管理器（清理所有资源）\n   */ destroy() {\n        // 清除所有定时器\n        this.fileSaveStates.forEach((fileState)=>{\n            if (fileState.timer) {\n                clearTimeout(fileState.timer);\n            }\n        });\n        // 清理状态\n        this.fileSaveStates.clear();\n        this.stateChangeCallbacks.clear();\n        this.saveExecuteCallback = null;\n        this.currentFileId = null;\n        console.log(\"\\uD83D\\uDDD1️ SaveManager 已销毁\");\n    }\n    constructor(){\n        /** 文件保存状态映射 */ this.fileSaveStates = new Map();\n        /** 当前活动文件ID */ this.currentFileId = null;\n        /** 保存状态变更回调 */ this.stateChangeCallbacks = new Set();\n        /** 保存执行回调 */ this.saveExecuteCallback = null;\n        /** 默认保存选项 */ this.defaultOptions = {\n            debounceDelay: 1000,\n            saveType: \"editor\",\n            immediate: false,\n            retryCount: 3\n        };\n    // 私有构造函数，确保单例模式\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/SaveManager.ts\n"));

/***/ })

});
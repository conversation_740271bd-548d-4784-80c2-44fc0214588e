"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts":
/*!*********************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/SelectionManager.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionManager: function() { return /* binding */ SelectionManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * SelectionManager - 框选管理器\r\n * 负责右键长按框选功能的实现\r\n * \r\n * 职责：\r\n * - 右键长按检测\r\n * - 框选区域渲染\r\n * - 多节点选择逻辑\r\n * \r\n * 设计原则：\r\n * - 简化状态：只有 idle 和 selecting 两个状态\r\n * - 官方API优先：使用官方的坐标转换和节点选择API\r\n */ \nclass SelectionManager {\n    /**\r\n   * 初始化框选管理器\r\n   */ initialize() {\n        this.createSelectionBox();\n        this.bindEvents();\n        console.log(\"✅ 框选管理器初始化完成\");\n    }\n    /**\r\n   * 创建选择框DOM元素\r\n   */ createSelectionBox() {\n        this.selectionBox = document.createElement(\"div\");\n        this.selectionBox.className = \"mindmap-selection-box\";\n        const { selectionBoxStyle } = this.config;\n        this.selectionBox.style.cssText = \"\\n      position: fixed;\\n      pointer-events: none;\\n      z-index: 10000;\\n      border: \".concat(selectionBoxStyle.strokeWidth, \"px solid \").concat(selectionBoxStyle.strokeColor, \";\\n      background-color: \").concat(selectionBoxStyle.fillColor, \";\\n      opacity: \").concat(selectionBoxStyle.fillOpacity, \";\\n      border-radius: 4px;\\n      display: none;\\n      box-sizing: border-box;\\n      box-shadow: 0 2px 8px rgba(143, 188, 143, 0.3);\\n    \");\n        // 直接插入到body，避免容器层级问题\n        document.body.appendChild(this.selectionBox);\n    }\n    /**\r\n   * 绑定事件监听\r\n   */ bindEvents() {\n        // 不使用capture模式，让节点事件优先处理\n        this.container.addEventListener(\"mousedown\", this.boundHandlers.mousedown, {\n            passive: false\n        });\n        this.container.addEventListener(\"contextmenu\", this.boundHandlers.contextmenu, {\n            passive: false\n        });\n        // 全局鼠标事件\n        document.addEventListener(\"mousemove\", this.boundHandlers.mousemove, {\n            passive: false\n        });\n        document.addEventListener(\"mouseup\", this.boundHandlers.mouseup, {\n            passive: false\n        });\n    }\n    /**\r\n   * 解除事件监听\r\n   */ unbindEvents() {\n        this.container.removeEventListener(\"mousedown\", this.boundHandlers.mousedown, true);\n        this.container.removeEventListener(\"contextmenu\", this.boundHandlers.contextmenu, true);\n        document.removeEventListener(\"mousemove\", this.boundHandlers.mousemove);\n        document.removeEventListener(\"mouseup\", this.boundHandlers.mouseup);\n    }\n    /**\r\n   * 处理鼠标按下事件\r\n   */ handleMouseDown(event) {\n        // 只处理右键\n        if (event.button !== 2) return;\n        // 检查是否点击在节点上\n        const target = event.target;\n        if (this.isClickOnNode(target)) {\n            // 如果点击在节点上，不处理框选，让节点的右键菜单正常工作\n            console.log(\"\\uD83D\\uDDB1️ 右键点击节点，跳过框选处理\");\n            return;\n        }\n        // 阻止默认右键菜单\n        event.preventDefault();\n        // 转换为画布坐标\n        const canvasPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);\n        this.startPoint = canvasPoint;\n        // 设置长按检测定时器\n        this.longPressTimer = setTimeout(()=>{\n            this.startSelection();\n        }, this.config.longPressDelay);\n        console.log(\"\\uD83D\\uDDB1️ 右键按下空白区域，开始检测长按\");\n    }\n    /**\r\n   * 处理鼠标移动事件\r\n   */ handleMouseMove(event) {\n        if (!this.startPoint) return;\n        const currentPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);\n        this.currentPoint = currentPoint;\n        // 如果还在检测阶段，检查移动距离\n        if (!this.isSelecting && this.longPressTimer) {\n            const distance = this.calculateDistance(this.startPoint, currentPoint);\n            if (distance > this.config.moveThreshold) {\n                // 移动距离超过阈值，立即开始框选\n                this.clearLongPressTimer();\n                this.startSelection();\n            }\n        }\n        // 如果正在框选，更新选择框\n        if (this.isSelecting) {\n            this.updateSelectionBox();\n        }\n    }\n    /**\r\n   * 处理鼠标释放事件\r\n   */ handleMouseUp(event) {\n        if (event.button !== 2) return;\n        this.clearLongPressTimer();\n        if (this.isSelecting) {\n            this.completeSelection();\n        } else {\n            // 短按，重置状态\n            this.resetSelection();\n        }\n    }\n    /**\r\n   * 处理右键菜单事件\r\n   */ handleContextMenu(event) {\n        // 只有在正在框选时才阻止右键菜单\n        if (this.isSelecting) {\n            event.stopPropagation();\n            event.preventDefault();\n            console.log(\"\\uD83D\\uDEAB 框选状态中，阻止右键菜单\");\n        } else {\n            // 检查是否点击在节点上\n            const target = event.target;\n            if (!this.isClickOnNode(target)) {\n                // 如果不是点击节点，阻止默认右键菜单\n                event.preventDefault();\n                console.log(\"\\uD83D\\uDEAB 空白区域右键，阻止默认菜单\");\n            }\n        }\n    }\n    /**\r\n   * 开始框选\r\n   */ startSelection() {\n        this.isSelecting = true;\n        this.showSelectionBox();\n        console.log(\"\\uD83D\\uDCE6 开始框选模式\");\n    }\n    /**\r\n   * 显示选择框\r\n   */ showSelectionBox() {\n        if (this.selectionBox) {\n            this.selectionBox.style.display = \"block\";\n            this.updateSelectionBox();\n        }\n    }\n    /**\r\n   * 更新选择框位置和大小\r\n   */ updateSelectionBox() {\n        if (!this.selectionBox || !this.startPoint || !this.currentPoint) return;\n        // 转换为屏幕坐标\n        const containerRect = this.container.getBoundingClientRect();\n        const startScreen = {\n            x: this.startPoint.x + containerRect.left,\n            y: this.startPoint.y + containerRect.top\n        };\n        const currentScreen = {\n            x: this.currentPoint.x + containerRect.left,\n            y: this.currentPoint.y + containerRect.top\n        };\n        const left = Math.min(startScreen.x, currentScreen.x);\n        const top = Math.min(startScreen.y, currentScreen.y);\n        const width = Math.abs(currentScreen.x - startScreen.x);\n        const height = Math.abs(currentScreen.y - startScreen.y);\n        this.selectionBox.style.left = \"\".concat(left, \"px\");\n        this.selectionBox.style.top = \"\".concat(top, \"px\");\n        this.selectionBox.style.width = \"\".concat(width, \"px\");\n        this.selectionBox.style.height = \"\".concat(height, \"px\");\n    }\n    /**\r\n   * 完成框选\r\n   */ completeSelection() {\n        if (!this.startPoint || !this.currentPoint) {\n            this.resetSelection();\n            return;\n        }\n        // 查找选择范围内的节点\n        const selectedNodes = this.findNodesInSelection();\n        if (selectedNodes.length > 0) {\n            var // 回调通知\n            _this_onSelectionComplete, _this;\n            // 使用官方API激活节点\n            this.activateNodes(selectedNodes);\n            (_this_onSelectionComplete = (_this = this).onSelectionComplete) === null || _this_onSelectionComplete === void 0 ? void 0 : _this_onSelectionComplete.call(_this, selectedNodes);\n            console.log(\"✅ 框选完成，选中 \".concat(selectedNodes.length, \" 个节点\"));\n        } else {\n            console.log(\"ℹ️ 框选区域内没有节点\");\n        }\n        // 延迟隐藏选择框，提供视觉反馈\n        setTimeout(()=>{\n            this.resetSelection();\n        }, 150);\n    }\n    /**\r\n   * 查找选择范围内的节点\r\n   */ findNodesInSelection() {\n        var _this_mindMapInstance_renderer;\n        if (!this.startPoint || !this.currentPoint || !((_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.root)) {\n            return [];\n        }\n        const selectedNodes = [];\n        const selectionRect = this.getSelectionRect();\n        // 递归遍历节点树\n        this.traverseNodes(this.mindMapInstance.renderer.root, (node)=>{\n            if (this.isNodeInSelection(node, selectionRect)) {\n                selectedNodes.push(node);\n            }\n        });\n        return selectedNodes;\n    }\n    /**\r\n   * 递归遍历节点\r\n   */ traverseNodes(node, callback) {\n        if (!node) return;\n        callback(node);\n        if (node.children && Array.isArray(node.children)) {\n            node.children.forEach((child)=>this.traverseNodes(child, callback));\n        }\n    }\n    /**\r\n   * 检查节点是否在选择范围内\r\n   */ isNodeInSelection(node, selectionRect) {\n        try {\n            var _node_getRectInSvg;\n            // 使用官方API获取节点位置\n            const nodeRect = (_node_getRectInSvg = node.getRectInSvg) === null || _node_getRectInSvg === void 0 ? void 0 : _node_getRectInSvg.call(node);\n            if (!nodeRect) return false;\n            const { left, top, width, height } = nodeRect;\n            const nodeRight = left + width;\n            const nodeBottom = top + height;\n            // 矩形相交检测\n            return !(nodeRight < selectionRect.left || left > selectionRect.right || nodeBottom < selectionRect.top || top > selectionRect.bottom);\n        } catch (error) {\n            console.warn(\"❌ 节点位置检测失败:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * 获取选择矩形\r\n   */ getSelectionRect() {\n        if (!this.startPoint || !this.currentPoint) {\n            return {\n                left: 0,\n                top: 0,\n                right: 0,\n                bottom: 0\n            };\n        }\n        const left = Math.min(this.startPoint.x, this.currentPoint.x);\n        const top = Math.min(this.startPoint.y, this.currentPoint.y);\n        const right = Math.max(this.startPoint.x, this.currentPoint.x);\n        const bottom = Math.max(this.startPoint.y, this.currentPoint.y);\n        return {\n            left,\n            top,\n            right,\n            bottom\n        };\n    }\n    /**\r\n   * 激活选中的节点\r\n   */ activateNodes(nodes) {\n        try {\n            var _this_mindMapInstance_renderer;\n            // 使用官方API激活多个节点\n            if ((_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.activeMultiNode) {\n                this.mindMapInstance.renderer.activeMultiNode(nodes);\n            } else {\n                var // 降级方案\n                _this_mindMapInstance_renderer1;\n                (_this_mindMapInstance_renderer1 = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer1 === void 0 ? void 0 : _this_mindMapInstance_renderer1.clearActiveNodeList();\n                nodes.forEach((node)=>{\n                    var _this_mindMapInstance_renderer;\n                    (_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.addNodeToActiveList(node);\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 激活节点失败:\", error);\n        }\n    }\n    /**\r\n   * 检查是否点击在节点上\r\n   */ isClickOnNode(target) {\n        // 检查点击目标是否是节点或节点的子元素\n        // SimpleMindMap的节点通常有特定的类名或属性\n        let element = target;\n        while(element && element !== this.container){\n            // 检查是否是SVG节点元素\n            if (element.tagName === \"g\" && element.getAttribute(\"data-nodeid\")) {\n                return true;\n            }\n            // 检查是否有节点相关的类名\n            if (element.classList && (element.classList.contains(\"smm-node\") || element.classList.contains(\"node\") || element.getAttribute(\"data-node\") !== null)) {\n                return true;\n            }\n            element = element.parentElement;\n        }\n        return false;\n    }\n    /**\r\n   * 计算两点间距离\r\n   */ calculateDistance(p1, p2) {\n        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n    }\n    /**\r\n   * 清除长按定时器\r\n   */ clearLongPressTimer() {\n        if (this.longPressTimer) {\n            clearTimeout(this.longPressTimer);\n            this.longPressTimer = null;\n        }\n    }\n    /**\r\n   * 重置选择状态\r\n   */ resetSelection() {\n        this.isSelecting = false;\n        this.startPoint = null;\n        this.currentPoint = null;\n        this.clearLongPressTimer();\n        if (this.selectionBox) {\n            this.selectionBox.style.display = \"none\";\n        }\n    }\n    /**\r\n   * 更新配置\r\n   */ updateConfig(config) {\n        this.config = {\n            ...this.config,\n            ...config\n        };\n    }\n    /**\r\n   * 检查是否正在框选\r\n   */ isActive() {\n        return this.isSelecting;\n    }\n    /**\r\n   * 销毁框选管理器\r\n   */ destroy() {\n        this.resetSelection();\n        this.unbindEvents();\n        // 🔧 完全跳过DOM清理，避免与React冲突\n        if (this.selectionBox) {\n            console.log(\"\\uD83D\\uDD12 跳过selectionBox.remove()调用，避免DOM冲突\");\n            this.selectionBox = null;\n        }\n        console.log(\"✅ 框选管理器销毁完成\");\n    }\n    constructor(mindMapInstance, container, onSelectionComplete){\n        // 简化状态：只有两种状态\n        this.isSelecting = false;\n        // 选择过程数据\n        this.startPoint = null;\n        this.currentPoint = null;\n        this.longPressTimer = null;\n        // DOM元素\n        this.selectionBox = null;\n        // 事件处理器绑定\n        this.boundHandlers = {\n            mousedown: this.handleMouseDown.bind(this),\n            mousemove: this.handleMouseMove.bind(this),\n            mouseup: this.handleMouseUp.bind(this),\n            contextmenu: this.handleContextMenu.bind(this)\n        };\n        this.mindMapInstance = mindMapInstance;\n        this.container = container;\n        this.config = _types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_SELECTION_CONFIG;\n        this.onSelectionComplete = onSelectionComplete;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var lib0_mutex__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/mutex */ \"(app-pages-browser)/./node_modules/lib0/mutex.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n *\r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \n\nclass CoreManager {\n    /**\r\n   * 生成唯一标识符\r\n   */ generateUniqueId() {\n        return \"mindmap_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    /**\r\n   * 生成数据版本号\r\n   */ generateDataVersion(data) {\n        const dataString = JSON.stringify(data);\n        return \"v_\".concat(Date.now(), \"_\").concat(this.hashCode(dataString));\n    }\n    /**\r\n   * 简单哈希函数\r\n   */ hashCode(str) {\n        let hash = 0;\n        if (str.length === 0) return hash.toString();\n        for(let i = 0; i < str.length; i++){\n            const char = str.charCodeAt(i);\n            hash = (hash << 5) - hash + char;\n            hash = hash & hash; // Convert to 32bit integer\n        }\n        return Math.abs(hash).toString(36);\n    }\n    /**\r\n   * 为数据添加元数据\r\n   */ addDataMetadata(data) {\n        const dataVersion = this.generateDataVersion(data);\n        const enhancedData = {\n            ...data,\n            _metadata: {\n                dataVersion,\n                instanceId: this.instanceId,\n                timestamp: Date.now()\n            }\n        };\n        this.currentDataVersion = dataVersion;\n        return enhancedData;\n    }\n    /**\r\n   * 验证数据一致性\r\n   */ validateDataConsistency(data) {\n        if (!data._metadata) {\n            // 没有元数据的数据被认为是有效的（向后兼容）\n            return true;\n        }\n        // 检查实例ID是否匹配\n        if (data._metadata.instanceId !== this.instanceId) {\n            console.warn(\"⚠️ 数据实例ID不匹配，可能来自不同的思维导图实例\");\n            return false;\n        }\n        return true;\n    }\n    /**\r\n   * 移除数据元数据（用于传递给SimpleMindMap）\r\n   */ removeDataMetadata(data) {\n        const { _metadata, ...cleanData } = data;\n        return cleanData;\n    }\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        return new Promise((resolve, reject)=>{\n            this.initMutex(async ()=>{\n                try {\n                    // 动态导入SimpleMindMap完整版（包含所有插件，特别是Drag插件）\n                    const { default: SimpleMindMap } = await Promise.all(/*! import() */[__webpack_require__.e(\"css-node_modules_quill_dist_quill_snow_css\"), __webpack_require__.e(\"_app-pages-browser_node_modules_simple-mind-map_full_js\")]).then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map/full.js */ \"(app-pages-browser)/./node_modules/simple-mind-map/full.js\"));\n                    // 清理现有实例（使用强制销毁）\n                    if (this.mindMapInstance) {\n                        await this.forceDestroy();\n                    }\n                    // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器\n                    const finalConfig = {\n                        ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                        ...this.config.config,\n                        el: this.config.container,\n                        data: this.config.data,\n                        readonly: this.config.readonly\n                    };\n                    // 创建实例\n                    this.mindMapInstance = new SimpleMindMap(finalConfig);\n                    this.isInitialized = true;\n                    resolve(this.mindMapInstance);\n                } catch (error) {\n                    console.error(\"❌ SimpleMindMap初始化失败:\", error);\n                    const errorMessage = \"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\");\n                    reject(new Error(errorMessage));\n                }\n            });\n        });\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据（带版本控制）\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            var _enhancedData__metadata;\n            // 添加数据元数据\n            const enhancedData = this.addDataMetadata(data);\n            // 验证数据一致性\n            if (!this.validateDataConsistency(enhancedData)) {\n                console.warn(\"⚠️ 数据版本不匹配，跳过设置\");\n                return;\n            }\n            // 移除元数据后传递给SimpleMindMap\n            const cleanData = this.removeDataMetadata(enhancedData);\n            this.mindMapInstance.setData(cleanData);\n            console.log(\"✅ 数据设置成功，版本:\", (_enhancedData__metadata = enhancedData._metadata) === null || _enhancedData__metadata === void 0 ? void 0 : _enhancedData__metadata.dataVersion);\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 容器尺寸变化后调整画布\r\n   */ resize() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 先更新容器位置和尺寸信息\n            this.mindMapInstance.getElRectInfo();\n            // 然后调整画布尺寸\n            this.mindMapInstance.resize();\n        } catch (error) {\n            console.error(\"❌ 画布尺寸调整失败:\", error);\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 强制销毁实例（完整清理版本）\r\n   */ async forceDestroy() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 解绑所有事件监听器\n            if (this.mindMapInstance) {\n                try {\n                    // 尝试解绑常见的事件监听器\n                    const commonEvents = [\n                        \"data_change\",\n                        \"node_active\",\n                        \"node_tree_render_end\",\n                        \"set_data\",\n                        \"expand_btn_click\",\n                        \"node_click\",\n                        \"draw_click\",\n                        \"svg_mousedown\",\n                        \"mousedown\",\n                        \"mousemove\",\n                        \"mouseup\",\n                        \"contextmenu\"\n                    ];\n                    commonEvents.forEach((eventName)=>{\n                        try {\n                            // 使用any类型避免类型检查问题\n                            const instance = this.mindMapInstance;\n                            if (instance && typeof instance.off === \"function\") {\n                                instance.off(eventName, ()=>{});\n                            }\n                        } catch (error) {\n                        // 忽略解绑失败的事件\n                        }\n                    });\n                } catch (error) {\n                    console.warn(\"⚠️ 事件解绑失败:\", error);\n                }\n            }\n            // 🔧 完全跳过SimpleMindMap的destroy调用，避免DOM冲突\n            // 让React和浏览器的垃圾回收机制处理清理\n            console.log(\"\\uD83D\\uDD12 跳过SimpleMindMap.destroy()调用，避免DOM冲突\");\n            // 🔧 跳过DOM清理，让React处理DOM管理\n            // this.clearDOMReferences();\n            // 等待销毁完成\n            await this.waitForDestruction();\n            console.log(\"✅ SimpleMindMap实例强制销毁完成\");\n        } catch (error) {\n            console.error(\"❌ 强制销毁SimpleMindMap实例失败:\", error);\n            throw error;\n        } finally{\n            this.mindMapInstance = null;\n            this.isInitialized = false;\n        }\n    }\n    /**\r\n   * 清理DOM引用\r\n   */ clearDOMReferences() {\n        try {\n            // 🔧 温和的DOM清理，避免与React冲突\n            if (this.config.container && this.config.container.parentNode) {\n                const container = this.config.container;\n                // 只清理SimpleMindMap创建的元素，避免移除React管理的DOM\n                const simpleMindMapElements = container.querySelectorAll(\".smm-container, .smm-svg, canvas\");\n                simpleMindMapElements.forEach((element)=>{\n                    try {\n                        if (element.parentNode === container) {\n                            container.removeChild(element);\n                        }\n                    } catch (e) {\n                    // 忽略已被移除的元素\n                    }\n                });\n            }\n        } catch (error) {\n            console.warn(\"⚠️ DOM引用清理失败:\", error);\n        }\n    }\n    /**\r\n   * 等待销毁完成\r\n   */ async waitForDestruction() {\n        // 给予适当的等待时间确保异步销毁完成\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 100);\n        });\n    }\n    /**\r\n   * 销毁实例（保持向后兼容）\r\n   */ destroy() {\n        if (this.mindMapInstance) {\n            try {\n                this.mindMapInstance.destroy();\n                console.log(\"✅ SimpleMindMap实例销毁完成\");\n            } catch (error) {\n                console.error(\"❌ 销毁SimpleMindMap实例失败:\", error);\n            }\n        }\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.initMutex = (0,lib0_mutex__WEBPACK_IMPORTED_MODULE_1__.createMutex)();\n        // 数据版本控制\n        this.currentDataVersion = null;\n        this.instanceId = this.generateUniqueId();\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ })

});
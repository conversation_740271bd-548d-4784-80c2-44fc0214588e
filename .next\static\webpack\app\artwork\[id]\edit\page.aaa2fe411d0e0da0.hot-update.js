"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/EventManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventManager: function() { return /* binding */ EventManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * EventManager - 事件管理器\r\n * 负责SimpleMindMap事件的统一管理和分发\r\n * \r\n * 职责：\r\n * - 监听SimpleMindMap官方事件\r\n * - 事件处理和回调分发\r\n * - 自定义事件逻辑\r\n */ \nclass EventManager {\n    /**\r\n   * 实现ICleanable接口 - 是否已销毁\r\n   */ get isDestroyed() {\n        return this._cleanupState === _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYED;\n    }\n    /**\r\n   * 实现ICleanable接口 - 当前清理状态\r\n   */ get cleanupState() {\n        return this._cleanupState;\n    }\n    /**\r\n   * 初始化事件监听\r\n   */ initialize() {\n        this.setupNodeEvents();\n        this.setupDataEvents();\n        this.setupRenderEvents();\n        this.setupCanvasEvents();\n        this.isInitialized = true;\n        console.log(\"✅ 事件管理器初始化完成\");\n    }\n    /**\r\n   * 设置节点相关事件\r\n   */ setupNodeEvents() {\n        // 节点点击事件\n        const nodeClickHandler = (node)=>{\n            var _this_config_onNodeClick, _this_config;\n            console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node);\n            (_this_config_onNodeClick = (_this_config = this.config).onNodeClick) === null || _this_config_onNodeClick === void 0 ? void 0 : _this_config_onNodeClick.call(_this_config, node);\n        };\n        // 节点激活事件\n        const nodeActiveHandler = (node, activeNodeList)=>{\n            console.log(\"✨ 节点激活:\", {\n                node,\n                activeNodeList\n            });\n        // 可以在这里添加节点激活的自定义逻辑\n        };\n        // 节点右键菜单事件\n        const nodeContextMenuHandler = (event, node)=>{\n            console.log(\"\\uD83D\\uDDB1️ 节点右键菜单:\", {\n                event,\n                node\n            });\n            // 阻止默认右键菜单\n            event.preventDefault();\n            event.stopPropagation();\n            // 计算菜单位置并显示\n            const position = this.calculateMenuPosition(event);\n            this.showContextMenu({\n                position,\n                node\n            });\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_click\", nodeClickHandler);\n        this.mindMapInstance.on(\"node_active\", nodeActiveHandler);\n        this.mindMapInstance.on(\"node_contextmenu\", nodeContextMenuHandler);\n        // 存储事件处理器用于清理\n        this.eventListeners.set(\"node_click\", nodeClickHandler);\n        this.eventListeners.set(\"node_active\", nodeActiveHandler);\n        this.eventListeners.set(\"node_contextmenu\", nodeContextMenuHandler);\n    }\n    /**\r\n   * 设置数据相关事件\r\n   */ setupDataEvents() {\n        // 数据变更事件\n        const dataChangeHandler = (newData)=>{\n            var _this_config_onDataChange, _this_config;\n            console.log(\"\\uD83D\\uDCCA 数据变更:\", newData);\n            (_this_config_onDataChange = (_this_config = this.config).onDataChange) === null || _this_config_onDataChange === void 0 ? void 0 : _this_config_onDataChange.call(_this_config, newData);\n        };\n        // 数据设置前事件\n        const beforeSetDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 准备设置数据:\", data);\n        };\n        // 数据设置后事件\n        const setDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 数据设置完成:\", data);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"data_change\", dataChangeHandler);\n        this.mindMapInstance.on(\"before_set_data\", beforeSetDataHandler);\n        this.mindMapInstance.on(\"set_data\", setDataHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"data_change\", dataChangeHandler);\n        this.eventListeners.set(\"before_set_data\", beforeSetDataHandler);\n        this.eventListeners.set(\"set_data\", setDataHandler);\n    }\n    /**\r\n   * 设置渲染相关事件\r\n   */ setupRenderEvents() {\n        // 节点树渲染结束事件\n        const nodeTreeRenderEndHandler = ()=>{\n            var _this_config_onRenderComplete, _this_config;\n            console.log(\"\\uD83C\\uDFA8 节点树渲染完成\");\n            // 完全不在EventManager中处理视图适应，交给组件级别控制\n            // 这样可以确保用户的视图位置永远不会被意外重置\n            console.log(\"✅ 渲染完成，保持当前视图位置\");\n            (_this_config_onRenderComplete = (_this_config = this.config).onRenderComplete) === null || _this_config_onRenderComplete === void 0 ? void 0 : _this_config_onRenderComplete.call(_this_config);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n    }\n    /**\r\n   * 设置画布相关事件\r\n   */ setupCanvasEvents() {\n        // 画布点击事件\n        const drawClickHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布点击\");\n            // 隐藏右键菜单\n            if (this.contextMenuVisible) {\n                this.hideContextMenu();\n            }\n        };\n        // 画布拖拽事件\n        const drawDragHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布拖拽\");\n        };\n        // 缩放事件\n        const scaleHandler = (scale)=>{\n            console.log(\"\\uD83D\\uDD0D 缩放变化:\", scale);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"draw_click\", drawClickHandler);\n        this.mindMapInstance.on(\"view_data_change\", drawDragHandler);\n        this.mindMapInstance.on(\"scale\", scaleHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"draw_click\", drawClickHandler);\n        this.eventListeners.set(\"view_data_change\", drawDragHandler);\n        this.eventListeners.set(\"scale\", scaleHandler);\n    }\n    /**\r\n   * 手动触发事件\r\n   */ emit(event) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.emit(event, ...args);\n        } catch (error) {\n            console.error(\"❌ 触发事件失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 添加自定义事件监听器\r\n   */ addEventListener(event, handler) {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.on(event, handler);\n            this.eventListeners.set(event, handler);\n            console.log(\"✅ 添加事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 添加事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 移除事件监听器\r\n   */ removeEventListener(event) {\n        const handler = this.eventListeners.get(event);\n        if (!handler || !this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.off(event, handler);\n            this.eventListeners.delete(event);\n            console.log(\"✅ 移除事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 获取当前激活的节点列表\r\n   */ getActiveNodes() {\n        var _this_mindMapInstance;\n        if (!((_this_mindMapInstance = this.mindMapInstance) === null || _this_mindMapInstance === void 0 ? void 0 : _this_mindMapInstance.renderer)) return [];\n        try {\n            return this.mindMapInstance.renderer.activeNodeList || [];\n        } catch (error) {\n            console.error(\"❌ 获取激活节点失败:\", error);\n            return [];\n        }\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 计算菜单位置，确保不超出视口\r\n   */ calculateMenuPosition(event) {\n        const viewportWidth = window.innerWidth;\n        const viewportHeight = window.innerHeight;\n        let x = event.clientX;\n        let y = event.clientY;\n        // 预估菜单尺寸（实际尺寸会在组件中调整）\n        const estimatedMenuWidth = 200;\n        const estimatedMenuHeight = 300;\n        // 水平方向调整\n        if (x + estimatedMenuWidth > viewportWidth) {\n            x = viewportWidth - estimatedMenuWidth - 10;\n        }\n        if (x < 10) {\n            x = 10;\n        }\n        // 垂直方向调整\n        if (y + estimatedMenuHeight > viewportHeight) {\n            y = viewportHeight - estimatedMenuHeight - 10;\n        }\n        if (y < 10) {\n            y = 10;\n        }\n        return {\n            x,\n            y\n        };\n    }\n    /**\r\n   * 显示右键菜单\r\n   */ showContextMenu(param) {\n        let { position, node } = param;\n        var // 触发菜单显示回调\n        _this_config_onContextMenuShow, _this_config;\n        this.contextMenuPosition = position;\n        this.contextMenuNode = node;\n        this.contextMenuVisible = true;\n        (_this_config_onContextMenuShow = (_this_config = this.config).onContextMenuShow) === null || _this_config_onContextMenuShow === void 0 ? void 0 : _this_config_onContextMenuShow.call(_this_config, position, node);\n        console.log(\"✅ 显示右键菜单:\", {\n            position,\n            node\n        });\n    }\n    /**\r\n   * 隐藏右键菜单\r\n   */ hideContextMenu() {\n        var // 触发菜单隐藏回调\n        _this_config_onContextMenuHide, _this_config;\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        (_this_config_onContextMenuHide = (_this_config = this.config).onContextMenuHide) === null || _this_config_onContextMenuHide === void 0 ? void 0 : _this_config_onContextMenuHide.call(_this_config);\n        console.log(\"✅ 隐藏右键菜单\");\n    }\n    /**\r\n   * 获取右键菜单状态\r\n   */ getContextMenuState() {\n        return {\n            visible: this.contextMenuVisible,\n            position: this.contextMenuPosition,\n            node: this.contextMenuNode\n        };\n    }\n    /**\r\n   * 销毁事件管理器\r\n   */ destroy() {\n        // 移除所有事件监听器\n        for (const [event, handler] of this.eventListeners){\n            try {\n                this.mindMapInstance.off(event, handler);\n            } catch (error) {\n                console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n            }\n        }\n        // 清理右键菜单状态\n        this.hideContextMenu();\n        this.eventListeners.clear();\n        this.isInitialized = false;\n        // 不重置isFirstRender，保持首次渲染状态\n        console.log(\"✅ 事件管理器销毁完成\");\n    }\n    constructor(mindMapInstance, config){\n        this.eventListeners = new Map();\n        this.isInitialized = false;\n        this.isFirstRender = true;\n        // 右键菜单状态管理\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        // 清理状态管理\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE;\n        this.mindMapInstance = mindMapInstance;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\n"));

/***/ })

});
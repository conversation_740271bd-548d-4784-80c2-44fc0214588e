"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/DragEnhancer.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_DRAG_ENHANCER_CONFIG: function() { return /* binding */ DEFAULT_DRAG_ENHANCER_CONFIG; },\n/* harmony export */   DragEnhancer: function() { return /* binding */ DragEnhancer; }\n/* harmony export */ });\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/**\n * DragEnhancer - 拖拽功能增强管理器\n * 基于SimpleMindMap的Drag插件，提供更丰富的拖拽体验\n * \n * 功能特性：\n * - 拖拽动画效果\n * - 视觉反馈优化\n * - 拖拽权限控制\n * - 拖拽数据保存\n * - 性能优化\n */ \nconst DEFAULT_DRAG_ENHANCER_CONFIG = {\n    enabled: true,\n    animation: {\n        startDuration: 200,\n        endDuration: 300,\n        easing: \"cubic-bezier(0.25, 0.46, 0.45, 0.94)\",\n        enableSpring: true\n    },\n    visual: {\n        dragOpacity: 0.7,\n        cloneOpacity: 0.8,\n        placeholderColor: \"#FFD700\",\n        placeholderWidth: 3,\n        showTrail: false\n    },\n    constraints: {\n        minDragDistance: 5,\n        constrainToCanvas: true\n    },\n    performance: {\n        throttleDelay: 16,\n        enableGPUAcceleration: true,\n        maxDragNodes: 10\n    },\n    persistence: {\n        autoSave: true,\n        saveDelay: 1000\n    }\n};\nclass DragEnhancer {\n    /**\n   * 设置当前文件ID\n   */ setCurrentFileId(fileId) {\n        this.currentFileId = fileId;\n    }\n    /**\n   * 初始化拖拽增强功能\n   */ initialize() {\n        if (!this.config.enabled || this.isInitialized) {\n            return;\n        }\n        try {\n            this.setupDragEnhancements();\n            this.setupEventListeners();\n            this.isInitialized = true;\n        } catch (error) {\n            console.error(\"❌ DragEnhancer 初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 设置拖拽增强功能\n   */ setupDragEnhancements() {\n        // 确保拖拽功能未被禁用\n        if (this.mindMapInstance.opt.readonly || this.mindMapInstance.opt.isDisableDrag) {\n            return;\n        }\n        // 只更新视觉相关的拖拽配置，不干扰核心拖拽逻辑\n        const dragConfig = {\n            // 拖拽透明度配置\n            dragOpacityConfig: {\n                beingDragNodeOpacity: this.config.visual.dragOpacity,\n                cloneNodeOpacity: this.config.visual.cloneOpacity\n            },\n            // 拖拽位置指示器配置\n            dragPlaceholderRectFill: this.config.visual.placeholderColor,\n            dragPlaceholderLineConfig: {\n                color: this.config.visual.placeholderColor,\n                width: this.config.visual.placeholderWidth\n            },\n            // 拖拽边缘自动移动\n            autoMoveWhenMouseInEdgeOnDrag: true\n        };\n        // 应用配置到SimpleMindMap实例\n        Object.assign(this.mindMapInstance.opt, dragConfig);\n    }\n    /**\n   * 设置事件监听器\n   */ setupEventListeners() {\n        // 监听拖拽开始事件\n        this.mindMapInstance.on(\"node_dragging\", this.handleDragStart.bind(this));\n        // 监听拖拽结束事件\n        this.mindMapInstance.on(\"node_dragend\", this.handleDragEnd.bind(this));\n        // 监听数据变更事件（用于自动保存）\n        this.mindMapInstance.on(\"data_change\", this.handleDataChange.bind(this));\n    }\n    /**\n   * 处理拖拽开始\n   */ handleDragStart(node) {\n        this.dragState.isDragging = true;\n        this.dragState.dragStartTime = Date.now();\n        this.dragState.dragNodes = Array.isArray(node) ? node : [\n            node\n        ];\n        // 记录原始位置\n        this.dragState.originalPositions.clear();\n        this.dragState.dragNodes.forEach((dragNode)=>{\n            if (dragNode.uid) {\n                this.dragState.originalPositions.set(dragNode.uid, {\n                    x: dragNode.left || 0,\n                    y: dragNode.top || 0\n                });\n            }\n        });\n        // 触发拖拽开始动画\n        this.playDragStartAnimation();\n    }\n    /**\n   * 处理拖拽结束\n   */ handleDragEnd(dragInfo) {\n        const dragDuration = Date.now() - this.dragState.dragStartTime;\n        // 触发拖拽结束动画\n        this.playDragEndAnimation();\n        // 重置拖拽状态\n        this.dragState.isDragging = false;\n        this.dragState.dragNodes = [];\n        this.dragState.originalPositions.clear();\n    }\n    /**\n   * 播放拖拽开始动画\n   */ playDragStartAnimation() {\n        if (!this.config.animation.enableSpring) return;\n        // 实现弹性动画效果\n        this.animationFrameId = requestAnimationFrame(()=>{\n        // 这里可以添加更复杂的动画逻辑\n        // 动画逻辑可以在这里实现\n        });\n    }\n    /**\n   * 播放拖拽结束动画\n   */ playDragEndAnimation() {\n        if (this.animationFrameId) {\n            cancelAnimationFrame(this.animationFrameId);\n            this.animationFrameId = null;\n        }\n    }\n    /**\n   * 处理数据变更（用于自动保存）- 使用 SaveManager\n   */ handleDataChange(data) {\n        if (!this.config.persistence.autoSave || !this.currentFileId) return;\n        console.log(\"\\uD83C\\uDFAF DragEnhancer 数据变更:\", {\n            fileId: this.currentFileId,\n            dataType: typeof data,\n            hasData: !!data\n        });\n        // 将思维导图数据转换为 Markdown 格式\n        try {\n            const markdownContent = this.convertMindMapToMarkdown(data);\n            // 使用 SaveManager 调度保存转换后的字符串\n            this.saveManager.scheduleAutoSave(this.currentFileId, markdownContent, {\n                debounceDelay: this.config.persistence.saveDelay,\n                saveType: \"drag\",\n                immediate: false\n            });\n            console.log(\"\\uD83C\\uDFAF 拖拽数据已转换为 Markdown 并调度保存:\", this.currentFileId);\n        } catch (error) {\n            console.error(\"❌ DragEnhancer 数据转换失败:\", error);\n        }\n    }\n    /**\n   * 将思维导图数据转换为 Markdown 格式\n   */ convertMindMapToMarkdown(data) {\n        if (!data || !data.data || !data.data.text) {\n            console.warn(\"⚠️ DragEnhancer 收到无效的思维导图数据\");\n            return \"# 思维导图\\n\\n内容为空\";\n        }\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            if (!node || !node.data || !node.data.text) {\n                return \"\";\n            }\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        const markdownResult = convertNode(data).trim();\n        return markdownResult || \"# 思维导图\\n\\n内容为空\";\n    }\n    /**\n   * 更新配置\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        if (this.isInitialized) {\n            this.setupDragEnhancements();\n        }\n    }\n    /**\n   * 获取拖拽状态\n   */ getDragState() {\n        return {\n            ...this.dragState\n        };\n    }\n    /**\n   * 销毁拖拽增强器\n   */ destroy() {\n        if (this.animationFrameId) {\n            cancelAnimationFrame(this.animationFrameId);\n            this.animationFrameId = null;\n        }\n        // 清理当前文件的保存状态\n        if (this.currentFileId) {\n            this.saveManager.clearFileState(this.currentFileId);\n        }\n        // 移除事件监听器\n        this.mindMapInstance.off(\"node_dragging\", this.handleDragStart.bind(this));\n        this.mindMapInstance.off(\"node_dragend\", this.handleDragEnd.bind(this));\n        this.mindMapInstance.off(\"data_change\", this.handleDataChange.bind(this));\n        this.isInitialized = false;\n        this.currentFileId = null;\n        console.log(\"\\uD83D\\uDDD1️ DragEnhancer 已销毁\");\n    }\n    constructor(mindMapInstance, config = {}){\n        this.isInitialized = false;\n        this.dragState = {\n            isDragging: false,\n            dragStartTime: 0,\n            dragNodes: [],\n            originalPositions: new Map()\n        };\n        this.animationFrameId = null;\n        this.currentFileId = null;\n        this.mindMapInstance = mindMapInstance;\n        this.config = {\n            ...DEFAULT_DRAG_ENHANCER_CONFIG,\n            ...config\n        };\n        this.saveManager = _services_SaveManager__WEBPACK_IMPORTED_MODULE_0__.SaveManager.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9EcmFnRW5oYW5jZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7Ozs7Ozs7Q0FVQyxHQUdvRDtBQTREOUMsTUFBTUMsK0JBQW1EO0lBQzlEQyxTQUFTO0lBQ1RDLFdBQVc7UUFDVEMsZUFBZTtRQUNmQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUkMsY0FBYztJQUNoQjtJQUNBQyxRQUFRO1FBQ05DLGFBQWE7UUFDYkMsY0FBYztRQUNkQyxrQkFBa0I7UUFDbEJDLGtCQUFrQjtRQUNsQkMsV0FBVztJQUNiO0lBQ0FDLGFBQWE7UUFDWEMsaUJBQWlCO1FBQ2pCQyxtQkFBbUI7SUFDckI7SUFDQUMsYUFBYTtRQUNYQyxlQUFlO1FBQ2ZDLHVCQUF1QjtRQUN2QkMsY0FBYztJQUNoQjtJQUNBQyxhQUFhO1FBQ1hDLFVBQVU7UUFDVkMsV0FBVztJQUNiO0FBQ0YsRUFBRTtBQUVLLE1BQU1DO0lBeUJYOztHQUVDLEdBQ0RDLGlCQUFpQkMsTUFBYyxFQUFRO1FBQ3JDLElBQUksQ0FBQ0MsYUFBYSxHQUFHRDtJQUN2QjtJQUVBOztHQUVDLEdBQ0RFLGFBQW1CO1FBQ2pCLElBQUksQ0FBQyxJQUFJLENBQUNDLE1BQU0sQ0FBQzNCLE9BQU8sSUFBSSxJQUFJLENBQUM0QixhQUFhLEVBQUU7WUFDOUM7UUFDRjtRQUVBLElBQUk7WUFDRixJQUFJLENBQUNDLHFCQUFxQjtZQUMxQixJQUFJLENBQUNDLG1CQUFtQjtZQUN4QixJQUFJLENBQUNGLGFBQWEsR0FBRztRQUN2QixFQUFFLE9BQU9HLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkMsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCx3QkFBc0M7UUFDcEMsYUFBYTtRQUNiLElBQUksSUFBSSxDQUFDRSxlQUFlLENBQUNDLEdBQUcsQ0FBQ0MsUUFBUSxJQUFJLElBQUksQ0FBQ0YsZUFBZSxDQUFDQyxHQUFHLENBQUNFLGFBQWEsRUFBRTtZQUMvRTtRQUNGO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU1DLGFBQWE7WUFDakIsVUFBVTtZQUNWQyxtQkFBbUI7Z0JBQ2pCQyxzQkFBc0IsSUFBSSxDQUFDWixNQUFNLENBQUNyQixNQUFNLENBQUNDLFdBQVc7Z0JBQ3BEaUMsa0JBQWtCLElBQUksQ0FBQ2IsTUFBTSxDQUFDckIsTUFBTSxDQUFDRSxZQUFZO1lBQ25EO1lBRUEsWUFBWTtZQUNaaUMseUJBQXlCLElBQUksQ0FBQ2QsTUFBTSxDQUFDckIsTUFBTSxDQUFDRyxnQkFBZ0I7WUFDNURpQywyQkFBMkI7Z0JBQ3pCQyxPQUFPLElBQUksQ0FBQ2hCLE1BQU0sQ0FBQ3JCLE1BQU0sQ0FBQ0csZ0JBQWdCO2dCQUMxQ21DLE9BQU8sSUFBSSxDQUFDakIsTUFBTSxDQUFDckIsTUFBTSxDQUFDSSxnQkFBZ0I7WUFDNUM7WUFFQSxXQUFXO1lBQ1htQywrQkFBK0I7UUFDakM7UUFFQSx1QkFBdUI7UUFDdkJDLE9BQU9DLE1BQU0sQ0FBQyxJQUFJLENBQUNkLGVBQWUsQ0FBQ0MsR0FBRyxFQUFFRztJQUMxQztJQUVBOztHQUVDLEdBQ0Qsc0JBQW9DO1FBQ2xDLFdBQVc7UUFDWCxJQUFJLENBQUNKLGVBQWUsQ0FBQ2UsRUFBRSxDQUFDLGlCQUFpQixJQUFJLENBQUNDLGVBQWUsQ0FBQ0MsSUFBSSxDQUFDLElBQUk7UUFFdkUsV0FBVztRQUNYLElBQUksQ0FBQ2pCLGVBQWUsQ0FBQ2UsRUFBRSxDQUFDLGdCQUFnQixJQUFJLENBQUNHLGFBQWEsQ0FBQ0QsSUFBSSxDQUFDLElBQUk7UUFFcEUsbUJBQW1CO1FBQ25CLElBQUksQ0FBQ2pCLGVBQWUsQ0FBQ2UsRUFBRSxDQUFDLGVBQWUsSUFBSSxDQUFDSSxnQkFBZ0IsQ0FBQ0YsSUFBSSxDQUFDLElBQUk7SUFDeEU7SUFRQTs7R0FFQyxHQUNELGdCQUF3QkcsSUFBUyxFQUFRO1FBQ3ZDLElBQUksQ0FBQ0MsU0FBUyxDQUFDQyxVQUFVLEdBQUc7UUFDNUIsSUFBSSxDQUFDRCxTQUFTLENBQUNFLGFBQWEsR0FBR0MsS0FBS0MsR0FBRztRQUN2QyxJQUFJLENBQUNKLFNBQVMsQ0FBQ0ssU0FBUyxHQUFHQyxNQUFNQyxPQUFPLENBQUNSLFFBQVFBLE9BQU87WUFBQ0E7U0FBSztRQUU5RCxTQUFTO1FBQ1QsSUFBSSxDQUFDQyxTQUFTLENBQUNRLGlCQUFpQixDQUFDQyxLQUFLO1FBQ3RDLElBQUksQ0FBQ1QsU0FBUyxDQUFDSyxTQUFTLENBQUNLLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDL0IsSUFBSUEsU0FBU0MsR0FBRyxFQUFFO2dCQUNoQixJQUFJLENBQUNaLFNBQVMsQ0FBQ1EsaUJBQWlCLENBQUNLLEdBQUcsQ0FBQ0YsU0FBU0MsR0FBRyxFQUFFO29CQUNqREUsR0FBR0gsU0FBU0ksSUFBSSxJQUFJO29CQUNwQkMsR0FBR0wsU0FBU00sR0FBRyxJQUFJO2dCQUNyQjtZQUNGO1FBQ0Y7UUFJQSxXQUFXO1FBQ1gsSUFBSSxDQUFDQyxzQkFBc0I7SUFDN0I7SUFFQTs7R0FFQyxHQUNELGNBQXNCQyxRQUFhLEVBQVE7UUFDekMsTUFBTUMsZUFBZWpCLEtBQUtDLEdBQUcsS0FBSyxJQUFJLENBQUNKLFNBQVMsQ0FBQ0UsYUFBYTtRQUk5RCxXQUFXO1FBQ1gsSUFBSSxDQUFDbUIsb0JBQW9CO1FBRXpCLFNBQVM7UUFDVCxJQUFJLENBQUNyQixTQUFTLENBQUNDLFVBQVUsR0FBRztRQUM1QixJQUFJLENBQUNELFNBQVMsQ0FBQ0ssU0FBUyxHQUFHLEVBQUU7UUFDN0IsSUFBSSxDQUFDTCxTQUFTLENBQUNRLGlCQUFpQixDQUFDQyxLQUFLO0lBQ3hDO0lBRUE7O0dBRUMsR0FDRCx5QkFBdUM7UUFDckMsSUFBSSxDQUFDLElBQUksQ0FBQ3BDLE1BQU0sQ0FBQzFCLFNBQVMsQ0FBQ0ksWUFBWSxFQUFFO1FBRXpDLFdBQVc7UUFDWCxJQUFJLENBQUN1RSxnQkFBZ0IsR0FBR0Msc0JBQXNCO1FBQzVDLGlCQUFpQjtRQUNqQixjQUFjO1FBQ2hCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHVCQUFxQztRQUNuQyxJQUFJLElBQUksQ0FBQ0QsZ0JBQWdCLEVBQUU7WUFDekJFLHFCQUFxQixJQUFJLENBQUNGLGdCQUFnQjtZQUMxQyxJQUFJLENBQUNBLGdCQUFnQixHQUFHO1FBQzFCO0lBR0Y7SUFFQTs7R0FFQyxHQUNELGlCQUF5QkcsSUFBUyxFQUFRO1FBQ3hDLElBQUksQ0FBQyxJQUFJLENBQUNwRCxNQUFNLENBQUNSLFdBQVcsQ0FBQ0MsUUFBUSxJQUFJLENBQUMsSUFBSSxDQUFDSyxhQUFhLEVBQUU7UUFFOURPLFFBQVFnRCxHQUFHLENBQUMsbUNBQXlCO1lBQ25DeEQsUUFBUSxJQUFJLENBQUNDLGFBQWE7WUFDMUJ3RCxVQUFVLE9BQU9GO1lBQ2pCRyxTQUFTLENBQUMsQ0FBQ0g7UUFDYjtRQUVBLHlCQUF5QjtRQUN6QixJQUFJO1lBQ0YsTUFBTUksa0JBQWtCLElBQUksQ0FBQ0Msd0JBQXdCLENBQUNMO1lBRXRELDZCQUE2QjtZQUM3QixJQUFJLENBQUNNLFdBQVcsQ0FBQ0MsZ0JBQWdCLENBQUMsSUFBSSxDQUFDN0QsYUFBYSxFQUFFMEQsaUJBQWlCO2dCQUNyRUksZUFBZSxJQUFJLENBQUM1RCxNQUFNLENBQUNSLFdBQVcsQ0FBQ0UsU0FBUztnQkFDaERtRSxVQUFVO2dCQUNWQyxXQUFXO1lBQ2I7WUFFQXpELFFBQVFnRCxHQUFHLENBQUMseUNBQStCLElBQUksQ0FBQ3ZELGFBQWE7UUFDL0QsRUFBRSxPQUFPTSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQzFDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHlCQUFpQ2dELElBQVMsRUFBVTtRQUNsRCxJQUFJLENBQUNBLFFBQVEsQ0FBQ0EsS0FBS0EsSUFBSSxJQUFJLENBQUNBLEtBQUtBLElBQUksQ0FBQ1csSUFBSSxFQUFFO1lBQzFDMUQsUUFBUTJELElBQUksQ0FBQztZQUNiLE9BQU87UUFDVDtRQUVBLE1BQU1DLGNBQWMsU0FBQ3ZDO2dCQUFXd0MseUVBQWdCO1lBQzlDLElBQUksQ0FBQ3hDLFFBQVEsQ0FBQ0EsS0FBSzBCLElBQUksSUFBSSxDQUFDMUIsS0FBSzBCLElBQUksQ0FBQ1csSUFBSSxFQUFFO2dCQUMxQyxPQUFPO1lBQ1Q7WUFFQSxNQUFNSSxTQUFTLElBQUlDLE1BQU0sQ0FBQ0Y7WUFDMUIsSUFBSUcsU0FBUyxHQUFhM0MsT0FBVnlDLFFBQU8sS0FBa0IsT0FBZnpDLEtBQUswQixJQUFJLENBQUNXLElBQUksRUFBQztZQUV6QyxJQUFJckMsS0FBSzRDLFFBQVEsSUFBSTVDLEtBQUs0QyxRQUFRLENBQUNDLE1BQU0sR0FBRyxHQUFHO2dCQUM3QyxLQUFLLE1BQU1DLFNBQVM5QyxLQUFLNEMsUUFBUSxDQUFFO29CQUNqQ0QsVUFBVUosWUFBWU8sT0FBT04sUUFBUTtnQkFDdkM7WUFDRjtZQUVBLE9BQU9HO1FBQ1Q7UUFFQSxNQUFNSSxpQkFBaUJSLFlBQVliLE1BQU1zQixJQUFJO1FBQzdDLE9BQU9ELGtCQUFrQjtJQUMzQjtJQU1BOztHQUVDLEdBQ0RFLGFBQWFDLFNBQXNDLEVBQVE7UUFDekQsSUFBSSxDQUFDNUUsTUFBTSxHQUFHO1lBQUUsR0FBRyxJQUFJLENBQUNBLE1BQU07WUFBRSxHQUFHNEUsU0FBUztRQUFDO1FBRTdDLElBQUksSUFBSSxDQUFDM0UsYUFBYSxFQUFFO1lBQ3RCLElBQUksQ0FBQ0MscUJBQXFCO1FBQzVCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEMkUsZUFBZTtRQUNiLE9BQU87WUFBRSxHQUFHLElBQUksQ0FBQ2xELFNBQVM7UUFBQztJQUM3QjtJQUVBOztHQUVDLEdBQ0RtRCxVQUFnQjtRQUNkLElBQUksSUFBSSxDQUFDN0IsZ0JBQWdCLEVBQUU7WUFDekJFLHFCQUFxQixJQUFJLENBQUNGLGdCQUFnQjtZQUMxQyxJQUFJLENBQUNBLGdCQUFnQixHQUFHO1FBQzFCO1FBRUEsY0FBYztRQUNkLElBQUksSUFBSSxDQUFDbkQsYUFBYSxFQUFFO1lBQ3RCLElBQUksQ0FBQzRELFdBQVcsQ0FBQ3FCLGNBQWMsQ0FBQyxJQUFJLENBQUNqRixhQUFhO1FBQ3BEO1FBRUEsVUFBVTtRQUNWLElBQUksQ0FBQ1EsZUFBZSxDQUFDMEUsR0FBRyxDQUFDLGlCQUFpQixJQUFJLENBQUMxRCxlQUFlLENBQUNDLElBQUksQ0FBQyxJQUFJO1FBQ3hFLElBQUksQ0FBQ2pCLGVBQWUsQ0FBQzBFLEdBQUcsQ0FBQyxnQkFBZ0IsSUFBSSxDQUFDeEQsYUFBYSxDQUFDRCxJQUFJLENBQUMsSUFBSTtRQUNyRSxJQUFJLENBQUNqQixlQUFlLENBQUMwRSxHQUFHLENBQUMsZUFBZSxJQUFJLENBQUN2RCxnQkFBZ0IsQ0FBQ0YsSUFBSSxDQUFDLElBQUk7UUFFdkUsSUFBSSxDQUFDdEIsYUFBYSxHQUFHO1FBQ3JCLElBQUksQ0FBQ0gsYUFBYSxHQUFHO1FBRXJCTyxRQUFRZ0QsR0FBRyxDQUFDO0lBQ2Q7SUE5UEE0QixZQUFZM0UsZUFBc0MsRUFBRU4sU0FBc0MsQ0FBQyxDQUFDLENBQUU7YUFoQnRGQyxnQkFBZ0I7YUFDaEIwQixZQUtKO1lBQ0ZDLFlBQVk7WUFDWkMsZUFBZTtZQUNmRyxXQUFXLEVBQUU7WUFDYkcsbUJBQW1CLElBQUkrQztRQUN6QjthQUNRakMsbUJBQWtDO2FBRWxDbkQsZ0JBQStCO1FBR3JDLElBQUksQ0FBQ1EsZUFBZSxHQUFHQTtRQUN2QixJQUFJLENBQUNOLE1BQU0sR0FBRztZQUFFLEdBQUc1Qiw0QkFBNEI7WUFBRSxHQUFHNEIsTUFBTTtRQUFDO1FBQzNELElBQUksQ0FBQzBELFdBQVcsR0FBR3ZGLDhEQUFXQSxDQUFDZ0gsV0FBVztJQUM1QztBQTJQRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9NaW5kTWFwUmVuZGVyZXIvbWFuYWdlcnMvRHJhZ0VuaGFuY2VyLnRzPzUwYTMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEcmFnRW5oYW5jZXIgLSDmi5bmi73lip/og73lop7lvLrnrqHnkIblmahcbiAqIOWfuuS6jlNpbXBsZU1pbmRNYXDnmoREcmFn5o+S5Lu277yM5o+Q5L6b5pu05Liw5a+M55qE5ouW5ou95L2T6aqMXG4gKiBcbiAqIOWKn+iDveeJueaAp++8mlxuICogLSDmi5bmi73liqjnlLvmlYjmnpxcbiAqIC0g6KeG6KeJ5Y+N6aaI5LyY5YyWXG4gKiAtIOaLluaLveadg+mZkOaOp+WItlxuICogLSDmi5bmi73mlbDmja7kv53lrZhcbiAqIC0g5oCn6IO95LyY5YyWXG4gKi9cblxuaW1wb3J0IHsgU2ltcGxlTWluZE1hcEluc3RhbmNlIH0gZnJvbSAnLi4vdHlwZXMnO1xuaW1wb3J0IHsgU2F2ZU1hbmFnZXIgfSBmcm9tICdAL3NlcnZpY2VzL1NhdmVNYW5hZ2VyJztcblxuZXhwb3J0IGludGVyZmFjZSBEcmFnRW5oYW5jZXJDb25maWcge1xuICAvKiog5piv5ZCm5ZCv55So5ouW5ou95aKe5by6ICovXG4gIGVuYWJsZWQ6IGJvb2xlYW47XG4gIC8qKiDmi5bmi73liqjnlLvphY3nva4gKi9cbiAgYW5pbWF0aW9uOiB7XG4gICAgLyoqIOaLluaLveW8gOWni+WKqOeUu+aXtumVvyAobXMpICovXG4gICAgc3RhcnREdXJhdGlvbjogbnVtYmVyO1xuICAgIC8qKiDmi5bmi73nu5PmnZ/liqjnlLvml7bplb8gKG1zKSAqL1xuICAgIGVuZER1cmF0aW9uOiBudW1iZXI7XG4gICAgLyoqIOe8k+WKqOWHveaVsCAqL1xuICAgIGVhc2luZzogc3RyaW5nO1xuICAgIC8qKiDmmK/lkKblkK/nlKjlvLnmgKfmlYjmnpwgKi9cbiAgICBlbmFibGVTcHJpbmc6IGJvb2xlYW47XG4gIH07XG4gIC8qKiDop4bop4nlj43ppojphY3nva4gKi9cbiAgdmlzdWFsOiB7XG4gICAgLyoqIOaLluaLveaXtuiKgueCuemAj+aYjuW6piAqL1xuICAgIGRyYWdPcGFjaXR5OiBudW1iZXI7XG4gICAgLyoqIOWFi+mahuiKgueCuemAj+aYjuW6piAqL1xuICAgIGNsb25lT3BhY2l0eTogbnVtYmVyO1xuICAgIC8qKiDkvY3nva7mjIfnpLrlmajpopzoibIgKi9cbiAgICBwbGFjZWhvbGRlckNvbG9yOiBzdHJpbmc7XG4gICAgLyoqIOS9jee9ruaMh+ekuuWZqOWuveW6piAqL1xuICAgIHBsYWNlaG9sZGVyV2lkdGg6IG51bWJlcjtcbiAgICAvKiog5piv5ZCm5pi+56S65ouW5ou96L2o6L+5ICovXG4gICAgc2hvd1RyYWlsOiBib29sZWFuO1xuICB9O1xuICAvKiog5ouW5ou96ZmQ5Yi26YWN572uICovXG4gIGNvbnN0cmFpbnRzOiB7XG4gICAgLyoqIOacgOWwj+aLluaLvei3neemuyAocHgpICovXG4gICAgbWluRHJhZ0Rpc3RhbmNlOiBudW1iZXI7XG4gICAgLyoqIOaYr+WQpumZkOWItuaLluaLveiMg+WbtCAqL1xuICAgIGNvbnN0cmFpblRvQ2FudmFzOiBib29sZWFuO1xuICAgIC8qKiDmi5bmi73mnYPpmZDmo4Dmn6Xlh73mlbAgKi9cbiAgICBjYW5EcmFnPzogKG5vZGU6IGFueSkgPT4gYm9vbGVhbjtcbiAgICAvKiog5ouW5ou955uu5qCH5qOA5p+l5Ye95pWwICovXG4gICAgY2FuRHJvcFRvPzogKGRyYWdOb2RlOiBhbnksIHRhcmdldE5vZGU6IGFueSkgPT4gYm9vbGVhbjtcbiAgfTtcbiAgLyoqIOaAp+iDveS8mOWMlumFjee9riAqL1xuICBwZXJmb3JtYW5jZToge1xuICAgIC8qKiDmi5bmi73oioLmtYHml7bpl7QgKG1zKSAqL1xuICAgIHRocm90dGxlRGVsYXk6IG51bWJlcjtcbiAgICAvKiog5piv5ZCm5ZCv55SoR1BV5Yqg6YCfICovXG4gICAgZW5hYmxlR1BVQWNjZWxlcmF0aW9uOiBib29sZWFuO1xuICAgIC8qKiDmnIDlpKflkIzml7bmi5bmi73oioLngrnmlbAgKi9cbiAgICBtYXhEcmFnTm9kZXM6IG51bWJlcjtcbiAgfTtcbiAgLyoqIOaVsOaNruS/neWtmOmFjee9riAqL1xuICBwZXJzaXN0ZW5jZToge1xuICAgIC8qKiDmmK/lkKboh6rliqjkv53lrZjmi5bmi73lkI7nmoTmlbDmja4gKi9cbiAgICBhdXRvU2F2ZTogYm9vbGVhbjtcbiAgICAvKiog5L+d5a2Y5bu26L+f5pe26Ze0IChtcykgLSDnjrDlnKjpgJrov4cgU2F2ZU1hbmFnZXIg57uf5LiA566h55CGICovXG4gICAgc2F2ZURlbGF5OiBudW1iZXI7XG4gICAgLyoqIOS/neWtmOWbnuiwg+WHveaVsCAtIOW3suW8g+eUqO+8jOeOsOWcqOmAmui/hyBTYXZlTWFuYWdlciDnu5/kuIDlpITnkIYgKi9cbiAgICBvblNhdmU/OiAoZGF0YTogYW55KSA9PiB2b2lkO1xuICB9O1xufVxuXG5leHBvcnQgY29uc3QgREVGQVVMVF9EUkFHX0VOSEFOQ0VSX0NPTkZJRzogRHJhZ0VuaGFuY2VyQ29uZmlnID0ge1xuICBlbmFibGVkOiB0cnVlLFxuICBhbmltYXRpb246IHtcbiAgICBzdGFydER1cmF0aW9uOiAyMDAsXG4gICAgZW5kRHVyYXRpb246IDMwMCxcbiAgICBlYXNpbmc6ICdjdWJpYy1iZXppZXIoMC4yNSwgMC40NiwgMC40NSwgMC45NCknLFxuICAgIGVuYWJsZVNwcmluZzogdHJ1ZVxuICB9LFxuICB2aXN1YWw6IHtcbiAgICBkcmFnT3BhY2l0eTogMC43LFxuICAgIGNsb25lT3BhY2l0eTogMC44LFxuICAgIHBsYWNlaG9sZGVyQ29sb3I6ICcjRkZENzAwJyxcbiAgICBwbGFjZWhvbGRlcldpZHRoOiAzLFxuICAgIHNob3dUcmFpbDogZmFsc2VcbiAgfSxcbiAgY29uc3RyYWludHM6IHtcbiAgICBtaW5EcmFnRGlzdGFuY2U6IDUsXG4gICAgY29uc3RyYWluVG9DYW52YXM6IHRydWVcbiAgfSxcbiAgcGVyZm9ybWFuY2U6IHtcbiAgICB0aHJvdHRsZURlbGF5OiAxNiwgLy8gNjBmcHNcbiAgICBlbmFibGVHUFVBY2NlbGVyYXRpb246IHRydWUsXG4gICAgbWF4RHJhZ05vZGVzOiAxMFxuICB9LFxuICBwZXJzaXN0ZW5jZToge1xuICAgIGF1dG9TYXZlOiB0cnVlLFxuICAgIHNhdmVEZWxheTogMTAwMFxuICB9XG59O1xuXG5leHBvcnQgY2xhc3MgRHJhZ0VuaGFuY2VyIHtcbiAgcHJpdmF0ZSBtaW5kTWFwSW5zdGFuY2U6IFNpbXBsZU1pbmRNYXBJbnN0YW5jZTtcbiAgcHJpdmF0ZSBjb25maWc6IERyYWdFbmhhbmNlckNvbmZpZztcbiAgcHJpdmF0ZSBpc0luaXRpYWxpemVkID0gZmFsc2U7XG4gIHByaXZhdGUgZHJhZ1N0YXRlOiB7XG4gICAgaXNEcmFnZ2luZzogYm9vbGVhbjtcbiAgICBkcmFnU3RhcnRUaW1lOiBudW1iZXI7XG4gICAgZHJhZ05vZGVzOiBhbnlbXTtcbiAgICBvcmlnaW5hbFBvc2l0aW9uczogTWFwPHN0cmluZywgeyB4OiBudW1iZXI7IHk6IG51bWJlciB9PjtcbiAgfSA9IHtcbiAgICBpc0RyYWdnaW5nOiBmYWxzZSxcbiAgICBkcmFnU3RhcnRUaW1lOiAwLFxuICAgIGRyYWdOb2RlczogW10sXG4gICAgb3JpZ2luYWxQb3NpdGlvbnM6IG5ldyBNYXAoKVxuICB9O1xuICBwcml2YXRlIGFuaW1hdGlvbkZyYW1lSWQ6IG51bWJlciB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIHNhdmVNYW5hZ2VyOiBTYXZlTWFuYWdlcjtcbiAgcHJpdmF0ZSBjdXJyZW50RmlsZUlkOiBzdHJpbmcgfCBudWxsID0gbnVsbDtcblxuICBjb25zdHJ1Y3RvcihtaW5kTWFwSW5zdGFuY2U6IFNpbXBsZU1pbmRNYXBJbnN0YW5jZSwgY29uZmlnOiBQYXJ0aWFsPERyYWdFbmhhbmNlckNvbmZpZz4gPSB7fSkge1xuICAgIHRoaXMubWluZE1hcEluc3RhbmNlID0gbWluZE1hcEluc3RhbmNlO1xuICAgIHRoaXMuY29uZmlnID0geyAuLi5ERUZBVUxUX0RSQUdfRU5IQU5DRVJfQ09ORklHLCAuLi5jb25maWcgfTtcbiAgICB0aGlzLnNhdmVNYW5hZ2VyID0gU2F2ZU1hbmFnZXIuZ2V0SW5zdGFuY2UoKTtcbiAgfVxuXG4gIC8qKlxuICAgKiDorr7nva7lvZPliY3mlofku7ZJRFxuICAgKi9cbiAgc2V0Q3VycmVudEZpbGVJZChmaWxlSWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIHRoaXMuY3VycmVudEZpbGVJZCA9IGZpbGVJZDtcbiAgfVxuXG4gIC8qKlxuICAgKiDliJ3lp4vljJbmi5bmi73lop7lvLrlip/og71cbiAgICovXG4gIGluaXRpYWxpemUoKTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLmNvbmZpZy5lbmFibGVkIHx8IHRoaXMuaXNJbml0aWFsaXplZCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICB0aGlzLnNldHVwRHJhZ0VuaGFuY2VtZW50cygpO1xuICAgICAgdGhpcy5zZXR1cEV2ZW50TGlzdGVuZXJzKCk7XG4gICAgICB0aGlzLmlzSW5pdGlhbGl6ZWQgPSB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRHJhZ0VuaGFuY2VyIOWIneWni+WMluWksei0pTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog6K6+572u5ouW5ou95aKe5by65Yqf6IO9XG4gICAqL1xuICBwcml2YXRlIHNldHVwRHJhZ0VuaGFuY2VtZW50cygpOiB2b2lkIHtcbiAgICAvLyDnoa7kv53mi5bmi73lip/og73mnKrooqvnpoHnlKhcbiAgICBpZiAodGhpcy5taW5kTWFwSW5zdGFuY2Uub3B0LnJlYWRvbmx5IHx8IHRoaXMubWluZE1hcEluc3RhbmNlLm9wdC5pc0Rpc2FibGVEcmFnKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g5Y+q5pu05paw6KeG6KeJ55u45YWz55qE5ouW5ou96YWN572u77yM5LiN5bmy5omw5qC45b+D5ouW5ou96YC76L6RXG4gICAgY29uc3QgZHJhZ0NvbmZpZyA9IHtcbiAgICAgIC8vIOaLluaLvemAj+aYjuW6pumFjee9rlxuICAgICAgZHJhZ09wYWNpdHlDb25maWc6IHtcbiAgICAgICAgYmVpbmdEcmFnTm9kZU9wYWNpdHk6IHRoaXMuY29uZmlnLnZpc3VhbC5kcmFnT3BhY2l0eSxcbiAgICAgICAgY2xvbmVOb2RlT3BhY2l0eTogdGhpcy5jb25maWcudmlzdWFsLmNsb25lT3BhY2l0eVxuICAgICAgfSxcblxuICAgICAgLy8g5ouW5ou95L2N572u5oyH56S65Zmo6YWN572uXG4gICAgICBkcmFnUGxhY2Vob2xkZXJSZWN0RmlsbDogdGhpcy5jb25maWcudmlzdWFsLnBsYWNlaG9sZGVyQ29sb3IsXG4gICAgICBkcmFnUGxhY2Vob2xkZXJMaW5lQ29uZmlnOiB7XG4gICAgICAgIGNvbG9yOiB0aGlzLmNvbmZpZy52aXN1YWwucGxhY2Vob2xkZXJDb2xvcixcbiAgICAgICAgd2lkdGg6IHRoaXMuY29uZmlnLnZpc3VhbC5wbGFjZWhvbGRlcldpZHRoXG4gICAgICB9LFxuXG4gICAgICAvLyDmi5bmi73ovrnnvJjoh6rliqjnp7vliqhcbiAgICAgIGF1dG9Nb3ZlV2hlbk1vdXNlSW5FZGdlT25EcmFnOiB0cnVlXG4gICAgfTtcblxuICAgIC8vIOW6lOeUqOmFjee9ruWIsFNpbXBsZU1pbmRNYXDlrp7kvotcbiAgICBPYmplY3QuYXNzaWduKHRoaXMubWluZE1hcEluc3RhbmNlLm9wdCwgZHJhZ0NvbmZpZyk7XG4gIH1cblxuICAvKipcbiAgICog6K6+572u5LqL5Lu255uR5ZCs5ZmoXG4gICAqL1xuICBwcml2YXRlIHNldHVwRXZlbnRMaXN0ZW5lcnMoKTogdm9pZCB7XG4gICAgLy8g55uR5ZCs5ouW5ou95byA5aeL5LqL5Lu2XG4gICAgdGhpcy5taW5kTWFwSW5zdGFuY2Uub24oJ25vZGVfZHJhZ2dpbmcnLCB0aGlzLmhhbmRsZURyYWdTdGFydC5iaW5kKHRoaXMpKTtcbiAgICBcbiAgICAvLyDnm5HlkKzmi5bmi73nu5PmnZ/kuovku7ZcbiAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5vbignbm9kZV9kcmFnZW5kJywgdGhpcy5oYW5kbGVEcmFnRW5kLmJpbmQodGhpcykpO1xuICAgIFxuICAgIC8vIOebkeWQrOaVsOaNruWPmOabtOS6i+S7tu+8iOeUqOS6juiHquWKqOS/neWtmO+8iVxuICAgIHRoaXMubWluZE1hcEluc3RhbmNlLm9uKCdkYXRhX2NoYW5nZScsIHRoaXMuaGFuZGxlRGF0YUNoYW5nZS5iaW5kKHRoaXMpKTtcbiAgfVxuXG5cblxuXG5cblxuXG4gIC8qKlxuICAgKiDlpITnkIbmi5bmi73lvIDlp4tcbiAgICovXG4gIHByaXZhdGUgaGFuZGxlRHJhZ1N0YXJ0KG5vZGU6IGFueSk6IHZvaWQge1xuICAgIHRoaXMuZHJhZ1N0YXRlLmlzRHJhZ2dpbmcgPSB0cnVlO1xuICAgIHRoaXMuZHJhZ1N0YXRlLmRyYWdTdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgIHRoaXMuZHJhZ1N0YXRlLmRyYWdOb2RlcyA9IEFycmF5LmlzQXJyYXkobm9kZSkgPyBub2RlIDogW25vZGVdO1xuXG4gICAgLy8g6K6w5b2V5Y6f5aeL5L2N572uXG4gICAgdGhpcy5kcmFnU3RhdGUub3JpZ2luYWxQb3NpdGlvbnMuY2xlYXIoKTtcbiAgICB0aGlzLmRyYWdTdGF0ZS5kcmFnTm9kZXMuZm9yRWFjaChkcmFnTm9kZSA9PiB7XG4gICAgICBpZiAoZHJhZ05vZGUudWlkKSB7XG4gICAgICAgIHRoaXMuZHJhZ1N0YXRlLm9yaWdpbmFsUG9zaXRpb25zLnNldChkcmFnTm9kZS51aWQsIHtcbiAgICAgICAgICB4OiBkcmFnTm9kZS5sZWZ0IHx8IDAsXG4gICAgICAgICAgeTogZHJhZ05vZGUudG9wIHx8IDBcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cblxuXG4gICAgLy8g6Kem5Y+R5ouW5ou95byA5aeL5Yqo55S7XG4gICAgdGhpcy5wbGF5RHJhZ1N0YXJ0QW5pbWF0aW9uKCk7XG4gIH1cblxuICAvKipcbiAgICog5aSE55CG5ouW5ou957uT5p2fXG4gICAqL1xuICBwcml2YXRlIGhhbmRsZURyYWdFbmQoZHJhZ0luZm86IGFueSk6IHZvaWQge1xuICAgIGNvbnN0IGRyYWdEdXJhdGlvbiA9IERhdGUubm93KCkgLSB0aGlzLmRyYWdTdGF0ZS5kcmFnU3RhcnRUaW1lO1xuICAgIFxuXG5cbiAgICAvLyDop6blj5Hmi5bmi73nu5PmnZ/liqjnlLtcbiAgICB0aGlzLnBsYXlEcmFnRW5kQW5pbWF0aW9uKCk7XG5cbiAgICAvLyDph43nva7mi5bmi73nirbmgIFcbiAgICB0aGlzLmRyYWdTdGF0ZS5pc0RyYWdnaW5nID0gZmFsc2U7XG4gICAgdGhpcy5kcmFnU3RhdGUuZHJhZ05vZGVzID0gW107XG4gICAgdGhpcy5kcmFnU3RhdGUub3JpZ2luYWxQb3NpdGlvbnMuY2xlYXIoKTtcbiAgfVxuXG4gIC8qKlxuICAgKiDmkq3mlL7mi5bmi73lvIDlp4vliqjnlLtcbiAgICovXG4gIHByaXZhdGUgcGxheURyYWdTdGFydEFuaW1hdGlvbigpOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMuY29uZmlnLmFuaW1hdGlvbi5lbmFibGVTcHJpbmcpIHJldHVybjtcblxuICAgIC8vIOWunueOsOW8ueaAp+WKqOeUu+aViOaenFxuICAgIHRoaXMuYW5pbWF0aW9uRnJhbWVJZCA9IHJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiB7XG4gICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDmm7TlpI3mnYLnmoTliqjnlLvpgLvovpFcbiAgICAgIC8vIOWKqOeUu+mAu+i+keWPr+S7peWcqOi/memHjOWunueOsFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIOaSreaUvuaLluaLvee7k+adn+WKqOeUu1xuICAgKi9cbiAgcHJpdmF0ZSBwbGF5RHJhZ0VuZEFuaW1hdGlvbigpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5hbmltYXRpb25GcmFtZUlkKSB7XG4gICAgICBjYW5jZWxBbmltYXRpb25GcmFtZSh0aGlzLmFuaW1hdGlvbkZyYW1lSWQpO1xuICAgICAgdGhpcy5hbmltYXRpb25GcmFtZUlkID0gbnVsbDtcbiAgICB9XG5cblxuICB9XG5cbiAgLyoqXG4gICAqIOWkhOeQhuaVsOaNruWPmOabtO+8iOeUqOS6juiHquWKqOS/neWtmO+8iS0g5L2/55SoIFNhdmVNYW5hZ2VyXG4gICAqL1xuICBwcml2YXRlIGhhbmRsZURhdGFDaGFuZ2UoZGF0YTogYW55KTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLmNvbmZpZy5wZXJzaXN0ZW5jZS5hdXRvU2F2ZSB8fCAhdGhpcy5jdXJyZW50RmlsZUlkKSByZXR1cm47XG5cbiAgICBjb25zb2xlLmxvZygn8J+OryBEcmFnRW5oYW5jZXIg5pWw5o2u5Y+Y5pu0OicsIHtcbiAgICAgIGZpbGVJZDogdGhpcy5jdXJyZW50RmlsZUlkLFxuICAgICAgZGF0YVR5cGU6IHR5cGVvZiBkYXRhLFxuICAgICAgaGFzRGF0YTogISFkYXRhXG4gICAgfSk7XG5cbiAgICAvLyDlsIbmgJ3nu7Tlr7zlm77mlbDmja7ovazmjaLkuLogTWFya2Rvd24g5qC85byPXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG1hcmtkb3duQ29udGVudCA9IHRoaXMuY29udmVydE1pbmRNYXBUb01hcmtkb3duKGRhdGEpO1xuXG4gICAgICAvLyDkvb/nlKggU2F2ZU1hbmFnZXIg6LCD5bqm5L+d5a2Y6L2s5o2i5ZCO55qE5a2X56ym5LiyXG4gICAgICB0aGlzLnNhdmVNYW5hZ2VyLnNjaGVkdWxlQXV0b1NhdmUodGhpcy5jdXJyZW50RmlsZUlkLCBtYXJrZG93bkNvbnRlbnQsIHtcbiAgICAgICAgZGVib3VuY2VEZWxheTogdGhpcy5jb25maWcucGVyc2lzdGVuY2Uuc2F2ZURlbGF5LFxuICAgICAgICBzYXZlVHlwZTogJ2RyYWcnLFxuICAgICAgICBpbW1lZGlhdGU6IGZhbHNlXG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfjq8g5ouW5ou95pWw5o2u5bey6L2s5o2i5Li6IE1hcmtkb3duIOW5tuiwg+W6puS/neWtmDonLCB0aGlzLmN1cnJlbnRGaWxlSWQpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRHJhZ0VuaGFuY2VyIOaVsOaNrui9rOaNouWksei0pTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOWwhuaAnee7tOWvvOWbvuaVsOaNrui9rOaNouS4uiBNYXJrZG93biDmoLzlvI9cbiAgICovXG4gIHByaXZhdGUgY29udmVydE1pbmRNYXBUb01hcmtkb3duKGRhdGE6IGFueSk6IHN0cmluZyB7XG4gICAgaWYgKCFkYXRhIHx8ICFkYXRhLmRhdGEgfHwgIWRhdGEuZGF0YS50ZXh0KSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBEcmFnRW5oYW5jZXIg5pS25Yiw5peg5pWI55qE5oCd57u05a+85Zu+5pWw5o2uJyk7XG4gICAgICByZXR1cm4gJyMg5oCd57u05a+85Zu+XFxuXFxu5YaF5a655Li656m6JztcbiAgICB9XG5cbiAgICBjb25zdCBjb252ZXJ0Tm9kZSA9IChub2RlOiBhbnksIGxldmVsOiBudW1iZXIgPSAxKTogc3RyaW5nID0+IHtcbiAgICAgIGlmICghbm9kZSB8fCAhbm9kZS5kYXRhIHx8ICFub2RlLmRhdGEudGV4dCkge1xuICAgICAgICByZXR1cm4gJyc7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHByZWZpeCA9ICcjJy5yZXBlYXQobGV2ZWwpO1xuICAgICAgbGV0IHJlc3VsdCA9IGAke3ByZWZpeH0gJHtub2RlLmRhdGEudGV4dH1cXG5cXG5gO1xuXG4gICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHtcbiAgICAgICAgZm9yIChjb25zdCBjaGlsZCBvZiBub2RlLmNoaWxkcmVuKSB7XG4gICAgICAgICAgcmVzdWx0ICs9IGNvbnZlcnROb2RlKGNoaWxkLCBsZXZlbCArIDEpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcblxuICAgIGNvbnN0IG1hcmtkb3duUmVzdWx0ID0gY29udmVydE5vZGUoZGF0YSkudHJpbSgpO1xuICAgIHJldHVybiBtYXJrZG93blJlc3VsdCB8fCAnIyDmgJ3nu7Tlr7zlm75cXG5cXG7lhoXlrrnkuLrnqbonO1xuICB9XG5cblxuXG5cblxuICAvKipcbiAgICog5pu05paw6YWN572uXG4gICAqL1xuICB1cGRhdGVDb25maWcobmV3Q29uZmlnOiBQYXJ0aWFsPERyYWdFbmhhbmNlckNvbmZpZz4pOiB2b2lkIHtcbiAgICB0aGlzLmNvbmZpZyA9IHsgLi4udGhpcy5jb25maWcsIC4uLm5ld0NvbmZpZyB9O1xuICAgIFxuICAgIGlmICh0aGlzLmlzSW5pdGlhbGl6ZWQpIHtcbiAgICAgIHRoaXMuc2V0dXBEcmFnRW5oYW5jZW1lbnRzKCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPluaLluaLveeKtuaAgVxuICAgKi9cbiAgZ2V0RHJhZ1N0YXRlKCkge1xuICAgIHJldHVybiB7IC4uLnRoaXMuZHJhZ1N0YXRlIH07XG4gIH1cblxuICAvKipcbiAgICog6ZSA5q+B5ouW5ou95aKe5by65ZmoXG4gICAqL1xuICBkZXN0cm95KCk6IHZvaWQge1xuICAgIGlmICh0aGlzLmFuaW1hdGlvbkZyYW1lSWQpIHtcbiAgICAgIGNhbmNlbEFuaW1hdGlvbkZyYW1lKHRoaXMuYW5pbWF0aW9uRnJhbWVJZCk7XG4gICAgICB0aGlzLmFuaW1hdGlvbkZyYW1lSWQgPSBudWxsO1xuICAgIH1cblxuICAgIC8vIOa4heeQhuW9k+WJjeaWh+S7tueahOS/neWtmOeKtuaAgVxuICAgIGlmICh0aGlzLmN1cnJlbnRGaWxlSWQpIHtcbiAgICAgIHRoaXMuc2F2ZU1hbmFnZXIuY2xlYXJGaWxlU3RhdGUodGhpcy5jdXJyZW50RmlsZUlkKTtcbiAgICB9XG5cbiAgICAvLyDnp7vpmaTkuovku7bnm5HlkKzlmahcbiAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5vZmYoJ25vZGVfZHJhZ2dpbmcnLCB0aGlzLmhhbmRsZURyYWdTdGFydC5iaW5kKHRoaXMpKTtcbiAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5vZmYoJ25vZGVfZHJhZ2VuZCcsIHRoaXMuaGFuZGxlRHJhZ0VuZC5iaW5kKHRoaXMpKTtcbiAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5vZmYoJ2RhdGFfY2hhbmdlJywgdGhpcy5oYW5kbGVEYXRhQ2hhbmdlLmJpbmQodGhpcykpO1xuXG4gICAgdGhpcy5pc0luaXRpYWxpemVkID0gZmFsc2U7XG4gICAgdGhpcy5jdXJyZW50RmlsZUlkID0gbnVsbDtcblxuICAgIGNvbnNvbGUubG9nKCfwn5eR77iPIERyYWdFbmhhbmNlciDlt7LplIDmr4EnKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIlNhdmVNYW5hZ2VyIiwiREVGQVVMVF9EUkFHX0VOSEFOQ0VSX0NPTkZJRyIsImVuYWJsZWQiLCJhbmltYXRpb24iLCJzdGFydER1cmF0aW9uIiwiZW5kRHVyYXRpb24iLCJlYXNpbmciLCJlbmFibGVTcHJpbmciLCJ2aXN1YWwiLCJkcmFnT3BhY2l0eSIsImNsb25lT3BhY2l0eSIsInBsYWNlaG9sZGVyQ29sb3IiLCJwbGFjZWhvbGRlcldpZHRoIiwic2hvd1RyYWlsIiwiY29uc3RyYWludHMiLCJtaW5EcmFnRGlzdGFuY2UiLCJjb25zdHJhaW5Ub0NhbnZhcyIsInBlcmZvcm1hbmNlIiwidGhyb3R0bGVEZWxheSIsImVuYWJsZUdQVUFjY2VsZXJhdGlvbiIsIm1heERyYWdOb2RlcyIsInBlcnNpc3RlbmNlIiwiYXV0b1NhdmUiLCJzYXZlRGVsYXkiLCJEcmFnRW5oYW5jZXIiLCJzZXRDdXJyZW50RmlsZUlkIiwiZmlsZUlkIiwiY3VycmVudEZpbGVJZCIsImluaXRpYWxpemUiLCJjb25maWciLCJpc0luaXRpYWxpemVkIiwic2V0dXBEcmFnRW5oYW5jZW1lbnRzIiwic2V0dXBFdmVudExpc3RlbmVycyIsImVycm9yIiwiY29uc29sZSIsIm1pbmRNYXBJbnN0YW5jZSIsIm9wdCIsInJlYWRvbmx5IiwiaXNEaXNhYmxlRHJhZyIsImRyYWdDb25maWciLCJkcmFnT3BhY2l0eUNvbmZpZyIsImJlaW5nRHJhZ05vZGVPcGFjaXR5IiwiY2xvbmVOb2RlT3BhY2l0eSIsImRyYWdQbGFjZWhvbGRlclJlY3RGaWxsIiwiZHJhZ1BsYWNlaG9sZGVyTGluZUNvbmZpZyIsImNvbG9yIiwid2lkdGgiLCJhdXRvTW92ZVdoZW5Nb3VzZUluRWRnZU9uRHJhZyIsIk9iamVjdCIsImFzc2lnbiIsIm9uIiwiaGFuZGxlRHJhZ1N0YXJ0IiwiYmluZCIsImhhbmRsZURyYWdFbmQiLCJoYW5kbGVEYXRhQ2hhbmdlIiwibm9kZSIsImRyYWdTdGF0ZSIsImlzRHJhZ2dpbmciLCJkcmFnU3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsImRyYWdOb2RlcyIsIkFycmF5IiwiaXNBcnJheSIsIm9yaWdpbmFsUG9zaXRpb25zIiwiY2xlYXIiLCJmb3JFYWNoIiwiZHJhZ05vZGUiLCJ1aWQiLCJzZXQiLCJ4IiwibGVmdCIsInkiLCJ0b3AiLCJwbGF5RHJhZ1N0YXJ0QW5pbWF0aW9uIiwiZHJhZ0luZm8iLCJkcmFnRHVyYXRpb24iLCJwbGF5RHJhZ0VuZEFuaW1hdGlvbiIsImFuaW1hdGlvbkZyYW1lSWQiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJjYW5jZWxBbmltYXRpb25GcmFtZSIsImRhdGEiLCJsb2ciLCJkYXRhVHlwZSIsImhhc0RhdGEiLCJtYXJrZG93bkNvbnRlbnQiLCJjb252ZXJ0TWluZE1hcFRvTWFya2Rvd24iLCJzYXZlTWFuYWdlciIsInNjaGVkdWxlQXV0b1NhdmUiLCJkZWJvdW5jZURlbGF5Iiwic2F2ZVR5cGUiLCJpbW1lZGlhdGUiLCJ0ZXh0Iiwid2FybiIsImNvbnZlcnROb2RlIiwibGV2ZWwiLCJwcmVmaXgiLCJyZXBlYXQiLCJyZXN1bHQiLCJjaGlsZHJlbiIsImxlbmd0aCIsImNoaWxkIiwibWFya2Rvd25SZXN1bHQiLCJ0cmltIiwidXBkYXRlQ29uZmlnIiwibmV3Q29uZmlnIiwiZ2V0RHJhZ1N0YXRlIiwiZGVzdHJveSIsImNsZWFyRmlsZVN0YXRlIiwib2ZmIiwiY29uc3RydWN0b3IiLCJNYXAiLCJnZXRJbnN0YW5jZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts\n"));

/***/ })

});
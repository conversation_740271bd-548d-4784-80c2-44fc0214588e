"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 SaveManager 集成 - 替换原有的定时器管理\n    const saveManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(_services_SaveManager__WEBPACK_IMPORTED_MODULE_9__.SaveManager.getInstance());\n    const [saveState, setSaveState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 SaveManager 状态变更回调\n    const handleSaveStateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId, state, error)=>{\n        // 只处理当前文件的状态变更\n        if ((file === null || file === void 0 ? void 0 : file.id) === fileId) {\n            setSaveState(state);\n            if (error) {\n                console.error(\"\\uD83D\\uDCBE 保存状态错误:\", error);\n            }\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 初始化 SaveManager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        // 设置保存执行回调\n        saveManager.setSaveExecuteCallback(async (fileId, data, options)=>{\n            if (onContentChange && (file === null || file === void 0 ? void 0 : file.id) === fileId) {\n                try {\n                    onContentChange(data);\n                    return true;\n                } catch (error) {\n                    console.error(\"❌ 保存执行失败:\", error);\n                    return false;\n                }\n            }\n            return false;\n        });\n        // 添加状态变更监听器\n        saveManager.addStateChangeListener(handleSaveStateChange);\n        return ()=>{\n            // 清理监听器\n            saveManager.removeStateChangeListener(handleSaveStateChange);\n        };\n    }, [\n        onContentChange,\n        file === null || file === void 0 ? void 0 : file.id,\n        handleSaveStateChange\n    ]);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        const previousFileId = saveManager.getCurrentFileId();\n        const currentFileId = (file === null || file === void 0 ? void 0 : file.id) || null;\n        // 🔧 文件切换时使用 SaveManager 处理\n        if (previousFileId !== currentFileId) {\n            saveManager.switchFile(previousFileId, currentFileId || \"\").then(()=>{\n                console.log(\"\\uD83D\\uDD04 文件切换完成:\", previousFileId, \"->\", currentFileId);\n            }).catch((error)=>{\n                console.error(\"❌ 文件切换失败:\", error);\n            });\n        }\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数 - 使用 SaveManager\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(content) {\n        let saveType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"editor\";\n        if (!settings.autoSave || !(file === null || file === void 0 ? void 0 : file.id)) return;\n        const saveManager = saveManagerRef.current;\n        saveManager.scheduleAutoSave(file.id, content, {\n            debounceDelay: settings.autoSaveDelay,\n            saveType,\n            immediate: false\n        });\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更 - 使用 SaveManager\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 基础状态验证\n        if (!(file === null || file === void 0 ? void 0 : file.id) || !isMindMapMode || !data) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 将思维导图数据转换为Markdown格式并保存\n        try {\n            const markdownContent = convertMindMapToMarkdown(data);\n            handleSave(markdownContent, \"mindmap\");\n            console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并调度保存 (文件ID:\", file.id, \")\");\n        } catch (error) {\n            console.error(\"❌ 思维导图数据转换失败:\", error);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理表格集成和资源\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, []);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 669,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 668,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    saveState !== _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 px-2 py-1 rounded text-xs\",\n                                        children: [\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.pending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-yellow-500 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400\",\n                                                        children: \"待保存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true),\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-blue-500 animate-spin border border-blue-300 border-t-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: \"保存中\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true),\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.saved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: \"已保存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true),\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-400\",\n                                                        children: \"保存失败\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 885,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 905,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 685,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 932,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 940,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 973,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 991,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 990,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1025,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1028,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 920,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 919,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1051,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1052,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1050,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1054,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1049,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1048,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1060,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1062,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1059,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1058,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1072,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1046,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1087,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1088,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1086,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1085,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1084,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1096,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1097,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1098,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1100,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1099,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1095,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1094,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1125,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1142,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1143,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1144,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1141,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1140,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1082,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1162,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1163,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1161,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1160,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1159,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1152,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1151,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 683,\n        columnNumber: 5\n    }, undefined);\n}, \"eHE3qNMPD7nMZY1MEjETOXwIYMc=\")), \"eHE3qNMPD7nMZY1MEjETOXwIYMc=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});
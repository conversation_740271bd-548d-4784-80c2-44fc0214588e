"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var lib0_mutex__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/mutex */ \"(app-pages-browser)/./node_modules/lib0/mutex.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n *\r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \n\nclass CoreManager {\n    /**\r\n   * 生成唯一标识符\r\n   */ generateUniqueId() {\n        return \"mindmap_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    /**\r\n   * 生成数据版本号\r\n   */ generateDataVersion(data) {\n        const dataString = JSON.stringify(data);\n        return \"v_\".concat(Date.now(), \"_\").concat(this.hashCode(dataString));\n    }\n    /**\r\n   * 简单哈希函数\r\n   */ hashCode(str) {\n        let hash = 0;\n        if (str.length === 0) return hash.toString();\n        for(let i = 0; i < str.length; i++){\n            const char = str.charCodeAt(i);\n            hash = (hash << 5) - hash + char;\n            hash = hash & hash; // Convert to 32bit integer\n        }\n        return Math.abs(hash).toString(36);\n    }\n    /**\r\n   * 为数据添加元数据\r\n   */ addDataMetadata(data) {\n        const dataVersion = this.generateDataVersion(data);\n        const enhancedData = {\n            ...data,\n            _metadata: {\n                dataVersion,\n                instanceId: this.instanceId,\n                timestamp: Date.now()\n            }\n        };\n        this.currentDataVersion = dataVersion;\n        return enhancedData;\n    }\n    /**\r\n   * 验证数据一致性\r\n   */ validateDataConsistency(data) {\n        if (!data._metadata) {\n            // 没有元数据的数据被认为是有效的（向后兼容）\n            return true;\n        }\n        // 检查实例ID是否匹配\n        if (data._metadata.instanceId !== this.instanceId) {\n            console.warn(\"⚠️ 数据实例ID不匹配，可能来自不同的思维导图实例\");\n            return false;\n        }\n        return true;\n    }\n    /**\r\n   * 移除数据元数据（用于传递给SimpleMindMap）\r\n   */ removeDataMetadata(data) {\n        const { _metadata, ...cleanData } = data;\n        return cleanData;\n    }\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        return new Promise((resolve, reject)=>{\n            this.initMutex(async ()=>{\n                try {\n                    // 动态导入SimpleMindMap完整版（包含所有插件，特别是Drag插件）\n                    const { default: SimpleMindMap } = await Promise.all(/*! import() */[__webpack_require__.e(\"css-node_modules_quill_dist_quill_snow_css\"), __webpack_require__.e(\"_app-pages-browser_node_modules_simple-mind-map_full_js\")]).then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map/full.js */ \"(app-pages-browser)/./node_modules/simple-mind-map/full.js\"));\n                    // 清理现有实例（使用强制销毁）\n                    if (this.mindMapInstance) {\n                        await this.forceDestroy();\n                    }\n                    // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器\n                    const finalConfig = {\n                        ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                        ...this.config.config,\n                        el: this.config.container,\n                        data: this.config.data,\n                        readonly: this.config.readonly\n                    };\n                    // 创建实例\n                    this.mindMapInstance = new SimpleMindMap(finalConfig);\n                    this.isInitialized = true;\n                    resolve(this.mindMapInstance);\n                } catch (error) {\n                    console.error(\"❌ SimpleMindMap初始化失败:\", error);\n                    const errorMessage = \"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\");\n                    reject(new Error(errorMessage));\n                }\n            });\n        });\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据（带版本控制）\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            var _enhancedData__metadata;\n            // 添加数据元数据\n            const enhancedData = this.addDataMetadata(data);\n            // 验证数据一致性\n            if (!this.validateDataConsistency(enhancedData)) {\n                console.warn(\"⚠️ 数据版本不匹配，跳过设置\");\n                return;\n            }\n            // 移除元数据后传递给SimpleMindMap\n            const cleanData = this.removeDataMetadata(enhancedData);\n            this.mindMapInstance.setData(cleanData);\n            console.log(\"✅ 数据设置成功，版本:\", (_enhancedData__metadata = enhancedData._metadata) === null || _enhancedData__metadata === void 0 ? void 0 : _enhancedData__metadata.dataVersion);\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 容器尺寸变化后调整画布\r\n   */ resize() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 先更新容器位置和尺寸信息\n            this.mindMapInstance.getElRectInfo();\n            // 然后调整画布尺寸\n            this.mindMapInstance.resize();\n        } catch (error) {\n            console.error(\"❌ 画布尺寸调整失败:\", error);\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 强制销毁实例（完整清理版本）\r\n   */ async forceDestroy() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 解绑所有事件监听器\n            if (this.mindMapInstance) {\n                try {\n                    // 尝试解绑常见的事件监听器\n                    const commonEvents = [\n                        \"data_change\",\n                        \"node_active\",\n                        \"node_tree_render_end\",\n                        \"set_data\",\n                        \"expand_btn_click\",\n                        \"node_click\",\n                        \"draw_click\",\n                        \"svg_mousedown\",\n                        \"mousedown\",\n                        \"mousemove\",\n                        \"mouseup\",\n                        \"contextmenu\"\n                    ];\n                    commonEvents.forEach((eventName)=>{\n                        try {\n                            // 使用any类型避免类型检查问题\n                            const instance = this.mindMapInstance;\n                            if (instance && typeof instance.off === \"function\") {\n                                instance.off(eventName, ()=>{});\n                            }\n                        } catch (error) {\n                        // 忽略解绑失败的事件\n                        }\n                    });\n                } catch (error) {\n                    console.warn(\"⚠️ 事件解绑失败:\", error);\n                }\n            }\n            // 销毁实例\n            this.mindMapInstance.destroy();\n            // 清理DOM引用\n            this.clearDOMReferences();\n            // 等待销毁完成\n            await this.waitForDestruction();\n            console.log(\"✅ SimpleMindMap实例强制销毁完成\");\n        } catch (error) {\n            console.error(\"❌ 强制销毁SimpleMindMap实例失败:\", error);\n            throw error;\n        } finally{\n            this.mindMapInstance = null;\n            this.isInitialized = false;\n        }\n    }\n    /**\r\n   * 清理DOM引用\r\n   */ clearDOMReferences() {\n        try {\n            // 清理容器中可能残留的SimpleMindMap相关DOM元素\n            if (this.config.container) {\n                const container = this.config.container;\n                // 移除所有子元素\n                while(container.firstChild){\n                    container.removeChild(container.firstChild);\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ DOM引用清理失败:\", error);\n        }\n    }\n    /**\r\n   * 等待销毁完成\r\n   */ async waitForDestruction() {\n        // 给予适当的等待时间确保异步销毁完成\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 100);\n        });\n    }\n    /**\r\n   * 销毁实例（保持向后兼容）\r\n   */ destroy() {\n        if (this.mindMapInstance) {\n            try {\n                this.mindMapInstance.destroy();\n                console.log(\"✅ SimpleMindMap实例销毁完成\");\n            } catch (error) {\n                console.error(\"❌ 销毁SimpleMindMap实例失败:\", error);\n            }\n        }\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.initMutex = (0,lib0_mutex__WEBPACK_IMPORTED_MODULE_1__.createMutex)();\n        // 数据版本控制\n        this.currentDataVersion = null;\n        this.instanceId = this.generateUniqueId();\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ })

});
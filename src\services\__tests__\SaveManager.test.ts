/**
 * SaveManager 测试文件
 * 验证 SaveManager 的基本功能和单例模式
 */

import { SaveManager } from '../SaveManager';
import { SaveState } from '@/types';

describe('SaveManager', () => {
  let saveManager: SaveManager;

  beforeEach(() => {
    // 获取 SaveManager 实例
    saveManager = SaveManager.getInstance();
    
    // 清理状态
    saveManager.destroy();
    
    // 重新获取实例
    saveManager = SaveManager.getInstance();
  });

  afterEach(() => {
    // 清理
    saveManager.destroy();
  });

  test('应该实现单例模式', () => {
    const instance1 = SaveManager.getInstance();
    const instance2 = SaveManager.getInstance();
    
    expect(instance1).toBe(instance2);
  });

  test('应该能设置和获取当前文件', () => {
    const fileId = 'test-file-1';
    
    saveManager.setCurrentFile(fileId);
    
    expect(saveManager.getCurrentFileId()).toBe(fileId);
  });

  test('应该能获取文件保存状态', () => {
    const fileId = 'test-file-1';
    
    // 初始状态应该为 null
    expect(saveManager.getFileSaveState(fileId)).toBeNull();
    
    // 调度保存后应该有状态
    saveManager.scheduleAutoSave(fileId, { test: 'data' });
    
    const state = saveManager.getFileSaveState(fileId);
    expect(state).not.toBeNull();
    expect(state?.fileId).toBe(fileId);
    expect(state?.state).toBe(SaveState.pending);
  });

  test('应该能添加和移除状态变更监听器', () => {
    const callback = jest.fn();
    
    saveManager.addStateChangeListener(callback);
    
    // 触发状态变更
    saveManager.scheduleAutoSave('test-file', { test: 'data' });
    
    expect(callback).toHaveBeenCalledWith('test-file', SaveState.pending);
    
    // 移除监听器
    saveManager.removeStateChangeListener(callback);
    
    // 再次触发不应该调用回调
    callback.mockClear();
    saveManager.scheduleAutoSave('test-file-2', { test: 'data' });
    
    // 由于是新文件，会触发一次，但移除的回调不会被调用
    expect(callback).not.toHaveBeenCalled();
  });

  test('应该能设置保存执行回调', () => {
    const executeCallback = jest.fn().mockResolvedValue(true);
    
    saveManager.setSaveExecuteCallback(executeCallback);
    
    // 这里只是验证设置成功，实际执行会在集成测试中验证
    expect(executeCallback).toBeDefined();
  });

  test('应该能清理文件状态', () => {
    const fileId = 'test-file-1';
    
    // 创建状态
    saveManager.scheduleAutoSave(fileId, { test: 'data' });
    expect(saveManager.getFileSaveState(fileId)).not.toBeNull();
    
    // 清理状态
    saveManager.clearFileState(fileId);
    expect(saveManager.getFileSaveState(fileId)).toBeNull();
  });

  test('应该能获取所有文件保存状态', () => {
    const fileId1 = 'test-file-1';
    const fileId2 = 'test-file-2';

    saveManager.scheduleAutoSave(fileId1, { test: 'data1' });
    saveManager.scheduleAutoSave(fileId2, { test: 'data2' });

    const allStates = saveManager.getAllFileSaveStates();

    expect(allStates.size).toBe(2);
    expect(allStates.has(fileId1)).toBe(true);
    expect(allStates.has(fileId2)).toBe(true);
  });

  test('应该能获取文件保存统计信息', () => {
    const fileId = 'test-file-1';

    // 初始状态应该返回 null
    expect(saveManager.getFileSaveStats(fileId)).toBeNull();

    // 调度保存后应该有统计信息
    saveManager.scheduleAutoSave(fileId, { test: 'data' });

    const stats = saveManager.getFileSaveStats(fileId);
    expect(stats).not.toBeNull();
    expect(stats?.currentState).toBe(SaveState.pending);
    expect(stats?.errorCount).toBe(0);
  });

  test('应该在状态变更时提供详细日志', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    const callback = jest.fn();

    saveManager.addStateChangeListener(callback);
    saveManager.scheduleAutoSave('test-file', { test: 'data' });

    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('📊'),
      expect.objectContaining({
        fileId: 'test-file',
        state: SaveState.pending,
        callbackCount: 1
      })
    );

    consoleSpy.mockRestore();
  });
});

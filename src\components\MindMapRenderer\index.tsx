/**
 * MindMapRenderer - 思维导图渲染器
 * 基于SimpleMindMap官方API的区块化实现
 * 
 * 设计理念：
 * - 严格遵循官方API最佳实践
 * - 区块化架构，职责分离
 * - 最小化封装，直接使用官方能力
 */

'use client';

import React, { useEffect, useRef, useState, useCallback, useImperativeHandle } from 'react';
import { MindMapNode, DocumentParseResult } from '../../types/mindMap';
import { CoreManager } from './managers/CoreManager';
import { EventManager } from './managers/EventManager';
import { SelectionManager } from './managers/SelectionManager';
import { StyleManager } from './managers/StyleManager';
import { DragEnhancer, DragEnhancerConfig } from './managers/DragEnhancer';
import { ClipboardManager } from './managers/ClipboardManager';
import MindMapContextMenu from './components/MindMapContextMenu';
import { MindMapConfig, MindMapTheme } from './types';
import './styles/drag-enhancements.css';

/**
 * 思维导图渲染器属性接口
 */
export interface MindMapRendererProps {
  /** 思维导图数据 */
  data?: MindMapNode;
  /** 解析结果（包含数据和元数据） */
  parseResult?: DocumentParseResult;
  /** 当前文件ID - 用于保存管理 */
  fileId?: string;
  /** 容器宽度 */
  width?: number | string;
  /** 容器高度 */
  height?: number | string;
  /** 是否只读模式 */
  readonly?: boolean;
  /** 主题配置 */
  theme?: MindMapTheme;
  /** 加载状态 */
  loading?: boolean;
  /** 错误信息 */
  error?: string;
  /** 渲染完成回调 */
  onRenderComplete?: () => void;
  /** 节点点击回调 */
  onNodeClick?: (node: any) => void;
  /** 数据变更回调 */
  onDataChange?: (data: MindMapNode) => void;
  /** 框选完成回调 */
  onSelectionComplete?: (nodes: any[]) => void;
  /** 拖拽开始回调 */
  onDragStart?: (nodes: any[]) => void;
  /** 拖拽结束回调 */
  onDragEnd?: (dragInfo: any) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义配置 */
  config?: Partial<MindMapConfig>;
  /** 拖拽增强配置 */
  dragConfig?: Partial<DragEnhancerConfig>;
  /** 是否启用拖拽增强 */
  enableDragEnhancement?: boolean;
}

/**
 * MindMapRenderer 主组件
 * 负责组件生命周期管理和Manager协调
 */
const MindMapRenderer = React.forwardRef<any, MindMapRendererProps>((props, ref) => {
  const {
    data,
    parseResult,
    fileId,
    width = '100%',
    height = '100%',
    readonly = false,
    theme = 'dark',
    loading = false,
    error,
    onRenderComplete,
    onNodeClick,
    onDataChange,
    onSelectionComplete,
    onDragStart,
    onDragEnd,
    className = '',
    config = {},
    dragConfig = {},
    enableDragEnhancement = true
  } = props;
  // DOM引用
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Manager实例引用
  const coreManagerRef = useRef<CoreManager | null>(null);
  const eventManagerRef = useRef<EventManager | null>(null);
  const selectionManagerRef = useRef<SelectionManager | null>(null);
  const styleManagerRef = useRef<StyleManager | null>(null);
  const dragEnhancerRef = useRef<DragEnhancer | null>(null);
  const clipboardManagerRef = useRef<ClipboardManager | null>(null);
  
  // 组件状态
  const [isInitialized, setIsInitialized] = useState(false);
  const [renderError, setRenderError] = useState<string | null>(null);
  const [hasPerformedInitialFit, setHasPerformedInitialFit] = useState(false);

  // 右键菜单状态
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    position: { x: 0, y: 0 },
    node: null as any
  });

  /**
   * 获取要渲染的数据
   */
  const getMindMapData = useCallback((): MindMapNode | null => {
    if (parseResult?.success && parseResult.data) {
      return parseResult.data;
    }
    return data || null;
  }, [data, parseResult]);

  /**
   * 处理右键菜单显示
   */
  const handleContextMenuShow = useCallback((position: { x: number; y: number }, node: any) => {
    setContextMenu({
      visible: true,
      position,
      node
    });
    console.log('🎯 显示右键菜单:', { position, node });
  }, []);

  /**
   * 处理右键菜单关闭
   */
  const handleContextMenuClose = useCallback(() => {
    setContextMenu({
      visible: false,
      position: { x: 0, y: 0 },
      node: null
    });
    console.log('🎯 关闭右键菜单');
  }, []);

  /**
   * 内部渲染完成处理
   */
  const handleRenderComplete = useCallback(() => {
    console.log('✅ 思维导图渲染完成');

    // 只在首次渲染完成时适应画布
    if (!hasPerformedInitialFit && coreManagerRef.current) {
      setTimeout(() => {
        try {
          coreManagerRef.current?.getInstance()?.view?.fit();
          setHasPerformedInitialFit(true);
          console.log('✅ 首次渲染完成，视图已适应画布');
        } catch (error) {
          console.error('❌ 首次适应画布失败:', error);
          setHasPerformedInitialFit(true); // 即使失败也标记为已尝试
        }
      }, 100);
    }

    // 调用外部传入的回调
    onRenderComplete?.();
  }, [hasPerformedInitialFit, onRenderComplete]);

  /**
   * 清理资源
   */
  const cleanup = useCallback(() => {
    dragEnhancerRef.current?.destroy();
    dragEnhancerRef.current = null;

    selectionManagerRef.current?.destroy();
    selectionManagerRef.current = null;

    clipboardManagerRef.current?.destroy();
    clipboardManagerRef.current = null;

    eventManagerRef.current?.destroy();
    eventManagerRef.current = null;

    styleManagerRef.current?.destroy();
    styleManagerRef.current = null;

    coreManagerRef.current?.destroy();
    coreManagerRef.current = null;

    setIsInitialized(false);
  }, []);

  /**
   * 初始化思维导图
   */
  const initializeMindMap = useCallback(async (retryCount = 0) => {
    // 确保容器元素已挂载
    if (!containerRef.current) {
      if (retryCount < 5) { // 最多重试5次
        console.warn(`⚠️ Container element not ready, retrying... (${retryCount + 1}/5)`);
        // 逐步增加延迟时间
        setTimeout(() => {
          initializeMindMap(retryCount + 1);
        }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms
        return;
      } else {
        setRenderError('Container element not found after 5 retries');
        return;
      }
    }

    const mindMapData = getMindMapData();
    if (!mindMapData) {
      setRenderError('No valid mind map data provided');
      return;
    }

    try {
      // 清理现有实例
      cleanup();



      // 1. 初始化核心管理器
      coreManagerRef.current = new CoreManager({
        container: containerRef.current,
        data: mindMapData as any, // 类型断言解决类型不兼容问题
        readonly,
        config
      });

      const mindMapInstance = await coreManagerRef.current.initialize();

      // 2. 初始化样式管理器
      styleManagerRef.current = new StyleManager(mindMapInstance, theme);
      await styleManagerRef.current.initialize();

      // 3. 初始化事件管理器
      eventManagerRef.current = new EventManager(mindMapInstance, {
        onNodeClick,
        onDataChange: onDataChange as any, // 类型断言解决类型不兼容问题
        onRenderComplete,
        onContextMenuShow: handleContextMenuShow,
        onContextMenuHide: handleContextMenuClose
      });
      eventManagerRef.current.initialize();

      // 4. 初始化剪贴板管理器
      clipboardManagerRef.current = new ClipboardManager(mindMapInstance);

      // 5. 初始化框选管理器（仅非只读模式）
      if (!readonly) {
        selectionManagerRef.current = new SelectionManager(
          mindMapInstance,
          containerRef.current,
          onSelectionComplete
        );
        selectionManagerRef.current.initialize();
      }

      // 5. 启用拖拽增强器，使用 SaveManager 统一保存机制
      if (!readonly && enableDragEnhancement) {
        const enhancedDragConfig = {
          ...dragConfig,
          persistence: {
            autoSave: dragConfig.persistence?.autoSave ?? true,
            saveDelay: dragConfig.persistence?.saveDelay ?? 1000,
            // onSave 回调已弃用，现在通过 SaveManager 统一处理
          }
        };

        dragEnhancerRef.current = new DragEnhancer(mindMapInstance, enhancedDragConfig);

        // 设置当前文件ID
        if (fileId) {
          dragEnhancerRef.current.setCurrentFileId(fileId);
        }

        dragEnhancerRef.current.initialize();
        console.log('🎯 DragEnhancer 已启用并集成 SaveManager，文件ID:', fileId);
      }

      setIsInitialized(true);
      setRenderError(null);



    } catch (err) {
      console.error('❌ 思维导图初始化失败:', err);
      setRenderError(err instanceof Error ? err.message : 'Failed to initialize mind map');
      setIsInitialized(false);
    }
  }, [
    getMindMapData,
    readonly,
    theme,
    config,
    onNodeClick,
    onDataChange,
    onRenderComplete,
    onSelectionComplete,
    onDragStart,
    onDragEnd,
    enableDragEnhancement,
    dragConfig,
    cleanup
  ]);

  /**
   * 更新思维导图数据
   */
  const updateMindMapData = useCallback(() => {
    if (!coreManagerRef.current || !isInitialized) return;

    const mindMapData = getMindMapData();
    if (mindMapData) {
      try {
        coreManagerRef.current.updateData(mindMapData as any);
        setRenderError(null);
      } catch (err) {
        console.error('Failed to update mind map data:', err);
        setRenderError('Failed to update mind map data');
      }
    }
  }, [getMindMapData, isInitialized]);

  /**
   * 手动触发画布尺寸更新
   */
  const resizeCanvas = useCallback(() => {
    if (!coreManagerRef.current || !isInitialized) {
      console.warn('⚠️ 画布未初始化，无法调整尺寸');
      return;
    }

    try {

      coreManagerRef.current.resize();
    } catch (err) {
      console.error('❌ 手动画布尺寸调整失败:', err);
    }
  }, [isInitialized]);

  // 使用 useImperativeHandle 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    resize: resizeCanvas,
    fitView: () => {
      if (coreManagerRef.current && isInitialized) {
        coreManagerRef.current.fitView();
      }
    },
    getInstance: () => coreManagerRef.current?.getInstance() || null,
    isReady: () => isInitialized,
    // 拖拽增强器相关方法
    getDragState: () => dragEnhancerRef.current?.getDragState() || null,
    updateDragConfig: (newConfig: Partial<DragEnhancerConfig>) => {
      if (dragEnhancerRef.current) {
        dragEnhancerRef.current.updateConfig(newConfig);
      }
    }
  }), [resizeCanvas, isInitialized]);

  // 组件挂载时初始化
  useEffect(() => {
    if (!loading && !error) {
      // 延迟执行确保 DOM 已渲染
      const timer = setTimeout(() => {
        initializeMindMap(0); // 从第0次重试开始
      }, 50); // 减少初始延迟
      
      return () => {
        clearTimeout(timer);
        cleanup();
      };
    }

    return cleanup;
  }, [loading, error]); // 移除函数依赖，避免不必要的重复执行

  // 添加容器尺寸监听（优化版本，避免画布自动上移）
  useEffect(() => {
    if (!containerRef.current || !isInitialized) return;

    const container = containerRef.current;
    let resizeObserver: ResizeObserver | null = null;
    let resizeTimeout: NodeJS.Timeout | null = null;
    let lastSize = { width: 0, height: 0 };

    // 使用 ResizeObserver 监听容器尺寸变化
    if ('ResizeObserver' in window) {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;

          // 只有当尺寸真正发生变化时才调整画布
          if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {
            lastSize = { width, height };

            // 清除之前的定时器
            if (resizeTimeout) {
              clearTimeout(resizeTimeout);
            }

            // 防抖处理，避免频繁调用
            resizeTimeout = setTimeout(() => {
              if (coreManagerRef.current) {
                coreManagerRef.current.resize();
              }
            }, 200); // 增加延迟时间，减少频繁调用
          }
        }
      });

      resizeObserver.observe(container);
    }

    return () => {
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [isInitialized]);

  // 数据变更时更新
  useEffect(() => {
    if (isInitialized && !loading) {
      updateMindMapData();
    }
  }, [data, parseResult, isInitialized, loading]); // 移除函数依赖

  // 监听文件ID变化，更新 DragEnhancer
  useEffect(() => {
    if (dragEnhancerRef.current && fileId) {
      dragEnhancerRef.current.setCurrentFileId(fileId);
      console.log('🔄 DragEnhancer 文件ID已更新:', fileId);
    }
  }, [fileId]);

  // 渲染加载状态
  if (loading) {
    return (
      <div 
        className={`mind-map-container loading ${className}`}
        style={{ width, height }}
      >
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4"></div>
            <p className="text-green-200 font-handwritten">正在加载思维导图...</p>
          </div>
        </div>
      </div>
    );
  }

  // 渲染错误状态
  if (error || renderError) {
    return (
      <div 
        className={`mind-map-container error ${className}`}
        style={{ width, height }}
      >
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="text-6xl mb-4">⚠️</div>
            <h3 className="text-xl font-handwritten text-amber-200 mb-2">思维导图加载失败</h3>
            <p className="text-gray-400 text-sm mb-4">{error || renderError}</p>
            <button
              onClick={() => initializeMindMap(0)}
              className="px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 渲染思维导图容器
  return (
    <div 
      ref={containerRef}
      className={`mind-map-container ${className}`}
      style={{ 
        width: '100%',
        height: '100%',
        background: '#0a0a0a',
        border: '2px solid #FFD700',
        borderRadius: '8px',
        overflow: 'hidden',
        position: 'relative',
        boxSizing: 'border-box',
        boxShadow: '0 4px 16px rgba(255, 215, 0, 0.3)'
      }}
    >
      {/* 操作提示（仅在非只读模式显示） */}
      {!readonly && isInitialized && (
        <div
          style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            background: 'rgba(255, 215, 0, 0.9)',
            color: '#000',
            padding: '6px 12px',
            borderRadius: '6px',
            fontSize: '12px',
            zIndex: 100,
            pointerEvents: 'none',
            fontFamily: 'var(--font-family-handwritten)',
            fontWeight: '600',
            border: '1px solid #FFA500'
          }}
        >
          💡 右键节点显示菜单，右键长按可框选
        </div>
      )}

      {/* 右键菜单（仅在非只读模式显示） */}
      {!readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && (
        <MindMapContextMenu
          node={contextMenu.node}
          position={contextMenu.position}
          mindMapInstance={coreManagerRef.current?.getInstance()}
          clipboardManager={clipboardManagerRef.current}
          onClose={handleContextMenuClose}
        />
      )}
    </div>
  );
});

MindMapRenderer.displayName = 'MindMapRenderer';

export default MindMapRenderer;
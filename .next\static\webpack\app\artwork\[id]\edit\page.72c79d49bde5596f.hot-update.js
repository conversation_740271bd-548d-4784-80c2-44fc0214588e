"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    // 🔧 统一的定时器管理 - 只保留一个定时器用于自动保存\n    const saveTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 统一的定时器清理函数\n    const clearSaveTimer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (saveTimeoutRef.current) {\n            clearTimeout(saveTimeoutRef.current);\n            saveTimeoutRef.current = undefined;\n            console.log(\"\\uD83D\\uDD12 清除保存定时器\");\n        }\n    }, []);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 🔧 文件切换时清除所有定时器\n        clearSaveTimer();\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        // 🔧 视图切换时清除所有定时器\n        clearSaveTimer();\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        if (!settings.autoSave || !onContentChange) return;\n        clearSaveTimer();\n        saveTimeoutRef.current = setTimeout(()=>{\n            onContentChange(content);\n        }, settings.autoSaveDelay);\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        onContentChange,\n        clearSaveTimer\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 🔧 验证当前文件状态，防止跨文件保存\n        if (!file || !isMindMapMode) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 将思维导图数据转换为Markdown格式并保存\n        if (data && onContentChange) {\n            const markdownContent = convertMindMapToMarkdown(data);\n            handleSave(markdownContent);\n            console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存\");\n        }\n    }, [\n        file,\n        isMindMapMode,\n        onContentChange,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearSaveTimer();\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, [\n        clearSaveTimer\n    ]);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 626,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 625,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 687,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 721,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 642,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 879,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 883,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 917,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 934,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 951,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 846,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 976,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 975,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 987,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 989,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 990,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 986,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 985,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 999,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 973,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1013,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1017,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1012,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1011,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1023,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1025,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1026,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1022,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1021,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1052,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1069,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1068,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1067,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1009,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1089,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1088,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1092,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1087,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1086,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1079,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1078,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 640,\n        columnNumber: 5\n    }, undefined);\n}, \"PG2CD78xuZgttSDmLmbn9ucF1P0=\")), \"PG2CD78xuZgttSDmLmbn9ucF1P0=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _dragEnhancerRef_current, _selectionManagerRef_current, _clipboardManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.destroy();\n        dragEnhancerRef.current = null;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_clipboardManagerRef_current = clipboardManagerRef.current) === null || _clipboardManagerRef_current === void 0 ? void 0 : _clipboardManagerRef_current.destroy();\n        clipboardManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 暂时禁用拖拽增强器，让SimpleMindMap原生拖拽功能正常工作\n            // TODO: 后续优化拖拽增强器与原生功能的兼容性\n            /*\r\n      if (!readonly && enableDragEnhancement) {\r\n        const enhancedDragConfig = {\r\n          ...dragConfig,\r\n          persistence: {\r\n            autoSave: dragConfig.persistence?.autoSave ?? true,\r\n            saveDelay: dragConfig.persistence?.saveDelay ?? 1000,\r\n            onSave: (data: any) => {\r\n              if (onDataChange) {\r\n                onDataChange(data);\r\n              }\r\n              if (dragConfig.persistence?.onSave) {\r\n                dragConfig.persistence.onSave(data);\r\n              }\r\n            }\r\n          }\r\n        };\r\n\r\n        dragEnhancerRef.current = new DragEnhancer(mindMapInstance, enhancedDragConfig);\r\n        dragEnhancerRef.current.initialize();\r\n      }\r\n      */ setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            }\n        }), [\n        resizeCanvas,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 462,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 485,\n        columnNumber: 5\n    }, undefined);\n}, \"n9iMgar1C7HJsnxniI5k3ycy3s4=\")), \"n9iMgar1C7HJsnxniI5k3ycy3s4=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTs7Ozs7Ozs7Q0FRQzs7QUFJNEY7QUFFeEM7QUFDRTtBQUNRO0FBQ1I7QUFFUTtBQUNFO0FBRXpCO0FBNEN4Qzs7O0NBR0MsR0FDRCxNQUFNWSxnQ0FBa0JaLEdBQUFBLHVEQUFnQixTQUE0QixDQUFDYyxPQUFPQztRQXljakRDOztJQXhjekIsTUFBTSxFQUNKQyxJQUFJLEVBQ0pDLFdBQVcsRUFDWEMsUUFBUSxNQUFNLEVBQ2RDLFNBQVMsTUFBTSxFQUNmQyxXQUFXLEtBQUssRUFDaEJDLFFBQVEsTUFBTSxFQUNkQyxVQUFVLEtBQUssRUFDZkMsS0FBSyxFQUNMQyxnQkFBZ0IsRUFDaEJDLFdBQVcsRUFDWEMsWUFBWSxFQUNaQyxtQkFBbUIsRUFDbkJDLFdBQVcsRUFDWEMsU0FBUyxFQUNUQyxZQUFZLEVBQUUsRUFDZEMsU0FBUyxDQUFDLENBQUMsRUFDWEMsYUFBYSxDQUFDLENBQUMsRUFDZkMsd0JBQXdCLElBQUksRUFDN0IsR0FBR3BCO0lBQ0osUUFBUTtJQUNSLE1BQU1xQixlQUFlakMsNkNBQU1BLENBQWlCO0lBRTVDLGNBQWM7SUFDZCxNQUFNYyxpQkFBaUJkLDZDQUFNQSxDQUFxQjtJQUNsRCxNQUFNa0Msa0JBQWtCbEMsNkNBQU1BLENBQXNCO0lBQ3BELE1BQU1tQyxzQkFBc0JuQyw2Q0FBTUEsQ0FBMEI7SUFDNUQsTUFBTW9DLGtCQUFrQnBDLDZDQUFNQSxDQUFzQjtJQUNwRCxNQUFNcUMsa0JBQWtCckMsNkNBQU1BLENBQXNCO0lBQ3BELE1BQU1zQyxzQkFBc0J0Qyw2Q0FBTUEsQ0FBMEI7SUFFNUQsT0FBTztJQUNQLE1BQU0sQ0FBQ3VDLGVBQWVDLGlCQUFpQixHQUFHdkMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDd0MsYUFBYUMsZUFBZSxHQUFHekMsK0NBQVFBLENBQWdCO0lBQzlELE1BQU0sQ0FBQzBDLHdCQUF3QkMsMEJBQTBCLEdBQUczQywrQ0FBUUEsQ0FBQztJQUVyRSxTQUFTO0lBQ1QsTUFBTSxDQUFDNEMsYUFBYUMsZUFBZSxHQUFHN0MsK0NBQVFBLENBQUM7UUFDN0M4QyxTQUFTO1FBQ1RDLFVBQVU7WUFBRUMsR0FBRztZQUFHQyxHQUFHO1FBQUU7UUFDdkJDLE1BQU07SUFDUjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsaUJBQWlCbEQsa0RBQVdBLENBQUM7UUFDakMsSUFBSWMsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhcUMsT0FBTyxLQUFJckMsWUFBWUQsSUFBSSxFQUFFO1lBQzVDLE9BQU9DLFlBQVlELElBQUk7UUFDekI7UUFDQSxPQUFPQSxRQUFRO0lBQ2pCLEdBQUc7UUFBQ0E7UUFBTUM7S0FBWTtJQUV0Qjs7R0FFQyxHQUNELE1BQU1zQyx3QkFBd0JwRCxrREFBV0EsQ0FBQyxDQUFDOEMsVUFBb0NHO1FBQzdFTCxlQUFlO1lBQ2JDLFNBQVM7WUFDVEM7WUFDQUc7UUFDRjtRQUNBSSxRQUFRQyxHQUFHLENBQUMsd0JBQWM7WUFBRVI7WUFBVUc7UUFBSztJQUM3QyxHQUFHLEVBQUU7SUFFTDs7R0FFQyxHQUNELE1BQU1NLHlCQUF5QnZELGtEQUFXQSxDQUFDO1FBQ3pDNEMsZUFBZTtZQUNiQyxTQUFTO1lBQ1RDLFVBQVU7Z0JBQUVDLEdBQUc7Z0JBQUdDLEdBQUc7WUFBRTtZQUN2QkMsTUFBTTtRQUNSO1FBQ0FJLFFBQVFDLEdBQUcsQ0FBQztJQUNkLEdBQUcsRUFBRTtJQUVMOztHQUVDLEdBQ0QsTUFBTUUsdUJBQXVCeEQsa0RBQVdBLENBQUM7UUFDdkNxRCxRQUFRQyxHQUFHLENBQUM7UUFFWixnQkFBZ0I7UUFDaEIsSUFBSSxDQUFDYiwwQkFBMEI3QixlQUFlNkMsT0FBTyxFQUFFO1lBQ3JEQyxXQUFXO2dCQUNULElBQUk7d0JBQ0Y5QywwQ0FBQUEscUNBQUFBO3FCQUFBQSwwQkFBQUEsZUFBZTZDLE9BQU8sY0FBdEI3QywrQ0FBQUEsc0NBQUFBLHdCQUF3QitDLFdBQVcsZ0JBQW5DL0MsMkRBQUFBLDJDQUFBQSxvQ0FBdUNnRCxJQUFJLGNBQTNDaEQsK0RBQUFBLHlDQUE2Q2lELEdBQUc7b0JBQ2hEbkIsMEJBQTBCO29CQUMxQlcsUUFBUUMsR0FBRyxDQUFDO2dCQUNkLEVBQUUsT0FBT2xDLE9BQU87b0JBQ2RpQyxRQUFRakMsS0FBSyxDQUFDLGVBQWVBO29CQUM3QnNCLDBCQUEwQixPQUFPLGNBQWM7Z0JBQ2pEO1lBQ0YsR0FBRztRQUNMO1FBRUEsWUFBWTtRQUNackIsNkJBQUFBLHVDQUFBQTtJQUNGLEdBQUc7UUFBQ29CO1FBQXdCcEI7S0FBaUI7SUFFN0M7O0dBRUMsR0FDRCxNQUFNeUMsVUFBVTlELGtEQUFXQSxDQUFDO1lBQzFCbUMsMEJBR0FGLDhCQUdBRyw4QkFHQUosMEJBR0FFLDBCQUdBdEI7U0FmQXVCLDJCQUFBQSxnQkFBZ0JzQixPQUFPLGNBQXZCdEIsK0NBQUFBLHlCQUF5QjRCLE9BQU87UUFDaEM1QixnQkFBZ0JzQixPQUFPLEdBQUc7U0FFMUJ4QiwrQkFBQUEsb0JBQW9Cd0IsT0FBTyxjQUEzQnhCLG1EQUFBQSw2QkFBNkI4QixPQUFPO1FBQ3BDOUIsb0JBQW9Cd0IsT0FBTyxHQUFHO1NBRTlCckIsK0JBQUFBLG9CQUFvQnFCLE9BQU8sY0FBM0JyQixtREFBQUEsNkJBQTZCMkIsT0FBTztRQUNwQzNCLG9CQUFvQnFCLE9BQU8sR0FBRztTQUU5QnpCLDJCQUFBQSxnQkFBZ0J5QixPQUFPLGNBQXZCekIsK0NBQUFBLHlCQUF5QitCLE9BQU87UUFDaEMvQixnQkFBZ0J5QixPQUFPLEdBQUc7U0FFMUJ2QiwyQkFBQUEsZ0JBQWdCdUIsT0FBTyxjQUF2QnZCLCtDQUFBQSx5QkFBeUI2QixPQUFPO1FBQ2hDN0IsZ0JBQWdCdUIsT0FBTyxHQUFHO1NBRTFCN0MsMEJBQUFBLGVBQWU2QyxPQUFPLGNBQXRCN0MsOENBQUFBLHdCQUF3Qm1ELE9BQU87UUFDL0JuRCxlQUFlNkMsT0FBTyxHQUFHO1FBRXpCbkIsaUJBQWlCO0lBQ25CLEdBQUcsRUFBRTtJQUVMOztHQUVDLEdBQ0QsTUFBTTBCLG9CQUFvQmhFLGtEQUFXQSxDQUFDO1lBQU9pRSw4RUFBYTtRQUN4RCxZQUFZO1FBQ1osSUFBSSxDQUFDbEMsYUFBYTBCLE9BQU8sRUFBRTtZQUN6QixJQUFJUSxhQUFhLEdBQUc7Z0JBQ2xCWixRQUFRYSxJQUFJLENBQUMsZ0RBQStELE9BQWZELGFBQWEsR0FBRTtnQkFDNUUsV0FBVztnQkFDWFAsV0FBVztvQkFDVE0sa0JBQWtCQyxhQUFhO2dCQUNqQyxHQUFHLE1BQU9BLENBQUFBLGFBQWEsS0FBSyxvQ0FBb0M7Z0JBQ2hFO1lBQ0YsT0FBTztnQkFDTHpCLGVBQWU7Z0JBQ2Y7WUFDRjtRQUNGO1FBRUEsTUFBTTJCLGNBQWNqQjtRQUNwQixJQUFJLENBQUNpQixhQUFhO1lBQ2hCM0IsZUFBZTtZQUNmO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsU0FBUztZQUNUc0I7WUFJQSxjQUFjO1lBQ2RsRCxlQUFlNkMsT0FBTyxHQUFHLElBQUl2RCw4REFBV0EsQ0FBQztnQkFDdkNrRSxXQUFXckMsYUFBYTBCLE9BQU87Z0JBQy9CNUMsTUFBTXNEO2dCQUNObEQ7Z0JBQ0FXO1lBQ0Y7WUFFQSxNQUFNeUMsa0JBQWtCLE1BQU16RCxlQUFlNkMsT0FBTyxDQUFDYSxVQUFVO1lBRS9ELGNBQWM7WUFDZHBDLGdCQUFnQnVCLE9BQU8sR0FBRyxJQUFJcEQsZ0VBQVlBLENBQUNnRSxpQkFBaUJuRDtZQUM1RCxNQUFNZ0IsZ0JBQWdCdUIsT0FBTyxDQUFDYSxVQUFVO1lBRXhDLGNBQWM7WUFDZHRDLGdCQUFnQnlCLE9BQU8sR0FBRyxJQUFJdEQsZ0VBQVlBLENBQUNrRSxpQkFBaUI7Z0JBQzFEL0M7Z0JBQ0FDLGNBQWNBO2dCQUNkRjtnQkFDQWtELG1CQUFtQm5CO2dCQUNuQm9CLG1CQUFtQmpCO1lBQ3JCO1lBQ0F2QixnQkFBZ0J5QixPQUFPLENBQUNhLFVBQVU7WUFFbEMsZUFBZTtZQUNmbEMsb0JBQW9CcUIsT0FBTyxHQUFHLElBQUluRCx3RUFBZ0JBLENBQUMrRDtZQUVuRCxzQkFBc0I7WUFDdEIsSUFBSSxDQUFDcEQsVUFBVTtnQkFDYmdCLG9CQUFvQndCLE9BQU8sR0FBRyxJQUFJckQsd0VBQWdCQSxDQUNoRGlFLGlCQUNBdEMsYUFBYTBCLE9BQU8sRUFDcEJqQztnQkFFRlMsb0JBQW9Cd0IsT0FBTyxDQUFDYSxVQUFVO1lBQ3hDO1lBRUEsd0NBQXdDO1lBQ3hDLDJCQUEyQjtZQUMzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O01BcUJBLEdBRUFoQyxpQkFBaUI7WUFDakJFLGVBQWU7UUFJakIsRUFBRSxPQUFPaUMsS0FBSztZQUNacEIsUUFBUWpDLEtBQUssQ0FBQyxnQkFBZ0JxRDtZQUM5QmpDLGVBQWVpQyxlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7WUFDcERyQyxpQkFBaUI7UUFDbkI7SUFDRixHQUFHO1FBQ0RZO1FBQ0FqQztRQUNBQztRQUNBVTtRQUNBTjtRQUNBQztRQUNBRjtRQUNBRztRQUNBQztRQUNBQztRQUNBSTtRQUNBRDtRQUNBaUM7S0FDRDtJQUVEOztHQUVDLEdBQ0QsTUFBTWMsb0JBQW9CNUUsa0RBQVdBLENBQUM7UUFDcEMsSUFBSSxDQUFDWSxlQUFlNkMsT0FBTyxJQUFJLENBQUNwQixlQUFlO1FBRS9DLE1BQU04QixjQUFjakI7UUFDcEIsSUFBSWlCLGFBQWE7WUFDZixJQUFJO2dCQUNGdkQsZUFBZTZDLE9BQU8sQ0FBQ29CLFVBQVUsQ0FBQ1Y7Z0JBQ2xDM0IsZUFBZTtZQUNqQixFQUFFLE9BQU9pQyxLQUFLO2dCQUNacEIsUUFBUWpDLEtBQUssQ0FBQyxtQ0FBbUNxRDtnQkFDakRqQyxlQUFlO1lBQ2pCO1FBQ0Y7SUFDRixHQUFHO1FBQUNVO1FBQWdCYjtLQUFjO0lBRWxDOztHQUVDLEdBQ0QsTUFBTXlDLGVBQWU5RSxrREFBV0EsQ0FBQztRQUMvQixJQUFJLENBQUNZLGVBQWU2QyxPQUFPLElBQUksQ0FBQ3BCLGVBQWU7WUFDN0NnQixRQUFRYSxJQUFJLENBQUM7WUFDYjtRQUNGO1FBRUEsSUFBSTtZQUVGdEQsZUFBZTZDLE9BQU8sQ0FBQ3NCLE1BQU07UUFDL0IsRUFBRSxPQUFPTixLQUFLO1lBQ1pwQixRQUFRakMsS0FBSyxDQUFDLGlCQUFpQnFEO1FBQ2pDO0lBQ0YsR0FBRztRQUFDcEM7S0FBYztJQUVsQixrQ0FBa0M7SUFDbENwQywwREFBbUJBLENBQUNVLEtBQUssSUFBTztZQUM5Qm9FLFFBQVFEO1lBQ1JFLFNBQVM7Z0JBQ1AsSUFBSXBFLGVBQWU2QyxPQUFPLElBQUlwQixlQUFlO29CQUMzQ3pCLGVBQWU2QyxPQUFPLENBQUN1QixPQUFPO2dCQUNoQztZQUNGO1lBQ0FyQixhQUFhO29CQUFNL0M7dUJBQUFBLEVBQUFBLDBCQUFBQSxlQUFlNkMsT0FBTyxjQUF0QjdDLDhDQUFBQSx3QkFBd0IrQyxXQUFXLE9BQU07O1lBQzVEc0IsU0FBUyxJQUFNNUM7WUFDZixZQUFZO1lBQ1o2QyxjQUFjO29CQUFNL0M7dUJBQUFBLEVBQUFBLDJCQUFBQSxnQkFBZ0JzQixPQUFPLGNBQXZCdEIsK0NBQUFBLHlCQUF5QitDLFlBQVksT0FBTTs7WUFDL0RDLGtCQUFrQixDQUFDQztnQkFDakIsSUFBSWpELGdCQUFnQnNCLE9BQU8sRUFBRTtvQkFDM0J0QixnQkFBZ0JzQixPQUFPLENBQUM0QixZQUFZLENBQUNEO2dCQUN2QztZQUNGO1FBQ0YsSUFBSTtRQUFDTjtRQUFjekM7S0FBYztJQUVqQyxXQUFXO0lBQ1h4QyxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ3NCLFdBQVcsQ0FBQ0MsT0FBTztZQUN0QixpQkFBaUI7WUFDakIsTUFBTWtFLFFBQVE1QixXQUFXO2dCQUN2Qk0sa0JBQWtCLElBQUksV0FBVztZQUNuQyxHQUFHLEtBQUssU0FBUztZQUVqQixPQUFPO2dCQUNMdUIsYUFBYUQ7Z0JBQ2J4QjtZQUNGO1FBQ0Y7UUFFQSxPQUFPQTtJQUNULEdBQUc7UUFBQzNDO1FBQVNDO0tBQU0sR0FBRyxvQkFBb0I7SUFFMUMsMEJBQTBCO0lBQzFCdkIsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNrQyxhQUFhMEIsT0FBTyxJQUFJLENBQUNwQixlQUFlO1FBRTdDLE1BQU0rQixZQUFZckMsYUFBYTBCLE9BQU87UUFDdEMsSUFBSStCLGlCQUF3QztRQUM1QyxJQUFJQyxnQkFBdUM7UUFDM0MsSUFBSUMsV0FBVztZQUFFM0UsT0FBTztZQUFHQyxRQUFRO1FBQUU7UUFFckMsNkJBQTZCO1FBQzdCLElBQUksb0JBQW9CMkUsUUFBUTtZQUM5QkgsaUJBQWlCLElBQUlJLGVBQWUsQ0FBQ0M7Z0JBQ25DLEtBQUssTUFBTUMsU0FBU0QsUUFBUztvQkFDM0IsTUFBTSxFQUFFOUUsS0FBSyxFQUFFQyxNQUFNLEVBQUUsR0FBRzhFLE1BQU1DLFdBQVc7b0JBRTNDLG9CQUFvQjtvQkFDcEIsSUFBSUMsS0FBS0MsR0FBRyxDQUFDbEYsUUFBUTJFLFNBQVMzRSxLQUFLLElBQUksS0FBS2lGLEtBQUtDLEdBQUcsQ0FBQ2pGLFNBQVMwRSxTQUFTMUUsTUFBTSxJQUFJLEdBQUc7d0JBQ2xGMEUsV0FBVzs0QkFBRTNFOzRCQUFPQzt3QkFBTzt3QkFFM0IsV0FBVzt3QkFDWCxJQUFJeUUsZUFBZTs0QkFDakJGLGFBQWFFO3dCQUNmO3dCQUVBLGNBQWM7d0JBQ2RBLGdCQUFnQi9CLFdBQVc7NEJBQ3pCLElBQUk5QyxlQUFlNkMsT0FBTyxFQUFFO2dDQUMxQjdDLGVBQWU2QyxPQUFPLENBQUNzQixNQUFNOzRCQUMvQjt3QkFDRixHQUFHLE1BQU0sZ0JBQWdCO29CQUMzQjtnQkFDRjtZQUNGO1lBRUFTLGVBQWVVLE9BQU8sQ0FBQzlCO1FBQ3pCO1FBRUEsT0FBTztZQUNMLElBQUlxQixlQUFlO2dCQUNqQkYsYUFBYUU7WUFDZjtZQUNBLElBQUlELGdCQUFnQjtnQkFDbEJBLGVBQWVXLFVBQVU7WUFDM0I7UUFDRjtJQUNGLEdBQUc7UUFBQzlEO0tBQWM7SUFFbEIsVUFBVTtJQUNWeEMsZ0RBQVNBLENBQUM7UUFDUixJQUFJd0MsaUJBQWlCLENBQUNsQixTQUFTO1lBQzdCeUQ7UUFDRjtJQUNGLEdBQUc7UUFBQy9EO1FBQU1DO1FBQWF1QjtRQUFlbEI7S0FBUSxHQUFHLFNBQVM7SUFFMUQsU0FBUztJQUNULElBQUlBLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2lGO1lBQ0N6RSxXQUFXLDhCQUF3QyxPQUFWQTtZQUN6QzBFLE9BQU87Z0JBQUV0RjtnQkFBT0M7WUFBTztzQkFFdkIsNEVBQUNvRjtnQkFBSXpFLFdBQVU7MEJBQ2IsNEVBQUN5RTtvQkFBSXpFLFdBQVU7O3NDQUNiLDhEQUFDeUU7NEJBQUl6RSxXQUFVOzs7Ozs7c0NBQ2YsOERBQUMyRTs0QkFBRTNFLFdBQVU7c0NBQWtDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3pEO0lBRUEsU0FBUztJQUNULElBQUlQLFNBQVNtQixhQUFhO1FBQ3hCLHFCQUNFLDhEQUFDNkQ7WUFDQ3pFLFdBQVcsNEJBQXNDLE9BQVZBO1lBQ3ZDMEUsT0FBTztnQkFBRXRGO2dCQUFPQztZQUFPO3NCQUV2Qiw0RUFBQ29GO2dCQUFJekUsV0FBVTswQkFDYiw0RUFBQ3lFO29CQUFJekUsV0FBVTs7c0NBQ2IsOERBQUN5RTs0QkFBSXpFLFdBQVU7c0NBQWdCOzs7Ozs7c0NBQy9CLDhEQUFDNEU7NEJBQUc1RSxXQUFVO3NDQUErQzs7Ozs7O3NDQUM3RCw4REFBQzJFOzRCQUFFM0UsV0FBVTtzQ0FBOEJQLFNBQVNtQjs7Ozs7O3NDQUNwRCw4REFBQ2lFOzRCQUNDQyxTQUFTLElBQU16QyxrQkFBa0I7NEJBQ2pDckMsV0FBVTtzQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU9YO0lBRUEsV0FBVztJQUNYLHFCQUNFLDhEQUFDeUU7UUFDQ3pGLEtBQUtvQjtRQUNMSixXQUFXLHNCQUFnQyxPQUFWQTtRQUNqQzBFLE9BQU87WUFDTHRGLE9BQU87WUFDUEMsUUFBUTtZQUNSMEYsWUFBWTtZQUNaQyxRQUFRO1lBQ1JDLGNBQWM7WUFDZEMsVUFBVTtZQUNWL0QsVUFBVTtZQUNWZ0UsV0FBVztZQUNYQyxXQUFXO1FBQ2I7O1lBR0MsQ0FBQzlGLFlBQVlvQiwrQkFDWiw4REFBQytEO2dCQUNDQyxPQUFPO29CQUNMdkQsVUFBVTtvQkFDVmtFLEtBQUs7b0JBQ0xDLE9BQU87b0JBQ1BQLFlBQVk7b0JBQ1pRLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RQLGNBQWM7b0JBQ2RRLFVBQVU7b0JBQ1ZDLFFBQVE7b0JBQ1JDLGVBQWU7b0JBQ2ZDLFlBQVk7b0JBQ1pDLFlBQVk7b0JBQ1piLFFBQVE7Z0JBQ1Y7MEJBQ0Q7Ozs7OztZQU1GLENBQUMxRixZQUFZMEIsWUFBWUUsT0FBTyxJQUFJRixZQUFZTSxJQUFJLElBQUliLG9CQUFvQnFCLE9BQU8sa0JBQ2xGLDhEQUFDbEQsc0VBQWtCQTtnQkFDakIwQyxNQUFNTixZQUFZTSxJQUFJO2dCQUN0QkgsVUFBVUgsWUFBWUcsUUFBUTtnQkFDOUJ1QixlQUFlLEdBQUV6RCwwQkFBQUEsZUFBZTZDLE9BQU8sY0FBdEI3Qyw4Q0FBQUEsd0JBQXdCK0MsV0FBVztnQkFDcEQ4RCxrQkFBa0JyRixvQkFBb0JxQixPQUFPO2dCQUM3Q2lFLFNBQVNuRTs7Ozs7Ozs7Ozs7O0FBS25COztBQUVBL0MsZ0JBQWdCbUgsV0FBVyxHQUFHO0FBRTlCLCtEQUFlbkgsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9NaW5kTWFwUmVuZGVyZXIvaW5kZXgudHN4PzRjNTEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIE1pbmRNYXBSZW5kZXJlciAtIOaAnee7tOWvvOWbvua4suafk+WZqFxyXG4gKiDln7rkuo5TaW1wbGVNaW5kTWFw5a6Y5pa5QVBJ55qE5Yy65Z2X5YyW5a6e546wXHJcbiAqIFxyXG4gKiDorr7orqHnkIblv7XvvJpcclxuICogLSDkuKXmoLzpgbXlvqrlrpjmlrlBUEnmnIDkvbPlrp7ot7VcclxuICogLSDljLrlnZfljJbmnrbmnoTvvIzogYzotKPliIbnprtcclxuICogLSDmnIDlsI/ljJblsIHoo4XvvIznm7TmjqXkvb/nlKjlrpjmlrnog73liptcclxuICovXHJcblxyXG4ndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlSW1wZXJhdGl2ZUhhbmRsZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgTWluZE1hcE5vZGUsIERvY3VtZW50UGFyc2VSZXN1bHQgfSBmcm9tICcuLi8uLi90eXBlcy9taW5kTWFwJztcclxuaW1wb3J0IHsgQ29yZU1hbmFnZXIgfSBmcm9tICcuL21hbmFnZXJzL0NvcmVNYW5hZ2VyJztcclxuaW1wb3J0IHsgRXZlbnRNYW5hZ2VyIH0gZnJvbSAnLi9tYW5hZ2Vycy9FdmVudE1hbmFnZXInO1xyXG5pbXBvcnQgeyBTZWxlY3Rpb25NYW5hZ2VyIH0gZnJvbSAnLi9tYW5hZ2Vycy9TZWxlY3Rpb25NYW5hZ2VyJztcclxuaW1wb3J0IHsgU3R5bGVNYW5hZ2VyIH0gZnJvbSAnLi9tYW5hZ2Vycy9TdHlsZU1hbmFnZXInO1xyXG5pbXBvcnQgeyBEcmFnRW5oYW5jZXIsIERyYWdFbmhhbmNlckNvbmZpZyB9IGZyb20gJy4vbWFuYWdlcnMvRHJhZ0VuaGFuY2VyJztcclxuaW1wb3J0IHsgQ2xpcGJvYXJkTWFuYWdlciB9IGZyb20gJy4vbWFuYWdlcnMvQ2xpcGJvYXJkTWFuYWdlcic7XHJcbmltcG9ydCBNaW5kTWFwQ29udGV4dE1lbnUgZnJvbSAnLi9jb21wb25lbnRzL01pbmRNYXBDb250ZXh0TWVudSc7XHJcbmltcG9ydCB7IE1pbmRNYXBDb25maWcsIE1pbmRNYXBUaGVtZSB9IGZyb20gJy4vdHlwZXMnO1xyXG5pbXBvcnQgJy4vc3R5bGVzL2RyYWctZW5oYW5jZW1lbnRzLmNzcyc7XHJcblxyXG4vKipcclxuICog5oCd57u05a+85Zu+5riy5p+T5Zmo5bGe5oCn5o6l5Y+jXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIE1pbmRNYXBSZW5kZXJlclByb3BzIHtcclxuICAvKiog5oCd57u05a+85Zu+5pWw5o2uICovXHJcbiAgZGF0YT86IE1pbmRNYXBOb2RlO1xyXG4gIC8qKiDop6PmnpDnu5PmnpzvvIjljIXlkKvmlbDmja7lkozlhYPmlbDmja7vvIkgKi9cclxuICBwYXJzZVJlc3VsdD86IERvY3VtZW50UGFyc2VSZXN1bHQ7XHJcbiAgLyoqIOWuueWZqOWuveW6piAqL1xyXG4gIHdpZHRoPzogbnVtYmVyIHwgc3RyaW5nO1xyXG4gIC8qKiDlrrnlmajpq5jluqYgKi9cclxuICBoZWlnaHQ/OiBudW1iZXIgfCBzdHJpbmc7XHJcbiAgLyoqIOaYr+WQpuWPquivu+aooeW8jyAqL1xyXG4gIHJlYWRvbmx5PzogYm9vbGVhbjtcclxuICAvKiog5Li76aKY6YWN572uICovXHJcbiAgdGhlbWU/OiBNaW5kTWFwVGhlbWU7XHJcbiAgLyoqIOWKoOi9veeKtuaAgSAqL1xyXG4gIGxvYWRpbmc/OiBib29sZWFuO1xyXG4gIC8qKiDplJnor6/kv6Hmga8gKi9cclxuICBlcnJvcj86IHN0cmluZztcclxuICAvKiog5riy5p+T5a6M5oiQ5Zue6LCDICovXHJcbiAgb25SZW5kZXJDb21wbGV0ZT86ICgpID0+IHZvaWQ7XHJcbiAgLyoqIOiKgueCueeCueWHu+WbnuiwgyAqL1xyXG4gIG9uTm9kZUNsaWNrPzogKG5vZGU6IGFueSkgPT4gdm9pZDtcclxuICAvKiog5pWw5o2u5Y+Y5pu05Zue6LCDICovXHJcbiAgb25EYXRhQ2hhbmdlPzogKGRhdGE6IE1pbmRNYXBOb2RlKSA9PiB2b2lkO1xyXG4gIC8qKiDmoYbpgInlrozmiJDlm57osIMgKi9cclxuICBvblNlbGVjdGlvbkNvbXBsZXRlPzogKG5vZGVzOiBhbnlbXSkgPT4gdm9pZDtcclxuICAvKiog5ouW5ou95byA5aeL5Zue6LCDICovXHJcbiAgb25EcmFnU3RhcnQ/OiAobm9kZXM6IGFueVtdKSA9PiB2b2lkO1xyXG4gIC8qKiDmi5bmi73nu5PmnZ/lm57osIMgKi9cclxuICBvbkRyYWdFbmQ/OiAoZHJhZ0luZm86IGFueSkgPT4gdm9pZDtcclxuICAvKiog6Ieq5a6a5LmJ5qC35byP57G75ZCNICovXHJcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gIC8qKiDoh6rlrprkuYnphY3nva4gKi9cclxuICBjb25maWc/OiBQYXJ0aWFsPE1pbmRNYXBDb25maWc+O1xyXG4gIC8qKiDmi5bmi73lop7lvLrphY3nva4gKi9cclxuICBkcmFnQ29uZmlnPzogUGFydGlhbDxEcmFnRW5oYW5jZXJDb25maWc+O1xyXG4gIC8qKiDmmK/lkKblkK/nlKjmi5bmi73lop7lvLogKi9cclxuICBlbmFibGVEcmFnRW5oYW5jZW1lbnQ/OiBib29sZWFuO1xyXG59XHJcblxyXG4vKipcclxuICogTWluZE1hcFJlbmRlcmVyIOS4u+e7hOS7tlxyXG4gKiDotJ/otKPnu4Tku7bnlJ/lkb3lkajmnJ/nrqHnkIblkoxNYW5hZ2Vy5Y2P6LCDXHJcbiAqL1xyXG5jb25zdCBNaW5kTWFwUmVuZGVyZXIgPSBSZWFjdC5mb3J3YXJkUmVmPGFueSwgTWluZE1hcFJlbmRlcmVyUHJvcHM+KChwcm9wcywgcmVmKSA9PiB7XHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YSxcclxuICAgIHBhcnNlUmVzdWx0LFxyXG4gICAgd2lkdGggPSAnMTAwJScsXHJcbiAgICBoZWlnaHQgPSAnMTAwJScsXHJcbiAgICByZWFkb25seSA9IGZhbHNlLFxyXG4gICAgdGhlbWUgPSAnZGFyaycsXHJcbiAgICBsb2FkaW5nID0gZmFsc2UsXHJcbiAgICBlcnJvcixcclxuICAgIG9uUmVuZGVyQ29tcGxldGUsXHJcbiAgICBvbk5vZGVDbGljayxcclxuICAgIG9uRGF0YUNoYW5nZSxcclxuICAgIG9uU2VsZWN0aW9uQ29tcGxldGUsXHJcbiAgICBvbkRyYWdTdGFydCxcclxuICAgIG9uRHJhZ0VuZCxcclxuICAgIGNsYXNzTmFtZSA9ICcnLFxyXG4gICAgY29uZmlnID0ge30sXHJcbiAgICBkcmFnQ29uZmlnID0ge30sXHJcbiAgICBlbmFibGVEcmFnRW5oYW5jZW1lbnQgPSB0cnVlXHJcbiAgfSA9IHByb3BzO1xyXG4gIC8vIERPTeW8leeUqFxyXG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcbiAgXHJcbiAgLy8gTWFuYWdlcuWunuS+i+W8leeUqFxyXG4gIGNvbnN0IGNvcmVNYW5hZ2VyUmVmID0gdXNlUmVmPENvcmVNYW5hZ2VyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgZXZlbnRNYW5hZ2VyUmVmID0gdXNlUmVmPEV2ZW50TWFuYWdlciB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHNlbGVjdGlvbk1hbmFnZXJSZWYgPSB1c2VSZWY8U2VsZWN0aW9uTWFuYWdlciB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHN0eWxlTWFuYWdlclJlZiA9IHVzZVJlZjxTdHlsZU1hbmFnZXIgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBkcmFnRW5oYW5jZXJSZWYgPSB1c2VSZWY8RHJhZ0VuaGFuY2VyIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgY2xpcGJvYXJkTWFuYWdlclJlZiA9IHVzZVJlZjxDbGlwYm9hcmRNYW5hZ2VyIHwgbnVsbD4obnVsbCk7XHJcbiAgXHJcbiAgLy8g57uE5Lu254q25oCBXHJcbiAgY29uc3QgW2lzSW5pdGlhbGl6ZWQsIHNldElzSW5pdGlhbGl6ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtyZW5kZXJFcnJvciwgc2V0UmVuZGVyRXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2hhc1BlcmZvcm1lZEluaXRpYWxGaXQsIHNldEhhc1BlcmZvcm1lZEluaXRpYWxGaXRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyDlj7PplK7oj5zljZXnirbmgIFcclxuICBjb25zdCBbY29udGV4dE1lbnUsIHNldENvbnRleHRNZW51XSA9IHVzZVN0YXRlKHtcclxuICAgIHZpc2libGU6IGZhbHNlLFxyXG4gICAgcG9zaXRpb246IHsgeDogMCwgeTogMCB9LFxyXG4gICAgbm9kZTogbnVsbCBhcyBhbnlcclxuICB9KTtcclxuXHJcbiAgLyoqXHJcbiAgICog6I635Y+W6KaB5riy5p+T55qE5pWw5o2uXHJcbiAgICovXHJcbiAgY29uc3QgZ2V0TWluZE1hcERhdGEgPSB1c2VDYWxsYmFjaygoKTogTWluZE1hcE5vZGUgfCBudWxsID0+IHtcclxuICAgIGlmIChwYXJzZVJlc3VsdD8uc3VjY2VzcyAmJiBwYXJzZVJlc3VsdC5kYXRhKSB7XHJcbiAgICAgIHJldHVybiBwYXJzZVJlc3VsdC5kYXRhO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIGRhdGEgfHwgbnVsbDtcclxuICB9LCBbZGF0YSwgcGFyc2VSZXN1bHRdKTtcclxuXHJcbiAgLyoqXHJcbiAgICog5aSE55CG5Y+z6ZSu6I+c5Y2V5pi+56S6XHJcbiAgICovXHJcbiAgY29uc3QgaGFuZGxlQ29udGV4dE1lbnVTaG93ID0gdXNlQ2FsbGJhY2soKHBvc2l0aW9uOiB7IHg6IG51bWJlcjsgeTogbnVtYmVyIH0sIG5vZGU6IGFueSkgPT4ge1xyXG4gICAgc2V0Q29udGV4dE1lbnUoe1xyXG4gICAgICB2aXNpYmxlOiB0cnVlLFxyXG4gICAgICBwb3NpdGlvbixcclxuICAgICAgbm9kZVxyXG4gICAgfSk7XHJcbiAgICBjb25zb2xlLmxvZygn8J+OryDmmL7npLrlj7PplK7oj5zljZU6JywgeyBwb3NpdGlvbiwgbm9kZSB9KTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8qKlxyXG4gICAqIOWkhOeQhuWPs+mUruiPnOWNleWFs+mXrVxyXG4gICAqL1xyXG4gIGNvbnN0IGhhbmRsZUNvbnRleHRNZW51Q2xvc2UgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBzZXRDb250ZXh0TWVudSh7XHJcbiAgICAgIHZpc2libGU6IGZhbHNlLFxyXG4gICAgICBwb3NpdGlvbjogeyB4OiAwLCB5OiAwIH0sXHJcbiAgICAgIG5vZGU6IG51bGxcclxuICAgIH0pO1xyXG4gICAgY29uc29sZS5sb2coJ/Cfjq8g5YWz6Zet5Y+z6ZSu6I+c5Y2VJyk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvKipcclxuICAgKiDlhoXpg6jmuLLmn5PlrozmiJDlpITnkIZcclxuICAgKi9cclxuICBjb25zdCBoYW5kbGVSZW5kZXJDb21wbGV0ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCfinIUg5oCd57u05a+85Zu+5riy5p+T5a6M5oiQJyk7XHJcblxyXG4gICAgLy8g5Y+q5Zyo6aaW5qyh5riy5p+T5a6M5oiQ5pe26YCC5bqU55S75biDXHJcbiAgICBpZiAoIWhhc1BlcmZvcm1lZEluaXRpYWxGaXQgJiYgY29yZU1hbmFnZXJSZWYuY3VycmVudCkge1xyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29yZU1hbmFnZXJSZWYuY3VycmVudD8uZ2V0SW5zdGFuY2UoKT8udmlldz8uZml0KCk7XHJcbiAgICAgICAgICBzZXRIYXNQZXJmb3JtZWRJbml0aWFsRml0KHRydWUpO1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDpppbmrKHmuLLmn5PlrozmiJDvvIzop4blm77lt7LpgILlupTnlLvluIMnKTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOmmluasoemAguW6lOeUu+W4g+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgICAgICBzZXRIYXNQZXJmb3JtZWRJbml0aWFsRml0KHRydWUpOyAvLyDljbPkvb/lpLHotKXkuZ/moIforrDkuLrlt7LlsJ3or5VcclxuICAgICAgICB9XHJcbiAgICAgIH0sIDEwMCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8g6LCD55So5aSW6YOo5Lyg5YWl55qE5Zue6LCDXHJcbiAgICBvblJlbmRlckNvbXBsZXRlPy4oKTtcclxuICB9LCBbaGFzUGVyZm9ybWVkSW5pdGlhbEZpdCwgb25SZW5kZXJDb21wbGV0ZV0pO1xyXG5cclxuICAvKipcclxuICAgKiDmuIXnkIbotYTmupBcclxuICAgKi9cclxuICBjb25zdCBjbGVhbnVwID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgZHJhZ0VuaGFuY2VyUmVmLmN1cnJlbnQ/LmRlc3Ryb3koKTtcclxuICAgIGRyYWdFbmhhbmNlclJlZi5jdXJyZW50ID0gbnVsbDtcclxuXHJcbiAgICBzZWxlY3Rpb25NYW5hZ2VyUmVmLmN1cnJlbnQ/LmRlc3Ryb3koKTtcclxuICAgIHNlbGVjdGlvbk1hbmFnZXJSZWYuY3VycmVudCA9IG51bGw7XHJcblxyXG4gICAgY2xpcGJvYXJkTWFuYWdlclJlZi5jdXJyZW50Py5kZXN0cm95KCk7XHJcbiAgICBjbGlwYm9hcmRNYW5hZ2VyUmVmLmN1cnJlbnQgPSBudWxsO1xyXG5cclxuICAgIGV2ZW50TWFuYWdlclJlZi5jdXJyZW50Py5kZXN0cm95KCk7XHJcbiAgICBldmVudE1hbmFnZXJSZWYuY3VycmVudCA9IG51bGw7XHJcblxyXG4gICAgc3R5bGVNYW5hZ2VyUmVmLmN1cnJlbnQ/LmRlc3Ryb3koKTtcclxuICAgIHN0eWxlTWFuYWdlclJlZi5jdXJyZW50ID0gbnVsbDtcclxuXHJcbiAgICBjb3JlTWFuYWdlclJlZi5jdXJyZW50Py5kZXN0cm95KCk7XHJcbiAgICBjb3JlTWFuYWdlclJlZi5jdXJyZW50ID0gbnVsbDtcclxuXHJcbiAgICBzZXRJc0luaXRpYWxpemVkKGZhbHNlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8qKlxyXG4gICAqIOWIneWni+WMluaAnee7tOWvvOWbvlxyXG4gICAqL1xyXG4gIGNvbnN0IGluaXRpYWxpemVNaW5kTWFwID0gdXNlQ2FsbGJhY2soYXN5bmMgKHJldHJ5Q291bnQgPSAwKSA9PiB7XHJcbiAgICAvLyDnoa7kv53lrrnlmajlhYPntKDlt7LmjILovb1cclxuICAgIGlmICghY29udGFpbmVyUmVmLmN1cnJlbnQpIHtcclxuICAgICAgaWYgKHJldHJ5Q291bnQgPCA1KSB7IC8vIOacgOWkmumHjeivlTXmrKFcclxuICAgICAgICBjb25zb2xlLndhcm4oYOKaoO+4jyBDb250YWluZXIgZWxlbWVudCBub3QgcmVhZHksIHJldHJ5aW5nLi4uICgke3JldHJ5Q291bnQgKyAxfS81KWApO1xyXG4gICAgICAgIC8vIOmAkOatpeWinuWKoOW7tui/n+aXtumXtFxyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgaW5pdGlhbGl6ZU1pbmRNYXAocmV0cnlDb3VudCArIDEpO1xyXG4gICAgICAgIH0sIDEwMCAqIChyZXRyeUNvdW50ICsgMSkpOyAvLyAxMDBtcywgMjAwbXMsIDMwMG1zLCA0MDBtcywgNTAwbXNcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0UmVuZGVyRXJyb3IoJ0NvbnRhaW5lciBlbGVtZW50IG5vdCBmb3VuZCBhZnRlciA1IHJldHJpZXMnKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBtaW5kTWFwRGF0YSA9IGdldE1pbmRNYXBEYXRhKCk7XHJcbiAgICBpZiAoIW1pbmRNYXBEYXRhKSB7XHJcbiAgICAgIHNldFJlbmRlckVycm9yKCdObyB2YWxpZCBtaW5kIG1hcCBkYXRhIHByb3ZpZGVkJyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDmuIXnkIbnjrDmnInlrp7kvotcclxuICAgICAgY2xlYW51cCgpO1xyXG5cclxuXHJcblxyXG4gICAgICAvLyAxLiDliJ3lp4vljJbmoLjlv4PnrqHnkIblmahcclxuICAgICAgY29yZU1hbmFnZXJSZWYuY3VycmVudCA9IG5ldyBDb3JlTWFuYWdlcih7XHJcbiAgICAgICAgY29udGFpbmVyOiBjb250YWluZXJSZWYuY3VycmVudCxcclxuICAgICAgICBkYXRhOiBtaW5kTWFwRGF0YSBhcyBhbnksIC8vIOexu+Wei+aWreiogOino+WGs+exu+Wei+S4jeWFvOWuuemXrumimFxyXG4gICAgICAgIHJlYWRvbmx5LFxyXG4gICAgICAgIGNvbmZpZ1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnN0IG1pbmRNYXBJbnN0YW5jZSA9IGF3YWl0IGNvcmVNYW5hZ2VyUmVmLmN1cnJlbnQuaW5pdGlhbGl6ZSgpO1xyXG5cclxuICAgICAgLy8gMi4g5Yid5aeL5YyW5qC35byP566h55CG5ZmoXHJcbiAgICAgIHN0eWxlTWFuYWdlclJlZi5jdXJyZW50ID0gbmV3IFN0eWxlTWFuYWdlcihtaW5kTWFwSW5zdGFuY2UsIHRoZW1lKTtcclxuICAgICAgYXdhaXQgc3R5bGVNYW5hZ2VyUmVmLmN1cnJlbnQuaW5pdGlhbGl6ZSgpO1xyXG5cclxuICAgICAgLy8gMy4g5Yid5aeL5YyW5LqL5Lu2566h55CG5ZmoXHJcbiAgICAgIGV2ZW50TWFuYWdlclJlZi5jdXJyZW50ID0gbmV3IEV2ZW50TWFuYWdlcihtaW5kTWFwSW5zdGFuY2UsIHtcclxuICAgICAgICBvbk5vZGVDbGljayxcclxuICAgICAgICBvbkRhdGFDaGFuZ2U6IG9uRGF0YUNoYW5nZSBhcyBhbnksIC8vIOexu+Wei+aWreiogOino+WGs+exu+Wei+S4jeWFvOWuuemXrumimFxyXG4gICAgICAgIG9uUmVuZGVyQ29tcGxldGUsXHJcbiAgICAgICAgb25Db250ZXh0TWVudVNob3c6IGhhbmRsZUNvbnRleHRNZW51U2hvdyxcclxuICAgICAgICBvbkNvbnRleHRNZW51SGlkZTogaGFuZGxlQ29udGV4dE1lbnVDbG9zZVxyXG4gICAgICB9KTtcclxuICAgICAgZXZlbnRNYW5hZ2VyUmVmLmN1cnJlbnQuaW5pdGlhbGl6ZSgpO1xyXG5cclxuICAgICAgLy8gNC4g5Yid5aeL5YyW5Ymq6LS05p2/566h55CG5ZmoXHJcbiAgICAgIGNsaXBib2FyZE1hbmFnZXJSZWYuY3VycmVudCA9IG5ldyBDbGlwYm9hcmRNYW5hZ2VyKG1pbmRNYXBJbnN0YW5jZSk7XHJcblxyXG4gICAgICAvLyA1LiDliJ3lp4vljJbmoYbpgInnrqHnkIblmajvvIjku4XpnZ7lj6ror7vmqKHlvI/vvIlcclxuICAgICAgaWYgKCFyZWFkb25seSkge1xyXG4gICAgICAgIHNlbGVjdGlvbk1hbmFnZXJSZWYuY3VycmVudCA9IG5ldyBTZWxlY3Rpb25NYW5hZ2VyKFxyXG4gICAgICAgICAgbWluZE1hcEluc3RhbmNlLFxyXG4gICAgICAgICAgY29udGFpbmVyUmVmLmN1cnJlbnQsXHJcbiAgICAgICAgICBvblNlbGVjdGlvbkNvbXBsZXRlXHJcbiAgICAgICAgKTtcclxuICAgICAgICBzZWxlY3Rpb25NYW5hZ2VyUmVmLmN1cnJlbnQuaW5pdGlhbGl6ZSgpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyA1LiDmmoLml7bnpoHnlKjmi5bmi73lop7lvLrlmajvvIzorqlTaW1wbGVNaW5kTWFw5Y6f55Sf5ouW5ou95Yqf6IO95q2j5bi45bel5L2cXHJcbiAgICAgIC8vIFRPRE86IOWQjue7reS8mOWMluaLluaLveWinuW8uuWZqOS4juWOn+eUn+WKn+iDveeahOWFvOWuueaAp1xyXG4gICAgICAvKlxyXG4gICAgICBpZiAoIXJlYWRvbmx5ICYmIGVuYWJsZURyYWdFbmhhbmNlbWVudCkge1xyXG4gICAgICAgIGNvbnN0IGVuaGFuY2VkRHJhZ0NvbmZpZyA9IHtcclxuICAgICAgICAgIC4uLmRyYWdDb25maWcsXHJcbiAgICAgICAgICBwZXJzaXN0ZW5jZToge1xyXG4gICAgICAgICAgICBhdXRvU2F2ZTogZHJhZ0NvbmZpZy5wZXJzaXN0ZW5jZT8uYXV0b1NhdmUgPz8gdHJ1ZSxcclxuICAgICAgICAgICAgc2F2ZURlbGF5OiBkcmFnQ29uZmlnLnBlcnNpc3RlbmNlPy5zYXZlRGVsYXkgPz8gMTAwMCxcclxuICAgICAgICAgICAgb25TYXZlOiAoZGF0YTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgaWYgKG9uRGF0YUNoYW5nZSkge1xyXG4gICAgICAgICAgICAgICAgb25EYXRhQ2hhbmdlKGRhdGEpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBpZiAoZHJhZ0NvbmZpZy5wZXJzaXN0ZW5jZT8ub25TYXZlKSB7XHJcbiAgICAgICAgICAgICAgICBkcmFnQ29uZmlnLnBlcnNpc3RlbmNlLm9uU2F2ZShkYXRhKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICBkcmFnRW5oYW5jZXJSZWYuY3VycmVudCA9IG5ldyBEcmFnRW5oYW5jZXIobWluZE1hcEluc3RhbmNlLCBlbmhhbmNlZERyYWdDb25maWcpO1xyXG4gICAgICAgIGRyYWdFbmhhbmNlclJlZi5jdXJyZW50LmluaXRpYWxpemUoKTtcclxuICAgICAgfVxyXG4gICAgICAqL1xyXG5cclxuICAgICAgc2V0SXNJbml0aWFsaXplZCh0cnVlKTtcclxuICAgICAgc2V0UmVuZGVyRXJyb3IobnVsbCk7XHJcblxyXG5cclxuXHJcbiAgICB9IGNhdGNoIChlcnIpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIOaAnee7tOWvvOWbvuWIneWni+WMluWksei0pTonLCBlcnIpO1xyXG4gICAgICBzZXRSZW5kZXJFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBpbml0aWFsaXplIG1pbmQgbWFwJyk7XHJcbiAgICAgIHNldElzSW5pdGlhbGl6ZWQoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH0sIFtcclxuICAgIGdldE1pbmRNYXBEYXRhLFxyXG4gICAgcmVhZG9ubHksXHJcbiAgICB0aGVtZSxcclxuICAgIGNvbmZpZyxcclxuICAgIG9uTm9kZUNsaWNrLFxyXG4gICAgb25EYXRhQ2hhbmdlLFxyXG4gICAgb25SZW5kZXJDb21wbGV0ZSxcclxuICAgIG9uU2VsZWN0aW9uQ29tcGxldGUsXHJcbiAgICBvbkRyYWdTdGFydCxcclxuICAgIG9uRHJhZ0VuZCxcclxuICAgIGVuYWJsZURyYWdFbmhhbmNlbWVudCxcclxuICAgIGRyYWdDb25maWcsXHJcbiAgICBjbGVhbnVwXHJcbiAgXSk7XHJcblxyXG4gIC8qKlxyXG4gICAqIOabtOaWsOaAnee7tOWvvOWbvuaVsOaNrlxyXG4gICAqL1xyXG4gIGNvbnN0IHVwZGF0ZU1pbmRNYXBEYXRhID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgaWYgKCFjb3JlTWFuYWdlclJlZi5jdXJyZW50IHx8ICFpc0luaXRpYWxpemVkKSByZXR1cm47XHJcblxyXG4gICAgY29uc3QgbWluZE1hcERhdGEgPSBnZXRNaW5kTWFwRGF0YSgpO1xyXG4gICAgaWYgKG1pbmRNYXBEYXRhKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29yZU1hbmFnZXJSZWYuY3VycmVudC51cGRhdGVEYXRhKG1pbmRNYXBEYXRhIGFzIGFueSk7XHJcbiAgICAgICAgc2V0UmVuZGVyRXJyb3IobnVsbCk7XHJcbiAgICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgbWluZCBtYXAgZGF0YTonLCBlcnIpO1xyXG4gICAgICAgIHNldFJlbmRlckVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIG1pbmQgbWFwIGRhdGEnKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFtnZXRNaW5kTWFwRGF0YSwgaXNJbml0aWFsaXplZF0pO1xyXG5cclxuICAvKipcclxuICAgKiDmiYvliqjop6blj5HnlLvluIPlsLrlr7jmm7TmlrBcclxuICAgKi9cclxuICBjb25zdCByZXNpemVDYW52YXMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBpZiAoIWNvcmVNYW5hZ2VyUmVmLmN1cnJlbnQgfHwgIWlzSW5pdGlhbGl6ZWQpIHtcclxuICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g55S75biD5pyq5Yid5aeL5YyW77yM5peg5rOV6LCD5pW05bC65a+4Jyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG5cclxuICAgICAgY29yZU1hbmFnZXJSZWYuY3VycmVudC5yZXNpemUoKTtcclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5omL5Yqo55S75biD5bC65a+46LCD5pW05aSx6LSlOicsIGVycik7XHJcbiAgICB9XHJcbiAgfSwgW2lzSW5pdGlhbGl6ZWRdKTtcclxuXHJcbiAgLy8g5L2/55SoIHVzZUltcGVyYXRpdmVIYW5kbGUg5pq06Zyy5pa55rOV57uZ54i257uE5Lu2XHJcbiAgdXNlSW1wZXJhdGl2ZUhhbmRsZShyZWYsICgpID0+ICh7XHJcbiAgICByZXNpemU6IHJlc2l6ZUNhbnZhcyxcclxuICAgIGZpdFZpZXc6ICgpID0+IHtcclxuICAgICAgaWYgKGNvcmVNYW5hZ2VyUmVmLmN1cnJlbnQgJiYgaXNJbml0aWFsaXplZCkge1xyXG4gICAgICAgIGNvcmVNYW5hZ2VyUmVmLmN1cnJlbnQuZml0VmlldygpO1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgZ2V0SW5zdGFuY2U6ICgpID0+IGNvcmVNYW5hZ2VyUmVmLmN1cnJlbnQ/LmdldEluc3RhbmNlKCkgfHwgbnVsbCxcclxuICAgIGlzUmVhZHk6ICgpID0+IGlzSW5pdGlhbGl6ZWQsXHJcbiAgICAvLyDmi5bmi73lop7lvLrlmajnm7jlhbPmlrnms5VcclxuICAgIGdldERyYWdTdGF0ZTogKCkgPT4gZHJhZ0VuaGFuY2VyUmVmLmN1cnJlbnQ/LmdldERyYWdTdGF0ZSgpIHx8IG51bGwsXHJcbiAgICB1cGRhdGVEcmFnQ29uZmlnOiAobmV3Q29uZmlnOiBQYXJ0aWFsPERyYWdFbmhhbmNlckNvbmZpZz4pID0+IHtcclxuICAgICAgaWYgKGRyYWdFbmhhbmNlclJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgZHJhZ0VuaGFuY2VyUmVmLmN1cnJlbnQudXBkYXRlQ29uZmlnKG5ld0NvbmZpZyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9KSwgW3Jlc2l6ZUNhbnZhcywgaXNJbml0aWFsaXplZF0pO1xyXG5cclxuICAvLyDnu4Tku7bmjILovb3ml7bliJ3lp4vljJZcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKCFsb2FkaW5nICYmICFlcnJvcikge1xyXG4gICAgICAvLyDlu7bov5/miafooYznoa7kv50gRE9NIOW3sua4suafk1xyXG4gICAgICBjb25zdCB0aW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIGluaXRpYWxpemVNaW5kTWFwKDApOyAvLyDku47nrKww5qyh6YeN6K+V5byA5aeLXHJcbiAgICAgIH0sIDUwKTsgLy8g5YeP5bCR5Yid5aeL5bu26L+fXHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lcik7XHJcbiAgICAgICAgY2xlYW51cCgpO1xyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBjbGVhbnVwO1xyXG4gIH0sIFtsb2FkaW5nLCBlcnJvcl0pOyAvLyDnp7vpmaTlh73mlbDkvp3otZbvvIzpgb/lhY3kuI3lv4XopoHnmoTph43lpI3miafooYxcclxuXHJcbiAgLy8g5re75Yqg5a655Zmo5bC65a+455uR5ZCs77yI5LyY5YyW54mI5pys77yM6YG/5YWN55S75biD6Ieq5Yqo5LiK56e777yJXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghY29udGFpbmVyUmVmLmN1cnJlbnQgfHwgIWlzSW5pdGlhbGl6ZWQpIHJldHVybjtcclxuXHJcbiAgICBjb25zdCBjb250YWluZXIgPSBjb250YWluZXJSZWYuY3VycmVudDtcclxuICAgIGxldCByZXNpemVPYnNlcnZlcjogUmVzaXplT2JzZXJ2ZXIgfCBudWxsID0gbnVsbDtcclxuICAgIGxldCByZXNpemVUaW1lb3V0OiBOb2RlSlMuVGltZW91dCB8IG51bGwgPSBudWxsO1xyXG4gICAgbGV0IGxhc3RTaXplID0geyB3aWR0aDogMCwgaGVpZ2h0OiAwIH07XHJcblxyXG4gICAgLy8g5L2/55SoIFJlc2l6ZU9ic2VydmVyIOebkeWQrOWuueWZqOWwuuWvuOWPmOWMllxyXG4gICAgaWYgKCdSZXNpemVPYnNlcnZlcicgaW4gd2luZG93KSB7XHJcbiAgICAgIHJlc2l6ZU9ic2VydmVyID0gbmV3IFJlc2l6ZU9ic2VydmVyKChlbnRyaWVzKSA9PiB7XHJcbiAgICAgICAgZm9yIChjb25zdCBlbnRyeSBvZiBlbnRyaWVzKSB7XHJcbiAgICAgICAgICBjb25zdCB7IHdpZHRoLCBoZWlnaHQgfSA9IGVudHJ5LmNvbnRlbnRSZWN0O1xyXG5cclxuICAgICAgICAgIC8vIOWPquacieW9k+WwuuWvuOecn+ato+WPkeeUn+WPmOWMluaXtuaJjeiwg+aVtOeUu+W4g1xyXG4gICAgICAgICAgaWYgKE1hdGguYWJzKHdpZHRoIC0gbGFzdFNpemUud2lkdGgpID4gMSB8fCBNYXRoLmFicyhoZWlnaHQgLSBsYXN0U2l6ZS5oZWlnaHQpID4gMSkge1xyXG4gICAgICAgICAgICBsYXN0U2l6ZSA9IHsgd2lkdGgsIGhlaWdodCB9O1xyXG5cclxuICAgICAgICAgICAgLy8g5riF6Zmk5LmL5YmN55qE5a6a5pe25ZmoXHJcbiAgICAgICAgICAgIGlmIChyZXNpemVUaW1lb3V0KSB7XHJcbiAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHJlc2l6ZVRpbWVvdXQpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyDpmLLmipblpITnkIbvvIzpgb/lhY3popHnuYHosIPnlKhcclxuICAgICAgICAgICAgcmVzaXplVGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgIGlmIChjb3JlTWFuYWdlclJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgICAgICBjb3JlTWFuYWdlclJlZi5jdXJyZW50LnJlc2l6ZSgpO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSwgMjAwKTsgLy8g5aKe5Yqg5bu26L+f5pe26Ze077yM5YeP5bCR6aKR57mB6LCD55SoXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHJlc2l6ZU9ic2VydmVyLm9ic2VydmUoY29udGFpbmVyKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpZiAocmVzaXplVGltZW91dCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dChyZXNpemVUaW1lb3V0KTtcclxuICAgICAgfVxyXG4gICAgICBpZiAocmVzaXplT2JzZXJ2ZXIpIHtcclxuICAgICAgICByZXNpemVPYnNlcnZlci5kaXNjb25uZWN0KCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSwgW2lzSW5pdGlhbGl6ZWRdKTtcclxuXHJcbiAgLy8g5pWw5o2u5Y+Y5pu05pe25pu05pawXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpc0luaXRpYWxpemVkICYmICFsb2FkaW5nKSB7XHJcbiAgICAgIHVwZGF0ZU1pbmRNYXBEYXRhKCk7XHJcbiAgICB9XHJcbiAgfSwgW2RhdGEsIHBhcnNlUmVzdWx0LCBpc0luaXRpYWxpemVkLCBsb2FkaW5nXSk7IC8vIOenu+mZpOWHveaVsOS+nei1llxyXG5cclxuICAvLyDmuLLmn5PliqDovb3nirbmgIFcclxuICBpZiAobG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBcclxuICAgICAgICBjbGFzc05hbWU9e2BtaW5kLW1hcC1jb250YWluZXIgbG9hZGluZyAke2NsYXNzTmFtZX1gfVxyXG4gICAgICAgIHN0eWxlPXt7IHdpZHRoLCBoZWlnaHQgfX1cclxuICAgICAgPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItZ3JlZW4tNTAwIG1iLTRcIj48L2Rpdj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0yMDAgZm9udC1oYW5kd3JpdHRlblwiPuato+WcqOWKoOi9veaAnee7tOWvvOWbvi4uLjwvcD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAvLyDmuLLmn5PplJnor6/nirbmgIFcclxuICBpZiAoZXJyb3IgfHwgcmVuZGVyRXJyb3IpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgXHJcbiAgICAgICAgY2xhc3NOYW1lPXtgbWluZC1tYXAtY29udGFpbmVyIGVycm9yICR7Y2xhc3NOYW1lfWB9XHJcbiAgICAgICAgc3R5bGU9e3sgd2lkdGgsIGhlaWdodCB9fVxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLWZ1bGxcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBtYi00XCI+4pqg77iPPC9kaXY+XHJcbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtaGFuZHdyaXR0ZW4gdGV4dC1hbWJlci0yMDAgbWItMlwiPuaAnee7tOWvvOWbvuWKoOi9veWksei0pTwvaDM+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBtYi00XCI+e2Vycm9yIHx8IHJlbmRlckVycm9yfTwvcD5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGluaXRpYWxpemVNaW5kTWFwKDApfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1ncmVlbi02MDAvMzAgdGV4dC1ncmVlbi0yMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmVlbi02MDAvNDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGZvbnQtaGFuZHdyaXR0ZW5cIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAg6YeN6K+VXHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIC8vIOa4suafk+aAnee7tOWvvOWbvuWuueWZqFxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IFxyXG4gICAgICByZWY9e2NvbnRhaW5lclJlZn1cclxuICAgICAgY2xhc3NOYW1lPXtgbWluZC1tYXAtY29udGFpbmVyICR7Y2xhc3NOYW1lfWB9XHJcbiAgICAgIHN0eWxlPXt7IFxyXG4gICAgICAgIHdpZHRoOiAnMTAwJScsXHJcbiAgICAgICAgaGVpZ2h0OiAnMTAwJScsXHJcbiAgICAgICAgYmFja2dyb3VuZDogJyMwYTBhMGEnLFxyXG4gICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCAjRkZENzAwJyxcclxuICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxyXG4gICAgICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcclxuICAgICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcclxuICAgICAgICBib3hTaXppbmc6ICdib3JkZXItYm94JyxcclxuICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxNnB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMyknXHJcbiAgICAgIH19XHJcbiAgICA+XHJcbiAgICAgIHsvKiDmk43kvZzmj5DnpLrvvIjku4XlnKjpnZ7lj6ror7vmqKHlvI/mmL7npLrvvIkgKi99XHJcbiAgICAgIHshcmVhZG9ubHkgJiYgaXNJbml0aWFsaXplZCAmJiAoXHJcbiAgICAgICAgPGRpdlxyXG4gICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXHJcbiAgICAgICAgICAgIHRvcDogJzEwcHgnLFxyXG4gICAgICAgICAgICByaWdodDogJzEwcHgnLFxyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsIDIxNSwgMCwgMC45KScsXHJcbiAgICAgICAgICAgIGNvbG9yOiAnIzAwMCcsXHJcbiAgICAgICAgICAgIHBhZGRpbmc6ICc2cHggMTJweCcsXHJcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzZweCcsXHJcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMTJweCcsXHJcbiAgICAgICAgICAgIHpJbmRleDogMTAwLFxyXG4gICAgICAgICAgICBwb2ludGVyRXZlbnRzOiAnbm9uZScsXHJcbiAgICAgICAgICAgIGZvbnRGYW1pbHk6ICd2YXIoLS1mb250LWZhbWlseS1oYW5kd3JpdHRlbiknLFxyXG4gICAgICAgICAgICBmb250V2VpZ2h0OiAnNjAwJyxcclxuICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICNGRkE1MDAnXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgID5cclxuICAgICAgICAgIPCfkqEg5Y+z6ZSu6IqC54K55pi+56S66I+c5Y2V77yM5Y+z6ZSu6ZW/5oyJ5Y+v5qGG6YCJXHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7Lyog5Y+z6ZSu6I+c5Y2V77yI5LuF5Zyo6Z2e5Y+q6K+75qih5byP5pi+56S677yJICovfVxyXG4gICAgICB7IXJlYWRvbmx5ICYmIGNvbnRleHRNZW51LnZpc2libGUgJiYgY29udGV4dE1lbnUubm9kZSAmJiBjbGlwYm9hcmRNYW5hZ2VyUmVmLmN1cnJlbnQgJiYgKFxyXG4gICAgICAgIDxNaW5kTWFwQ29udGV4dE1lbnVcclxuICAgICAgICAgIG5vZGU9e2NvbnRleHRNZW51Lm5vZGV9XHJcbiAgICAgICAgICBwb3NpdGlvbj17Y29udGV4dE1lbnUucG9zaXRpb259XHJcbiAgICAgICAgICBtaW5kTWFwSW5zdGFuY2U9e2NvcmVNYW5hZ2VyUmVmLmN1cnJlbnQ/LmdldEluc3RhbmNlKCl9XHJcbiAgICAgICAgICBjbGlwYm9hcmRNYW5hZ2VyPXtjbGlwYm9hcmRNYW5hZ2VyUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICBvbkNsb3NlPXtoYW5kbGVDb250ZXh0TWVudUNsb3NlfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbk1pbmRNYXBSZW5kZXJlci5kaXNwbGF5TmFtZSA9ICdNaW5kTWFwUmVuZGVyZXInO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgTWluZE1hcFJlbmRlcmVyOyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VJbXBlcmF0aXZlSGFuZGxlIiwiQ29yZU1hbmFnZXIiLCJFdmVudE1hbmFnZXIiLCJTZWxlY3Rpb25NYW5hZ2VyIiwiU3R5bGVNYW5hZ2VyIiwiQ2xpcGJvYXJkTWFuYWdlciIsIk1pbmRNYXBDb250ZXh0TWVudSIsIk1pbmRNYXBSZW5kZXJlciIsImZvcndhcmRSZWYiLCJwcm9wcyIsInJlZiIsImNvcmVNYW5hZ2VyUmVmIiwiZGF0YSIsInBhcnNlUmVzdWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJyZWFkb25seSIsInRoZW1lIiwibG9hZGluZyIsImVycm9yIiwib25SZW5kZXJDb21wbGV0ZSIsIm9uTm9kZUNsaWNrIiwib25EYXRhQ2hhbmdlIiwib25TZWxlY3Rpb25Db21wbGV0ZSIsIm9uRHJhZ1N0YXJ0Iiwib25EcmFnRW5kIiwiY2xhc3NOYW1lIiwiY29uZmlnIiwiZHJhZ0NvbmZpZyIsImVuYWJsZURyYWdFbmhhbmNlbWVudCIsImNvbnRhaW5lclJlZiIsImV2ZW50TWFuYWdlclJlZiIsInNlbGVjdGlvbk1hbmFnZXJSZWYiLCJzdHlsZU1hbmFnZXJSZWYiLCJkcmFnRW5oYW5jZXJSZWYiLCJjbGlwYm9hcmRNYW5hZ2VyUmVmIiwiaXNJbml0aWFsaXplZCIsInNldElzSW5pdGlhbGl6ZWQiLCJyZW5kZXJFcnJvciIsInNldFJlbmRlckVycm9yIiwiaGFzUGVyZm9ybWVkSW5pdGlhbEZpdCIsInNldEhhc1BlcmZvcm1lZEluaXRpYWxGaXQiLCJjb250ZXh0TWVudSIsInNldENvbnRleHRNZW51IiwidmlzaWJsZSIsInBvc2l0aW9uIiwieCIsInkiLCJub2RlIiwiZ2V0TWluZE1hcERhdGEiLCJzdWNjZXNzIiwiaGFuZGxlQ29udGV4dE1lbnVTaG93IiwiY29uc29sZSIsImxvZyIsImhhbmRsZUNvbnRleHRNZW51Q2xvc2UiLCJoYW5kbGVSZW5kZXJDb21wbGV0ZSIsImN1cnJlbnQiLCJzZXRUaW1lb3V0IiwiZ2V0SW5zdGFuY2UiLCJ2aWV3IiwiZml0IiwiY2xlYW51cCIsImRlc3Ryb3kiLCJpbml0aWFsaXplTWluZE1hcCIsInJldHJ5Q291bnQiLCJ3YXJuIiwibWluZE1hcERhdGEiLCJjb250YWluZXIiLCJtaW5kTWFwSW5zdGFuY2UiLCJpbml0aWFsaXplIiwib25Db250ZXh0TWVudVNob3ciLCJvbkNvbnRleHRNZW51SGlkZSIsImVyciIsIkVycm9yIiwibWVzc2FnZSIsInVwZGF0ZU1pbmRNYXBEYXRhIiwidXBkYXRlRGF0YSIsInJlc2l6ZUNhbnZhcyIsInJlc2l6ZSIsImZpdFZpZXciLCJpc1JlYWR5IiwiZ2V0RHJhZ1N0YXRlIiwidXBkYXRlRHJhZ0NvbmZpZyIsIm5ld0NvbmZpZyIsInVwZGF0ZUNvbmZpZyIsInRpbWVyIiwiY2xlYXJUaW1lb3V0IiwicmVzaXplT2JzZXJ2ZXIiLCJyZXNpemVUaW1lb3V0IiwibGFzdFNpemUiLCJ3aW5kb3ciLCJSZXNpemVPYnNlcnZlciIsImVudHJpZXMiLCJlbnRyeSIsImNvbnRlbnRSZWN0IiwiTWF0aCIsImFicyIsIm9ic2VydmUiLCJkaXNjb25uZWN0IiwiZGl2Iiwic3R5bGUiLCJwIiwiaDMiLCJidXR0b24iLCJvbkNsaWNrIiwiYmFja2dyb3VuZCIsImJvcmRlciIsImJvcmRlclJhZGl1cyIsIm92ZXJmbG93IiwiYm94U2l6aW5nIiwiYm94U2hhZG93IiwidG9wIiwicmlnaHQiLCJjb2xvciIsInBhZGRpbmciLCJmb250U2l6ZSIsInpJbmRleCIsInBvaW50ZXJFdmVudHMiLCJmb250RmFtaWx5IiwiZm9udFdlaWdodCIsImNsaXBib2FyZE1hbmFnZXIiLCJvbkNsb3NlIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var lib0_mutex__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/mutex */ \"(app-pages-browser)/./node_modules/lib0/mutex.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n *\r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \n\nclass CoreManager {\n    /**\r\n   * 生成唯一标识符\r\n   */ generateUniqueId() {\n        return \"mindmap_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    /**\r\n   * 生成数据版本号\r\n   */ generateDataVersion(data) {\n        const dataString = JSON.stringify(data);\n        return \"v_\".concat(Date.now(), \"_\").concat(this.hashCode(dataString));\n    }\n    /**\r\n   * 简单哈希函数\r\n   */ hashCode(str) {\n        let hash = 0;\n        if (str.length === 0) return hash.toString();\n        for(let i = 0; i < str.length; i++){\n            const char = str.charCodeAt(i);\n            hash = (hash << 5) - hash + char;\n            hash = hash & hash; // Convert to 32bit integer\n        }\n        return Math.abs(hash).toString(36);\n    }\n    /**\r\n   * 为数据添加元数据\r\n   */ addDataMetadata(data) {\n        const dataVersion = this.generateDataVersion(data);\n        const enhancedData = {\n            ...data,\n            _metadata: {\n                dataVersion,\n                instanceId: this.instanceId,\n                timestamp: Date.now()\n            }\n        };\n        this.currentDataVersion = dataVersion;\n        return enhancedData;\n    }\n    /**\r\n   * 验证数据一致性\r\n   */ validateDataConsistency(data) {\n        if (!data._metadata) {\n            // 没有元数据的数据被认为是有效的（向后兼容）\n            return true;\n        }\n        // 检查实例ID是否匹配\n        if (data._metadata.instanceId !== this.instanceId) {\n            console.warn(\"⚠️ 数据实例ID不匹配，可能来自不同的思维导图实例\");\n            return false;\n        }\n        return true;\n    }\n    /**\r\n   * 移除数据元数据（用于传递给SimpleMindMap）\r\n   */ removeDataMetadata(data) {\n        const { _metadata, ...cleanData } = data;\n        return cleanData;\n    }\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        return new Promise((resolve, reject)=>{\n            this.initMutex(async ()=>{\n                try {\n                    // 动态导入SimpleMindMap完整版（包含所有插件，特别是Drag插件）\n                    const { default: SimpleMindMap } = await Promise.all(/*! import() */[__webpack_require__.e(\"css-node_modules_quill_dist_quill_snow_css\"), __webpack_require__.e(\"_app-pages-browser_node_modules_simple-mind-map_full_js\")]).then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map/full.js */ \"(app-pages-browser)/./node_modules/simple-mind-map/full.js\"));\n                    // 清理现有实例（使用强制销毁）\n                    if (this.mindMapInstance) {\n                        await this.forceDestroy();\n                    }\n                    // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器\n                    const finalConfig = {\n                        ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                        ...this.config.config,\n                        el: this.config.container,\n                        data: this.config.data,\n                        readonly: this.config.readonly\n                    };\n                    // 创建实例\n                    this.mindMapInstance = new SimpleMindMap(finalConfig);\n                    this.isInitialized = true;\n                    resolve(this.mindMapInstance);\n                } catch (error) {\n                    console.error(\"❌ SimpleMindMap初始化失败:\", error);\n                    const errorMessage = \"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\");\n                    reject(new Error(errorMessage));\n                }\n            });\n        });\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据（带版本控制）\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            var _enhancedData__metadata;\n            // 添加数据元数据\n            const enhancedData = this.addDataMetadata(data);\n            // 验证数据一致性\n            if (!this.validateDataConsistency(enhancedData)) {\n                console.warn(\"⚠️ 数据版本不匹配，跳过设置\");\n                return;\n            }\n            // 移除元数据后传递给SimpleMindMap\n            const cleanData = this.removeDataMetadata(enhancedData);\n            this.mindMapInstance.setData(cleanData);\n            console.log(\"✅ 数据设置成功，版本:\", (_enhancedData__metadata = enhancedData._metadata) === null || _enhancedData__metadata === void 0 ? void 0 : _enhancedData__metadata.dataVersion);\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 容器尺寸变化后调整画布\r\n   */ resize() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 先更新容器位置和尺寸信息\n            this.mindMapInstance.getElRectInfo();\n            // 然后调整画布尺寸\n            this.mindMapInstance.resize();\n        } catch (error) {\n            console.error(\"❌ 画布尺寸调整失败:\", error);\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 强制销毁实例（完整清理版本）\r\n   */ async forceDestroy() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 解绑所有事件监听器\n            if (this.mindMapInstance) {\n                try {\n                    // 尝试解绑常见的事件监听器\n                    const commonEvents = [\n                        \"data_change\",\n                        \"node_active\",\n                        \"node_tree_render_end\",\n                        \"set_data\",\n                        \"expand_btn_click\",\n                        \"node_click\",\n                        \"draw_click\",\n                        \"svg_mousedown\",\n                        \"mousedown\",\n                        \"mousemove\",\n                        \"mouseup\",\n                        \"contextmenu\"\n                    ];\n                    commonEvents.forEach((eventName)=>{\n                        try {\n                            // 使用any类型避免类型检查问题\n                            const instance = this.mindMapInstance;\n                            if (instance && typeof instance.off === \"function\") {\n                                instance.off(eventName, ()=>{});\n                            }\n                        } catch (error) {\n                        // 忽略解绑失败的事件\n                        }\n                    });\n                } catch (error) {\n                    console.warn(\"⚠️ 事件解绑失败:\", error);\n                }\n            }\n            // 销毁实例\n            this.mindMapInstance.destroy();\n            // 清理DOM引用\n            this.clearDOMReferences();\n            // 等待销毁完成\n            await this.waitForDestruction();\n            console.log(\"✅ SimpleMindMap实例强制销毁完成\");\n        } catch (error) {\n            console.error(\"❌ 强制销毁SimpleMindMap实例失败:\", error);\n            throw error;\n        } finally{\n            this.mindMapInstance = null;\n            this.isInitialized = false;\n        }\n    }\n    /**\r\n   * 清理DOM引用\r\n   */ clearDOMReferences() {\n        try {\n            // 清理容器中可能残留的SimpleMindMap相关DOM元素\n            if (this.config.container) {\n                const container = this.config.container;\n                // 移除所有子元素\n                while(container.firstChild){\n                    container.removeChild(container.firstChild);\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ DOM引用清理失败:\", error);\n        }\n    }\n    /**\r\n   * 等待销毁完成\r\n   */ async waitForDestruction() {\n        // 给予适当的等待时间确保异步销毁完成\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 100);\n        });\n    }\n    /**\r\n   * 销毁实例（保持向后兼容）\r\n   */ destroy() {\n        if (this.mindMapInstance) {\n            try {\n                this.mindMapInstance.destroy();\n                console.log(\"✅ SimpleMindMap实例销毁完成\");\n            } catch (error) {\n                console.error(\"❌ 销毁SimpleMindMap实例失败:\", error);\n            }\n        }\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.initMutex = (0,lib0_mutex__WEBPACK_IMPORTED_MODULE_1__.createMutex)();\n        // 数据版本控制\n        this.currentDataVersion = null;\n        this.instanceId = this.generateUniqueId();\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts":
/*!*********************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/SelectionManager.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionManager: function() { return /* binding */ SelectionManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * SelectionManager - 框选管理器\r\n * 负责右键长按框选功能的实现\r\n * \r\n * 职责：\r\n * - 右键长按检测\r\n * - 框选区域渲染\r\n * - 多节点选择逻辑\r\n * \r\n * 设计原则：\r\n * - 简化状态：只有 idle 和 selecting 两个状态\r\n * - 官方API优先：使用官方的坐标转换和节点选择API\r\n */ \nclass SelectionManager {\n    /**\r\n   * 初始化框选管理器\r\n   */ initialize() {\n        this.createSelectionBox();\n        this.bindEvents();\n        console.log(\"✅ 框选管理器初始化完成\");\n    }\n    /**\r\n   * 创建选择框DOM元素\r\n   */ createSelectionBox() {\n        this.selectionBox = document.createElement(\"div\");\n        this.selectionBox.className = \"mindmap-selection-box\";\n        const { selectionBoxStyle } = this.config;\n        this.selectionBox.style.cssText = \"\\n      position: fixed;\\n      pointer-events: none;\\n      z-index: 10000;\\n      border: \".concat(selectionBoxStyle.strokeWidth, \"px solid \").concat(selectionBoxStyle.strokeColor, \";\\n      background-color: \").concat(selectionBoxStyle.fillColor, \";\\n      opacity: \").concat(selectionBoxStyle.fillOpacity, \";\\n      border-radius: 4px;\\n      display: none;\\n      box-sizing: border-box;\\n      box-shadow: 0 2px 8px rgba(143, 188, 143, 0.3);\\n    \");\n        // 直接插入到body，避免容器层级问题\n        document.body.appendChild(this.selectionBox);\n    }\n    /**\r\n   * 绑定事件监听\r\n   */ bindEvents() {\n        // 不使用capture模式，让节点事件优先处理\n        this.container.addEventListener(\"mousedown\", this.boundHandlers.mousedown, {\n            passive: false\n        });\n        this.container.addEventListener(\"contextmenu\", this.boundHandlers.contextmenu, {\n            passive: false\n        });\n        // 全局鼠标事件\n        document.addEventListener(\"mousemove\", this.boundHandlers.mousemove, {\n            passive: false\n        });\n        document.addEventListener(\"mouseup\", this.boundHandlers.mouseup, {\n            passive: false\n        });\n    }\n    /**\r\n   * 解除事件监听\r\n   */ unbindEvents() {\n        this.container.removeEventListener(\"mousedown\", this.boundHandlers.mousedown, true);\n        this.container.removeEventListener(\"contextmenu\", this.boundHandlers.contextmenu, true);\n        document.removeEventListener(\"mousemove\", this.boundHandlers.mousemove);\n        document.removeEventListener(\"mouseup\", this.boundHandlers.mouseup);\n    }\n    /**\r\n   * 处理鼠标按下事件\r\n   */ handleMouseDown(event) {\n        // 只处理右键\n        if (event.button !== 2) return;\n        // 检查是否点击在节点上\n        const target = event.target;\n        if (this.isClickOnNode(target)) {\n            // 如果点击在节点上，不处理框选，让节点的右键菜单正常工作\n            console.log(\"\\uD83D\\uDDB1️ 右键点击节点，跳过框选处理\");\n            return;\n        }\n        // 阻止默认右键菜单\n        event.preventDefault();\n        // 转换为画布坐标\n        const canvasPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);\n        this.startPoint = canvasPoint;\n        // 设置长按检测定时器\n        this.longPressTimer = setTimeout(()=>{\n            this.startSelection();\n        }, this.config.longPressDelay);\n        console.log(\"\\uD83D\\uDDB1️ 右键按下空白区域，开始检测长按\");\n    }\n    /**\r\n   * 处理鼠标移动事件\r\n   */ handleMouseMove(event) {\n        if (!this.startPoint) return;\n        const currentPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);\n        this.currentPoint = currentPoint;\n        // 如果还在检测阶段，检查移动距离\n        if (!this.isSelecting && this.longPressTimer) {\n            const distance = this.calculateDistance(this.startPoint, currentPoint);\n            if (distance > this.config.moveThreshold) {\n                // 移动距离超过阈值，立即开始框选\n                this.clearLongPressTimer();\n                this.startSelection();\n            }\n        }\n        // 如果正在框选，更新选择框\n        if (this.isSelecting) {\n            this.updateSelectionBox();\n        }\n    }\n    /**\r\n   * 处理鼠标释放事件\r\n   */ handleMouseUp(event) {\n        if (event.button !== 2) return;\n        this.clearLongPressTimer();\n        if (this.isSelecting) {\n            this.completeSelection();\n        } else {\n            // 短按，重置状态\n            this.resetSelection();\n        }\n    }\n    /**\r\n   * 处理右键菜单事件\r\n   */ handleContextMenu(event) {\n        // 只有在正在框选时才阻止右键菜单\n        if (this.isSelecting) {\n            event.stopPropagation();\n            event.preventDefault();\n            console.log(\"\\uD83D\\uDEAB 框选状态中，阻止右键菜单\");\n        } else {\n            // 检查是否点击在节点上\n            const target = event.target;\n            if (!this.isClickOnNode(target)) {\n                // 如果不是点击节点，阻止默认右键菜单\n                event.preventDefault();\n                console.log(\"\\uD83D\\uDEAB 空白区域右键，阻止默认菜单\");\n            }\n        }\n    }\n    /**\r\n   * 开始框选\r\n   */ startSelection() {\n        this.isSelecting = true;\n        this.showSelectionBox();\n        console.log(\"\\uD83D\\uDCE6 开始框选模式\");\n    }\n    /**\r\n   * 显示选择框\r\n   */ showSelectionBox() {\n        if (this.selectionBox) {\n            this.selectionBox.style.display = \"block\";\n            this.updateSelectionBox();\n        }\n    }\n    /**\r\n   * 更新选择框位置和大小\r\n   */ updateSelectionBox() {\n        if (!this.selectionBox || !this.startPoint || !this.currentPoint) return;\n        // 转换为屏幕坐标\n        const containerRect = this.container.getBoundingClientRect();\n        const startScreen = {\n            x: this.startPoint.x + containerRect.left,\n            y: this.startPoint.y + containerRect.top\n        };\n        const currentScreen = {\n            x: this.currentPoint.x + containerRect.left,\n            y: this.currentPoint.y + containerRect.top\n        };\n        const left = Math.min(startScreen.x, currentScreen.x);\n        const top = Math.min(startScreen.y, currentScreen.y);\n        const width = Math.abs(currentScreen.x - startScreen.x);\n        const height = Math.abs(currentScreen.y - startScreen.y);\n        this.selectionBox.style.left = \"\".concat(left, \"px\");\n        this.selectionBox.style.top = \"\".concat(top, \"px\");\n        this.selectionBox.style.width = \"\".concat(width, \"px\");\n        this.selectionBox.style.height = \"\".concat(height, \"px\");\n    }\n    /**\r\n   * 完成框选\r\n   */ completeSelection() {\n        if (!this.startPoint || !this.currentPoint) {\n            this.resetSelection();\n            return;\n        }\n        // 查找选择范围内的节点\n        const selectedNodes = this.findNodesInSelection();\n        if (selectedNodes.length > 0) {\n            var // 回调通知\n            _this_onSelectionComplete, _this;\n            // 使用官方API激活节点\n            this.activateNodes(selectedNodes);\n            (_this_onSelectionComplete = (_this = this).onSelectionComplete) === null || _this_onSelectionComplete === void 0 ? void 0 : _this_onSelectionComplete.call(_this, selectedNodes);\n            console.log(\"✅ 框选完成，选中 \".concat(selectedNodes.length, \" 个节点\"));\n        } else {\n            console.log(\"ℹ️ 框选区域内没有节点\");\n        }\n        // 延迟隐藏选择框，提供视觉反馈\n        setTimeout(()=>{\n            this.resetSelection();\n        }, 150);\n    }\n    /**\r\n   * 查找选择范围内的节点\r\n   */ findNodesInSelection() {\n        var _this_mindMapInstance_renderer;\n        if (!this.startPoint || !this.currentPoint || !((_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.root)) {\n            return [];\n        }\n        const selectedNodes = [];\n        const selectionRect = this.getSelectionRect();\n        // 递归遍历节点树\n        this.traverseNodes(this.mindMapInstance.renderer.root, (node)=>{\n            if (this.isNodeInSelection(node, selectionRect)) {\n                selectedNodes.push(node);\n            }\n        });\n        return selectedNodes;\n    }\n    /**\r\n   * 递归遍历节点\r\n   */ traverseNodes(node, callback) {\n        if (!node) return;\n        callback(node);\n        if (node.children && Array.isArray(node.children)) {\n            node.children.forEach((child)=>this.traverseNodes(child, callback));\n        }\n    }\n    /**\r\n   * 检查节点是否在选择范围内\r\n   */ isNodeInSelection(node, selectionRect) {\n        try {\n            var _node_getRectInSvg;\n            // 使用官方API获取节点位置\n            const nodeRect = (_node_getRectInSvg = node.getRectInSvg) === null || _node_getRectInSvg === void 0 ? void 0 : _node_getRectInSvg.call(node);\n            if (!nodeRect) return false;\n            const { left, top, width, height } = nodeRect;\n            const nodeRight = left + width;\n            const nodeBottom = top + height;\n            // 矩形相交检测\n            return !(nodeRight < selectionRect.left || left > selectionRect.right || nodeBottom < selectionRect.top || top > selectionRect.bottom);\n        } catch (error) {\n            console.warn(\"❌ 节点位置检测失败:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * 获取选择矩形\r\n   */ getSelectionRect() {\n        if (!this.startPoint || !this.currentPoint) {\n            return {\n                left: 0,\n                top: 0,\n                right: 0,\n                bottom: 0\n            };\n        }\n        const left = Math.min(this.startPoint.x, this.currentPoint.x);\n        const top = Math.min(this.startPoint.y, this.currentPoint.y);\n        const right = Math.max(this.startPoint.x, this.currentPoint.x);\n        const bottom = Math.max(this.startPoint.y, this.currentPoint.y);\n        return {\n            left,\n            top,\n            right,\n            bottom\n        };\n    }\n    /**\r\n   * 激活选中的节点\r\n   */ activateNodes(nodes) {\n        try {\n            var _this_mindMapInstance_renderer;\n            // 使用官方API激活多个节点\n            if ((_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.activeMultiNode) {\n                this.mindMapInstance.renderer.activeMultiNode(nodes);\n            } else {\n                var // 降级方案\n                _this_mindMapInstance_renderer1;\n                (_this_mindMapInstance_renderer1 = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer1 === void 0 ? void 0 : _this_mindMapInstance_renderer1.clearActiveNodeList();\n                nodes.forEach((node)=>{\n                    var _this_mindMapInstance_renderer;\n                    (_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.addNodeToActiveList(node);\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 激活节点失败:\", error);\n        }\n    }\n    /**\r\n   * 检查是否点击在节点上\r\n   */ isClickOnNode(target) {\n        // 检查点击目标是否是节点或节点的子元素\n        // SimpleMindMap的节点通常有特定的类名或属性\n        let element = target;\n        while(element && element !== this.container){\n            // 检查是否是SVG节点元素\n            if (element.tagName === \"g\" && element.getAttribute(\"data-nodeid\")) {\n                return true;\n            }\n            // 检查是否有节点相关的类名\n            if (element.classList && (element.classList.contains(\"smm-node\") || element.classList.contains(\"node\") || element.getAttribute(\"data-node\") !== null)) {\n                return true;\n            }\n            element = element.parentElement;\n        }\n        return false;\n    }\n    /**\r\n   * 计算两点间距离\r\n   */ calculateDistance(p1, p2) {\n        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n    }\n    /**\r\n   * 清除长按定时器\r\n   */ clearLongPressTimer() {\n        if (this.longPressTimer) {\n            clearTimeout(this.longPressTimer);\n            this.longPressTimer = null;\n        }\n    }\n    /**\r\n   * 重置选择状态\r\n   */ resetSelection() {\n        this.isSelecting = false;\n        this.startPoint = null;\n        this.currentPoint = null;\n        this.clearLongPressTimer();\n        if (this.selectionBox) {\n            this.selectionBox.style.display = \"none\";\n        }\n    }\n    /**\r\n   * 更新配置\r\n   */ updateConfig(config) {\n        this.config = {\n            ...this.config,\n            ...config\n        };\n    }\n    /**\r\n   * 检查是否正在框选\r\n   */ isActive() {\n        return this.isSelecting;\n    }\n    /**\r\n   * 销毁框选管理器\r\n   */ destroy() {\n        this.resetSelection();\n        this.unbindEvents();\n        if (this.selectionBox) {\n            this.selectionBox.remove();\n            this.selectionBox = null;\n        }\n        console.log(\"✅ 框选管理器销毁完成\");\n    }\n    constructor(mindMapInstance, container, onSelectionComplete){\n        // 简化状态：只有两种状态\n        this.isSelecting = false;\n        // 选择过程数据\n        this.startPoint = null;\n        this.currentPoint = null;\n        this.longPressTimer = null;\n        // DOM元素\n        this.selectionBox = null;\n        // 事件处理器绑定\n        this.boundHandlers = {\n            mousedown: this.handleMouseDown.bind(this),\n            mousemove: this.handleMouseMove.bind(this),\n            mouseup: this.handleMouseUp.bind(this),\n            contextmenu: this.handleContextMenu.bind(this)\n        };\n        this.mindMapInstance = mindMapInstance;\n        this.container = container;\n        this.config = _types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_SELECTION_CONFIG;\n        this.onSelectionComplete = onSelectionComplete;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\n"));

/***/ })

});
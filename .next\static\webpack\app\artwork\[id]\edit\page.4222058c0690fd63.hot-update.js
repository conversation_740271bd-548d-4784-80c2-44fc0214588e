"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleManager: function() { return /* binding */ StyleManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * StyleManager - 样式管理器\r\n * 负责SimpleMindMap主题和样式的统一管理\r\n * \r\n * 职责：\r\n * - 主题切换和配置\r\n * - 自定义样式应用\r\n * - 样式状态管理\r\n * \r\n * 设计原则：\r\n * - 官方API优先：使用SimpleMindMap官方主题API\r\n * - 简洁高效：最小化样式配置，避免过度自定义\r\n */ \nclass StyleManager {\n    /**\r\n   * 实现ICleanable接口 - 是否已销毁\r\n   */ get isDestroyed() {\n        return this._cleanupState === _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYED;\n    }\n    /**\r\n   * 实现ICleanable接口 - 当前清理状态\r\n   */ get cleanupState() {\n        return this._cleanupState;\n    }\n    /**\r\n   * 初始化样式管理器\r\n   */ async initialize() {\n        try {\n            // 应用初始主题\n            await this.applyTheme(this.currentTheme);\n            this.isInitialized = true;\n            console.log(\"✅ 样式管理器初始化完成，主题:\", this.currentTheme);\n        } catch (error) {\n            console.error(\"❌ 样式管理器初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ async applyTheme(theme) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not available\");\n        }\n        try {\n            // 直接使用我们的自定义主题配置，不依赖SimpleMindMap内置主题\n            const themeConfig = this.getThemeConfig(theme);\n            this.mindMapInstance.setThemeConfig(themeConfig, false);\n            this.currentTheme = theme;\n            console.log(\"✅ 自定义主题应用成功: \".concat(theme), themeConfig);\n        } catch (error) {\n            console.error(\"❌ 主题应用失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式 - 金色高亮\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#FFD700\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 4,\n                fontSize: 13,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#000000\",\n                padding: [\n                    6,\n                    10\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 0.95\n            },\n            // 展开按钮样式 - 金色\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#FFD700\",\n                fillColor: \"#0a0a0a\",\n                strokeColor: \"#FFD700\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式 - 金色系\n            associativeLine: {\n                strokeColor: \"#DAA520\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式 - 亮金色\n            activeNodeStyle: {\n                strokeColor: \"#FFFF00\",\n                strokeWidth: 4,\n                fillColor: \"rgba(255, 215, 0, 0.15)\"\n            },\n            // 悬停状态样式 - 金色光晕\n            hoverNodeStyle: {\n                fillColor: \"rgba(255, 215, 0, 0.1)\",\n                strokeColor: \"#FFA500\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 更新主题\r\n   */ async updateTheme(theme) {\n        if (theme === this.currentTheme) return;\n        await this.applyTheme(theme);\n    }\n    /**\r\n   * 获取当前主题\r\n   */ getCurrentTheme() {\n        return this.currentTheme;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 实现ICleanable接口 - 销毁方法\r\n   */ async destroy() {\n        if (this._cleanupState !== _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE) {\n            console.log(\"\\uD83D\\uDD12 StyleManager已在清理中或已销毁，跳过重复清理\");\n            return;\n        }\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYING;\n        console.log(\"\\uD83E\\uDDF9 开始清理StyleManager...\");\n        try {\n            this.isInitialized = false;\n            this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYED;\n            console.log(\"✅ StyleManager清理完成\");\n        } catch (error) {\n            console.error(\"❌ StyleManager清理失败:\", error);\n            this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE; // 重置状态以允许重试\n            throw error;\n        }\n    }\n    constructor(mindMapInstance, theme = \"dark\"){\n        this.isInitialized = false;\n        // 清理状态管理\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE;\n        this.mindMapInstance = mindMapInstance;\n        this.currentTheme = theme;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ })

});
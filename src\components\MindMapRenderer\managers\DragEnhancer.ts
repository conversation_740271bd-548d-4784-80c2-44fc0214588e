/**
 * DragEnhancer - 拖拽功能增强管理器
 * 基于SimpleMindMap的Drag插件，提供更丰富的拖拽体验
 * 
 * 功能特性：
 * - 拖拽动画效果
 * - 视觉反馈优化
 * - 拖拽权限控制
 * - 拖拽数据保存
 * - 性能优化
 */

import { SimpleMindMapInstance } from '../types';
import { SaveManager } from '@/services/SaveManager';

export interface DragEnhancerConfig {
  /** 是否启用拖拽增强 */
  enabled: boolean;
  /** 拖拽动画配置 */
  animation: {
    /** 拖拽开始动画时长 (ms) */
    startDuration: number;
    /** 拖拽结束动画时长 (ms) */
    endDuration: number;
    /** 缓动函数 */
    easing: string;
    /** 是否启用弹性效果 */
    enableSpring: boolean;
  };
  /** 视觉反馈配置 */
  visual: {
    /** 拖拽时节点透明度 */
    dragOpacity: number;
    /** 克隆节点透明度 */
    cloneOpacity: number;
    /** 位置指示器颜色 */
    placeholderColor: string;
    /** 位置指示器宽度 */
    placeholderWidth: number;
    /** 是否显示拖拽轨迹 */
    showTrail: boolean;
  };
  /** 拖拽限制配置 */
  constraints: {
    /** 最小拖拽距离 (px) */
    minDragDistance: number;
    /** 是否限制拖拽范围 */
    constrainToCanvas: boolean;
    /** 拖拽权限检查函数 */
    canDrag?: (node: any) => boolean;
    /** 拖拽目标检查函数 */
    canDropTo?: (dragNode: any, targetNode: any) => boolean;
  };
  /** 性能优化配置 */
  performance: {
    /** 拖拽节流时间 (ms) */
    throttleDelay: number;
    /** 是否启用GPU加速 */
    enableGPUAcceleration: boolean;
    /** 最大同时拖拽节点数 */
    maxDragNodes: number;
  };
  /** 数据保存配置 */
  persistence: {
    /** 是否自动保存拖拽后的数据 */
    autoSave: boolean;
    /** 保存延迟时间 (ms) - 现在通过 SaveManager 统一管理 */
    saveDelay: number;
    /** 保存回调函数 - 已弃用，现在通过 SaveManager 统一处理 */
    onSave?: (data: any) => void;
  };
}

export const DEFAULT_DRAG_ENHANCER_CONFIG: DragEnhancerConfig = {
  enabled: true,
  animation: {
    startDuration: 200,
    endDuration: 300,
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    enableSpring: true
  },
  visual: {
    dragOpacity: 0.7,
    cloneOpacity: 0.8,
    placeholderColor: '#FFD700',
    placeholderWidth: 3,
    showTrail: false
  },
  constraints: {
    minDragDistance: 5,
    constrainToCanvas: true
  },
  performance: {
    throttleDelay: 16, // 60fps
    enableGPUAcceleration: true,
    maxDragNodes: 10
  },
  persistence: {
    autoSave: true,
    saveDelay: 1000
  }
};

export class DragEnhancer {
  private mindMapInstance: SimpleMindMapInstance;
  private config: DragEnhancerConfig;
  private isInitialized = false;
  private dragState: {
    isDragging: boolean;
    dragStartTime: number;
    dragNodes: any[];
    originalPositions: Map<string, { x: number; y: number }>;
  } = {
    isDragging: false,
    dragStartTime: 0,
    dragNodes: [],
    originalPositions: new Map()
  };
  private animationFrameId: number | null = null;
  private saveManager: SaveManager;
  private currentFileId: string | null = null;

  constructor(mindMapInstance: SimpleMindMapInstance, config: Partial<DragEnhancerConfig> = {}) {
    this.mindMapInstance = mindMapInstance;
    this.config = { ...DEFAULT_DRAG_ENHANCER_CONFIG, ...config };
    this.saveManager = SaveManager.getInstance();
  }

  /**
   * 设置当前文件ID
   */
  setCurrentFileId(fileId: string): void {
    this.currentFileId = fileId;
  }

  /**
   * 初始化拖拽增强功能
   */
  initialize(): void {
    if (!this.config.enabled || this.isInitialized) {
      return;
    }

    try {
      this.setupDragEnhancements();
      this.setupEventListeners();
      this.isInitialized = true;
    } catch (error) {
      console.error('❌ DragEnhancer 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置拖拽增强功能
   */
  private setupDragEnhancements(): void {
    // 确保拖拽功能未被禁用
    if (this.mindMapInstance.opt.readonly || this.mindMapInstance.opt.isDisableDrag) {
      return;
    }

    // 只更新视觉相关的拖拽配置，不干扰核心拖拽逻辑
    const dragConfig = {
      // 拖拽透明度配置
      dragOpacityConfig: {
        beingDragNodeOpacity: this.config.visual.dragOpacity,
        cloneNodeOpacity: this.config.visual.cloneOpacity
      },

      // 拖拽位置指示器配置
      dragPlaceholderRectFill: this.config.visual.placeholderColor,
      dragPlaceholderLineConfig: {
        color: this.config.visual.placeholderColor,
        width: this.config.visual.placeholderWidth
      },

      // 拖拽边缘自动移动
      autoMoveWhenMouseInEdgeOnDrag: true
    };

    // 应用配置到SimpleMindMap实例
    Object.assign(this.mindMapInstance.opt, dragConfig);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听拖拽开始事件
    this.mindMapInstance.on('node_dragging', this.handleDragStart.bind(this));
    
    // 监听拖拽结束事件
    this.mindMapInstance.on('node_dragend', this.handleDragEnd.bind(this));
    
    // 监听数据变更事件（用于自动保存）
    this.mindMapInstance.on('data_change', this.handleDataChange.bind(this));
  }







  /**
   * 处理拖拽开始
   */
  private handleDragStart(node: any): void {
    this.dragState.isDragging = true;
    this.dragState.dragStartTime = Date.now();
    this.dragState.dragNodes = Array.isArray(node) ? node : [node];

    // 记录原始位置
    this.dragState.originalPositions.clear();
    this.dragState.dragNodes.forEach(dragNode => {
      if (dragNode.uid) {
        this.dragState.originalPositions.set(dragNode.uid, {
          x: dragNode.left || 0,
          y: dragNode.top || 0
        });
      }
    });



    // 触发拖拽开始动画
    this.playDragStartAnimation();
  }

  /**
   * 处理拖拽结束
   */
  private handleDragEnd(dragInfo: any): void {
    const dragDuration = Date.now() - this.dragState.dragStartTime;
    


    // 触发拖拽结束动画
    this.playDragEndAnimation();

    // 重置拖拽状态
    this.dragState.isDragging = false;
    this.dragState.dragNodes = [];
    this.dragState.originalPositions.clear();
  }

  /**
   * 播放拖拽开始动画
   */
  private playDragStartAnimation(): void {
    if (!this.config.animation.enableSpring) return;

    // 实现弹性动画效果
    this.animationFrameId = requestAnimationFrame(() => {
      // 这里可以添加更复杂的动画逻辑
      // 动画逻辑可以在这里实现
    });
  }

  /**
   * 播放拖拽结束动画
   */
  private playDragEndAnimation(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }


  }

  /**
   * 处理数据变更（用于自动保存）- 使用 SaveManager
   */
  private handleDataChange(data: any): void {
    if (!this.config.persistence.autoSave || !this.currentFileId) return;

    console.log('🎯 DragEnhancer 数据变更:', {
      fileId: this.currentFileId,
      dataType: typeof data,
      hasData: !!data
    });

    // 将思维导图数据转换为 Markdown 格式
    try {
      const markdownContent = this.convertMindMapToMarkdown(data);

      // 使用 SaveManager 调度保存转换后的字符串
      this.saveManager.scheduleAutoSave(this.currentFileId, markdownContent, {
        debounceDelay: this.config.persistence.saveDelay,
        saveType: 'drag',
        immediate: false
      });

      console.log('🎯 拖拽数据已转换为 Markdown 并调度保存:', this.currentFileId);
    } catch (error) {
      console.error('❌ DragEnhancer 数据转换失败:', error);
    }
  }

  /**
   * 将思维导图数据转换为 Markdown 格式
   */
  private convertMindMapToMarkdown(data: any): string {
    if (!data || !data.data || !data.data.text) {
      console.warn('⚠️ DragEnhancer 收到无效的思维导图数据');
      return '# 思维导图\n\n内容为空';
    }

    const convertNode = (node: any, level: number = 1): string => {
      if (!node || !node.data || !node.data.text) {
        return '';
      }

      const prefix = '#'.repeat(level);
      let result = `${prefix} ${node.data.text}\n\n`;

      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          result += convertNode(child, level + 1);
        }
      }

      return result;
    };

    const markdownResult = convertNode(data).trim();
    return markdownResult || '# 思维导图\n\n内容为空';
  }





  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<DragEnhancerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.isInitialized) {
      this.setupDragEnhancements();
    }
  }

  /**
   * 获取拖拽状态
   */
  getDragState() {
    return { ...this.dragState };
  }

  /**
   * 销毁拖拽增强器
   */
  destroy(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    // 清理当前文件的保存状态
    if (this.currentFileId) {
      this.saveManager.clearFileState(this.currentFileId);
    }

    // 移除事件监听器
    this.mindMapInstance.off('node_dragging', this.handleDragStart.bind(this));
    this.mindMapInstance.off('node_dragend', this.handleDragEnd.bind(this));
    this.mindMapInstance.off('data_change', this.handleDataChange.bind(this));

    this.isInitialized = false;
    this.currentFileId = null;

    console.log('🗑️ DragEnhancer 已销毁');
  }
}

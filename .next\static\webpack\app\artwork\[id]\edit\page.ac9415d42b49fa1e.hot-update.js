"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/DragEnhancer */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _dragEnhancerRef_current, _selectionManagerRef_current, _clipboardManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.destroy();\n        dragEnhancerRef.current = null;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_clipboardManagerRef_current = clipboardManagerRef.current) === null || _clipboardManagerRef_current === void 0 ? void 0 : _clipboardManagerRef_current.destroy();\n        clipboardManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 启用拖拽增强器，使用 SaveManager 统一保存机制\n            if (!readonly && enableDragEnhancement) {\n                var _dragConfig_persistence, _dragConfig_persistence1;\n                var _dragConfig_persistence_autoSave, _dragConfig_persistence_saveDelay;\n                const enhancedDragConfig = {\n                    ...dragConfig,\n                    persistence: {\n                        autoSave: (_dragConfig_persistence_autoSave = (_dragConfig_persistence = dragConfig.persistence) === null || _dragConfig_persistence === void 0 ? void 0 : _dragConfig_persistence.autoSave) !== null && _dragConfig_persistence_autoSave !== void 0 ? _dragConfig_persistence_autoSave : true,\n                        saveDelay: (_dragConfig_persistence_saveDelay = (_dragConfig_persistence1 = dragConfig.persistence) === null || _dragConfig_persistence1 === void 0 ? void 0 : _dragConfig_persistence1.saveDelay) !== null && _dragConfig_persistence_saveDelay !== void 0 ? _dragConfig_persistence_saveDelay : 1000\n                    }\n                };\n                dragEnhancerRef.current = new _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__.DragEnhancer(mindMapInstance, enhancedDragConfig);\n                dragEnhancerRef.current.initialize();\n                console.log(\"\\uD83C\\uDFAF DragEnhancer 已启用并集成 SaveManager\");\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            }\n        }), [\n        resizeCanvas,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 440,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 457,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 453,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 493,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 516,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 476,\n        columnNumber: 5\n    }, undefined);\n}, \"n9iMgar1C7HJsnxniI5k3ycy3s4=\")), \"n9iMgar1C7HJsnxniI5k3ycy3s4=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/DragEnhancer.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_DRAG_ENHANCER_CONFIG: function() { return /* binding */ DEFAULT_DRAG_ENHANCER_CONFIG; },\n/* harmony export */   DragEnhancer: function() { return /* binding */ DragEnhancer; }\n/* harmony export */ });\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/**\n * DragEnhancer - 拖拽功能增强管理器\n * 基于SimpleMindMap的Drag插件，提供更丰富的拖拽体验\n * \n * 功能特性：\n * - 拖拽动画效果\n * - 视觉反馈优化\n * - 拖拽权限控制\n * - 拖拽数据保存\n * - 性能优化\n */ \nconst DEFAULT_DRAG_ENHANCER_CONFIG = {\n    enabled: true,\n    animation: {\n        startDuration: 200,\n        endDuration: 300,\n        easing: \"cubic-bezier(0.25, 0.46, 0.45, 0.94)\",\n        enableSpring: true\n    },\n    visual: {\n        dragOpacity: 0.7,\n        cloneOpacity: 0.8,\n        placeholderColor: \"#FFD700\",\n        placeholderWidth: 3,\n        showTrail: false\n    },\n    constraints: {\n        minDragDistance: 5,\n        constrainToCanvas: true\n    },\n    performance: {\n        throttleDelay: 16,\n        enableGPUAcceleration: true,\n        maxDragNodes: 10\n    },\n    persistence: {\n        autoSave: true,\n        saveDelay: 1000\n    }\n};\nclass DragEnhancer {\n    /**\n   * 设置当前文件ID\n   */ setCurrentFileId(fileId) {\n        this.currentFileId = fileId;\n    }\n    /**\n   * 初始化拖拽增强功能\n   */ initialize() {\n        if (!this.config.enabled || this.isInitialized) {\n            return;\n        }\n        try {\n            this.setupDragEnhancements();\n            this.setupEventListeners();\n            this.isInitialized = true;\n        } catch (error) {\n            console.error(\"❌ DragEnhancer 初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 设置拖拽增强功能\n   */ setupDragEnhancements() {\n        // 确保拖拽功能未被禁用\n        if (this.mindMapInstance.opt.readonly || this.mindMapInstance.opt.isDisableDrag) {\n            return;\n        }\n        // 只更新视觉相关的拖拽配置，不干扰核心拖拽逻辑\n        const dragConfig = {\n            // 拖拽透明度配置\n            dragOpacityConfig: {\n                beingDragNodeOpacity: this.config.visual.dragOpacity,\n                cloneNodeOpacity: this.config.visual.cloneOpacity\n            },\n            // 拖拽位置指示器配置\n            dragPlaceholderRectFill: this.config.visual.placeholderColor,\n            dragPlaceholderLineConfig: {\n                color: this.config.visual.placeholderColor,\n                width: this.config.visual.placeholderWidth\n            },\n            // 拖拽边缘自动移动\n            autoMoveWhenMouseInEdgeOnDrag: true\n        };\n        // 应用配置到SimpleMindMap实例\n        Object.assign(this.mindMapInstance.opt, dragConfig);\n    }\n    /**\n   * 设置事件监听器\n   */ setupEventListeners() {\n        // 监听拖拽开始事件\n        this.mindMapInstance.on(\"node_dragging\", this.handleDragStart.bind(this));\n        // 监听拖拽结束事件\n        this.mindMapInstance.on(\"node_dragend\", this.handleDragEnd.bind(this));\n        // 监听数据变更事件（用于自动保存）\n        this.mindMapInstance.on(\"data_change\", this.handleDataChange.bind(this));\n    }\n    /**\n   * 处理拖拽开始\n   */ handleDragStart(node) {\n        this.dragState.isDragging = true;\n        this.dragState.dragStartTime = Date.now();\n        this.dragState.dragNodes = Array.isArray(node) ? node : [\n            node\n        ];\n        // 记录原始位置\n        this.dragState.originalPositions.clear();\n        this.dragState.dragNodes.forEach((dragNode)=>{\n            if (dragNode.uid) {\n                this.dragState.originalPositions.set(dragNode.uid, {\n                    x: dragNode.left || 0,\n                    y: dragNode.top || 0\n                });\n            }\n        });\n        // 触发拖拽开始动画\n        this.playDragStartAnimation();\n    }\n    /**\n   * 处理拖拽结束\n   */ handleDragEnd(dragInfo) {\n        const dragDuration = Date.now() - this.dragState.dragStartTime;\n        // 触发拖拽结束动画\n        this.playDragEndAnimation();\n        // 重置拖拽状态\n        this.dragState.isDragging = false;\n        this.dragState.dragNodes = [];\n        this.dragState.originalPositions.clear();\n    }\n    /**\n   * 播放拖拽开始动画\n   */ playDragStartAnimation() {\n        if (!this.config.animation.enableSpring) return;\n        // 实现弹性动画效果\n        this.animationFrameId = requestAnimationFrame(()=>{\n        // 这里可以添加更复杂的动画逻辑\n        // 动画逻辑可以在这里实现\n        });\n    }\n    /**\n   * 播放拖拽结束动画\n   */ playDragEndAnimation() {\n        if (this.animationFrameId) {\n            cancelAnimationFrame(this.animationFrameId);\n            this.animationFrameId = null;\n        }\n    }\n    /**\n   * 处理数据变更（用于自动保存）- 使用 SaveManager\n   */ handleDataChange(data) {\n        if (!this.config.persistence.autoSave || !this.currentFileId) return;\n        // 使用 SaveManager 调度保存\n        this.saveManager.scheduleAutoSave(this.currentFileId, data, {\n            debounceDelay: this.config.persistence.saveDelay,\n            saveType: \"drag\",\n            immediate: false\n        });\n        console.log(\"\\uD83C\\uDFAF 拖拽数据变更已调度保存:\", this.currentFileId);\n    }\n    /**\n   * 更新配置\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        if (this.isInitialized) {\n            this.setupDragEnhancements();\n        }\n    }\n    /**\n   * 获取拖拽状态\n   */ getDragState() {\n        return {\n            ...this.dragState\n        };\n    }\n    /**\n   * 销毁拖拽增强器\n   */ destroy() {\n        if (this.animationFrameId) {\n            cancelAnimationFrame(this.animationFrameId);\n            this.animationFrameId = null;\n        }\n        // 清理当前文件的保存状态\n        if (this.currentFileId) {\n            this.saveManager.clearFileState(this.currentFileId);\n        }\n        // 移除事件监听器\n        this.mindMapInstance.off(\"node_dragging\", this.handleDragStart.bind(this));\n        this.mindMapInstance.off(\"node_dragend\", this.handleDragEnd.bind(this));\n        this.mindMapInstance.off(\"data_change\", this.handleDataChange.bind(this));\n        this.isInitialized = false;\n        this.currentFileId = null;\n        console.log(\"\\uD83D\\uDDD1️ DragEnhancer 已销毁\");\n    }\n    constructor(mindMapInstance, config = {}){\n        this.isInitialized = false;\n        this.dragState = {\n            isDragging: false,\n            dragStartTime: 0,\n            dragNodes: [],\n            originalPositions: new Map()\n        };\n        this.animationFrameId = null;\n        this.currentFileId = null;\n        this.mindMapInstance = mindMapInstance;\n        this.config = {\n            ...DEFAULT_DRAG_ENHANCER_CONFIG,\n            ...config\n        };\n        this.saveManager = _services_SaveManager__WEBPACK_IMPORTED_MODULE_0__.SaveManager.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts\n"));

/***/ })

});
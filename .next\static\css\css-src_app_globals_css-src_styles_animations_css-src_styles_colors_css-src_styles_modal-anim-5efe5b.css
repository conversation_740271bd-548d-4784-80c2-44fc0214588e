/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./src/styles/animations.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* 手绘风格动画效果 */

/* 弹窗出现动画 */
@keyframes dialog-enter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog-enter {
  animation: dialog-enter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 弹窗消失动画 */
@keyframes dialog-exit {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
}

.dialog-exit {
  animation: dialog-exit 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
}

/* 表单验证错误动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20% { transform: translateX(-5px); }
  40% { transform: translateX(5px); }
  60% { transform: translateX(-3px); }
  80% { transform: translateX(3px); }
}

.shake-animation {
  animation: shake 0.5s ease-in-out;
}

/* 创建成功动画 */
@keyframes success-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.success-pulse {
  animation: success-pulse 0.6s ease-in-out;
}

/* 手绘风格的淡入效果 */
@keyframes hand-fade-in {
  from {
    opacity: 0;
    transform: translateY(5px) rotate(-1deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotate(0);
  }
}

.hand-fade-in {
  animation: hand-fade-in 0.5s ease-out forwards;
}

/* 手绘风格的淡出效果 */
@keyframes hand-fade-out {
  from {
    opacity: 1;
    transform: translateY(0) rotate(0);
  }
  to {
    opacity: 0;
    transform: translateY(-5px) rotate(1deg);
  }
}

.hand-fade-out {
  animation: hand-fade-out 0.4s ease-in forwards;
}

/* 手绘风格的加载动画 */
@keyframes hand-loading {
  0% { transform: rotate(0deg); }
  25% { transform: rotate(90deg) scale(1.05); }
  50% { transform: rotate(180deg); }
  75% { transform: rotate(270deg) scale(0.95); }
  100% { transform: rotate(360deg); }
}

.hand-loading {
  animation: hand-loading 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
}
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./src/styles/colors.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* 宫崎骏风格色彩系统 */

:root {
  /* 主色调 - 柔和的绿色系 */
  --color-primary-green: #8FBC8F;
  --color-dark-green: #6B8E6B;
  --color-light-green: #A8D3A8;
  --color-sage-green: #B2C8B2;
  
  /* 中性色调 - 温暖的米色系 */
  --color-primary-beige: #F5F5DC;
  --color-cream: #FFFDD0;
  --color-soft-yellow: #F8F4E3;
  
  /* 强调色 - 自然色调 */
  --color-sky-blue: #87CEEB;
  --color-soft-blue: #B0E0E6;
  --color-sunset-orange: #FFA07A;
  --color-soft-pink: #FFB6C1;
  
  /* 背景色系 */
  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F9F7F0;
  --color-bg-tertiary: #F5F5F0;
  
  /* 文本色系 */
  --color-text-primary: #333333;
  --color-text-secondary: #666666;
  --color-text-tertiary: #999999;
  
  /* 边框色系 */
  --color-border-light: #E5E7EB;
  --color-border-medium: #D1D5DB;
  --color-border-dark: #9CA3AF;
  
  /* 状态色系 */
  --color-success: #8FBC8F;
  --color-warning: #FFD700;
  --color-error: #DC2626;
  --color-info: #87CEEB;
  
  /* 渐变色系 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-beige) 0%, var(--color-cream) 50%, var(--color-soft-yellow) 100%);
  --gradient-card: linear-gradient(145deg, #ffffff 0%, var(--color-bg-secondary) 100%);
  --gradient-button: linear-gradient(to bottom, var(--color-light-green) 0%, var(--color-primary-green) 100%);
  --gradient-highlight: linear-gradient(to right, transparent, rgba(143, 188, 143, 0.5), transparent);
}
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./src/styles/modal-animations.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 全局弹窗动画样式
 * 为所有弹窗组件提供统一的进入和退出动画效果
 */

/* 弹窗动画基础类 */
.modal-overlay {
 position: fixed;
 inset: 0px;
 z-index: 50;
 display: flex;
 align-items: center;
 justify-content: center;
 padding: 1rem;
 transition-property: all;
 transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
 transition-duration: 300ms;
 transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.modal-overlay.modal-enter {
 opacity: 1;
 --tw-backdrop-blur: blur(4px);
 -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
 backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.modal-overlay.modal-exit {
 opacity: 0;
 --tw-backdrop-blur:  ;
 -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
 backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

/* 弹窗容器动画 */
.modal-container {
 transition-property: all;
 transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
 transition-duration: 300ms;
 transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
 transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); /* 启用硬件加速 */
}

.modal-container.modal-enter {
 --tw-translate-y: 0px;
 --tw-scale-x: 1;
 --tw-scale-y: 1;
 transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
 opacity: 1;
}

.modal-container.modal-exit {
 --tw-translate-y: 1rem;
 --tw-scale-x: .95;
 --tw-scale-y: .95;
 transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
 opacity: 0;
}

/* 大型弹窗样式 */
.modal-large {
 border-radius: 1rem;
 border-width: 1px;
 border-color: rgb(245 158 11 / 0.3);
 --tw-bg-opacity: 1;
 background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
 --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
 --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
 box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
 display: flex;
 max-height: 85vh;
 width: 100%;
 max-width: 72rem;
 flex-direction: column;
}

/* 中型弹窗样式 */
.modal-medium {
 border-radius: 1rem;
 border-width: 1px;
 border-color: rgb(245 158 11 / 0.3);
 --tw-bg-opacity: 1;
 background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
 --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
 --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
 box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
 width: 100%;
 max-width: 42rem;
}

/* 小型弹窗样式 */
.modal-small {
 border-radius: 1rem;
 border-width: 1px;
 border-color: rgb(245 158 11 / 0.3);
 --tw-bg-opacity: 1;
 background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
 --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
 --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
 box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
 width: 100%;
 max-width: 28rem;
}

/* 确认对话框特殊样式 */
.modal-confirm {
 border-radius: 1rem;
 border-width: 1px;
 border-color: rgb(245 158 11 / 0.3);
 --tw-bg-opacity: 1;
 background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
 --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
 --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
 box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
 width: 100%;
 max-width: 28rem;
}

/* 弹窗背景遮罩 */
.modal-backdrop {
 background-color: rgb(51 51 51 / 0.5);
}

.modal-backdrop.modal-enter {
 background-color: rgb(51 51 51 / 0.5);
}

.modal-backdrop.modal-exit {
 background-color: rgb(51 51 51 / 0);
}

/* 删除动画 */
.delete-animation {
 transition-property: all;
 transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
 transition-duration: 300ms;
 transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.delete-animation.deleting {
 pointer-events: none;
 --tw-scale-x: .95;
 --tw-scale-y: .95;
 transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
 opacity: 0;
}

/* 弹窗内容区域 */
.modal-content {
 flex: 1 1 0%;
 overflow-y: auto;
}

.modal-content.large {
 max-height: 75vh;
 min-height: 600px;
 padding: 2rem;
}

.modal-content.medium {
 padding: 1.5rem;
}

.modal-content.small {
 padding: 1.5rem;
}

/* 弹窗头部 */
.modal-header {
 display: flex;
 align-items: center;
 justify-content: space-between;
 padding-left: 1.5rem;
 padding-right: 1.5rem;
 padding-top: 1rem;
 padding-bottom: 1rem;
 border-bottom-width: 1px;
 border-color: rgb(245 158 11 / 0.2);
}

/* 弹窗底部 */
.modal-footer {
 display: flex;
 align-items: center;
 justify-content: flex-end;
 gap: 0.75rem;
 padding-left: 1.5rem;
 padding-right: 1.5rem;
 padding-top: 1rem;
 padding-bottom: 1rem;
 border-top-width: 1px;
 border-color: rgb(55 65 81 / 0.5);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .modal-large {
  max-height: 90vh;
  max-width: 100%;
 }
  
  .modal-content.large {
  max-height: 80vh;
  min-height: 400px;
  padding: 1rem;
 }
  
  .modal-overlay {
  padding: 0.5rem;
 }
}

/* 高性能动画优化 */
.modal-container,
.modal-overlay,
.delete-animation {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 弹窗进入动画关键帧 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(16px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalSlideOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(16px);
  }
}

/* 背景淡入淡出动画 */
@keyframes backdropFadeIn {
  from {
    opacity: 0;
    -webkit-backdrop-filter: blur(0px);
            backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);
  }
}

@keyframes backdropFadeOut {
  from {
    opacity: 1;
    -webkit-backdrop-filter: blur(4px);
            backdrop-filter: blur(4px);
  }
  to {
    opacity: 0;
    -webkit-backdrop-filter: blur(0px);
            backdrop-filter: blur(0px);
  }
}

/* 删除动画关键帧 */
@keyframes deleteItem {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 动画类的应用 */
.animate-modal-enter {
  animation: modalSlideIn 300ms ease-out forwards;
}

.animate-modal-exit {
  animation: modalSlideOut 300ms ease-out forwards;
}

.animate-backdrop-enter {
  animation: backdropFadeIn 300ms ease-out forwards;
}

.animate-backdrop-exit {
  animation: backdropFadeOut 300ms ease-out forwards;
}

.animate-delete {
  animation: deleteItem 300ms ease-out forwards;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.font-primary {
    font-family: var(--font-family-primary);
  }
.font-handwritten {
    font-family: var(--font-family-handwritten);
  }
.font-applied {
    font-family: var(--font-family-applied);
  }
.bg-primary-beige {
    background-color: var(--color-primary-beige);
  }
.bg-primary-green {
    background-color: var(--color-primary-green);
  }
.text-primary-green {
    color: var(--color-primary-green);
  }
.text-dark-gray {
    color: var(--color-dark-gray);
  }
.text-black {
    color: var(--color-black);
  }
.pointer-events-none {
  pointer-events: none;
}
.\!visible {
  visibility: visible !important;
}
.visible {
  visibility: visible;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-left-0 {
  left: -0px;
}
.-left-2 {
  left: -0.5rem;
}
.-left-3 {
  left: -0.75rem;
}
.-right-0 {
  right: -0px;
}
.-right-1 {
  right: -0.25rem;
}
.-right-3 {
  right: -0.75rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-2 {
  top: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1 {
  bottom: 0.25rem;
}
.bottom-2 {
  bottom: 0.5rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-full {
  bottom: 100%;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-3 {
  left: 0.75rem;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-2\.5 {
  top: 0.625rem;
}
.top-4 {
  top: 1rem;
}
.top-full {
  top: 100%;
}
.z-10 {
  z-index: 10;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[60\] {
  z-index: 60;
}
.z-\[70\] {
  z-index: 70;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-full {
  grid-column: 1 / -1;
}
.-m-1 {
  margin: -0.25rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.h-1 {
  height: 0.25rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-3 {
  height: 0.75rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-8 {
  height: 2rem;
}
.h-96 {
  height: 24rem;
}
.h-\[85vh\] {
  height: 85vh;
}
.h-\[calc\(100vh-80px\)\] {
  height: calc(100vh - 80px);
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-32 {
  max-height: 8rem;
}
.max-h-40 {
  max-height: 10rem;
}
.max-h-48 {
  max-height: 12rem;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[120px\] {
  max-height: 120px;
}
.max-h-\[50vh\] {
  max-height: 50vh;
}
.max-h-\[60vh\] {
  max-height: 60vh;
}
.max-h-\[70vh\] {
  max-height: 70vh;
}
.max-h-\[75vh\] {
  max-height: 75vh;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.max-h-\[95vh\] {
  max-height: 95vh;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-\[100px\] {
  min-height: 100px;
}
.min-h-\[200px\] {
  min-height: 200px;
}
.min-h-\[300px\] {
  min-height: 300px;
}
.min-h-\[3rem\] {
  min-height: 3rem;
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[40px\] {
  min-height: 40px;
}
.min-h-\[500px\] {
  min-height: 500px;
}
.min-h-\[600px\] {
  min-height: 600px;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-0\.5 {
  width: 0.125rem;
}
.w-1 {
  width: 0.25rem;
}
.w-1\/2 {
  width: 50%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\/5 {
  width: 40%;
}
.w-3 {
  width: 0.75rem;
}
.w-3\/5 {
  width: 60%;
}
.w-4 {
  width: 1rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-8 {
  width: 2rem;
}
.w-full {
  width: 100%;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-40 {
  min-width: 10rem;
}
.min-w-48 {
  min-width: 12rem;
}
.min-w-\[120px\] {
  min-width: 120px;
}
.min-w-\[140px\] {
  min-width: 140px;
}
.min-w-\[20px\] {
  min-width: 20px;
}
.min-w-\[24px\] {
  min-width: 24px;
}
.min-w-\[2rem\] {
  min-width: 2rem;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[120px\] {
  max-width: 120px;
}
.max-w-\[80\%\] {
  max-width: 80%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce {

  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce {
  animation: bounce 1s infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-col-resize {
  cursor: col-resize;
}
.cursor-default {
  cursor: default;
}
.cursor-move {
  cursor: move;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-line {
  white-space: pre-line;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 1rem;
}
.rounded-md {
  border-radius: 0.5rem;
}
.rounded-xl {
  border-radius: 1.5rem;
}
.rounded-b-lg {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-l-md {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-r {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.rounded-r-lg {
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
}
.rounded-r-md {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rounded-t-lg {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.border-amber-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}
.border-amber-400\/20 {
  border-color: rgb(251 191 36 / 0.2);
}
.border-amber-400\/30 {
  border-color: rgb(251 191 36 / 0.3);
}
.border-amber-500 {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.border-amber-500\/10 {
  border-color: rgb(245 158 11 / 0.1);
}
.border-amber-500\/20 {
  border-color: rgb(245 158 11 / 0.2);
}
.border-amber-500\/30 {
  border-color: rgb(245 158 11 / 0.3);
}
.border-amber-500\/40 {
  border-color: rgb(245 158 11 / 0.4);
}
.border-amber-500\/50 {
  border-color: rgb(245 158 11 / 0.5);
}
.border-amber-500\/70 {
  border-color: rgb(245 158 11 / 0.7);
}
.border-amber-600\/50 {
  border-color: rgb(217 119 6 / 0.5);
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-400\/20 {
  border-color: rgb(96 165 250 / 0.2);
}
.border-blue-400\/30 {
  border-color: rgb(96 165 250 / 0.3);
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-500\/10 {
  border-color: rgb(59 130 246 / 0.1);
}
.border-blue-500\/20 {
  border-color: rgb(59 130 246 / 0.2);
}
.border-blue-500\/30 {
  border-color: rgb(59 130 246 / 0.3);
}
.border-blue-500\/50 {
  border-color: rgb(59 130 246 / 0.5);
}
.border-blue-500\/70 {
  border-color: rgb(59 130 246 / 0.7);
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.border-gray-500\/30 {
  border-color: rgb(107 114 128 / 0.3);
}
.border-gray-500\/50 {
  border-color: rgb(107 114 128 / 0.5);
}
.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.border-gray-600\/30 {
  border-color: rgb(75 85 99 / 0.3);
}
.border-gray-600\/50 {
  border-color: rgb(75 85 99 / 0.5);
}
.border-gray-700\/50 {
  border-color: rgb(55 65 81 / 0.5);
}
.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.border-gray-900 {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-green-400\/30 {
  border-color: rgb(74 222 128 / 0.3);
}
.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-green-500\/20 {
  border-color: rgb(34 197 94 / 0.2);
}
.border-green-500\/30 {
  border-color: rgb(34 197 94 / 0.3);
}
.border-green-500\/50 {
  border-color: rgb(34 197 94 / 0.5);
}
.border-green-500\/70 {
  border-color: rgb(34 197 94 / 0.7);
}
.border-orange-500\/30 {
  border-color: rgb(249 115 22 / 0.3);
}
.border-orange-500\/50 {
  border-color: rgb(249 115 22 / 0.5);
}
.border-primary-green {
  --tw-border-opacity: 1;
  border-color: rgb(143 188 143 / var(--tw-border-opacity, 1));
}
.border-purple-400 {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.border-purple-400\/30 {
  border-color: rgb(192 132 252 / 0.3);
}
.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-purple-500\/20 {
  border-color: rgb(168 85 247 / 0.2);
}
.border-purple-500\/30 {
  border-color: rgb(168 85 247 / 0.3);
}
.border-purple-500\/50 {
  border-color: rgb(168 85 247 / 0.5);
}
.border-purple-500\/70 {
  border-color: rgb(168 85 247 / 0.7);
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-500\/20 {
  border-color: rgb(239 68 68 / 0.2);
}
.border-red-500\/30 {
  border-color: rgb(239 68 68 / 0.3);
}
.border-red-500\/50 {
  border-color: rgb(239 68 68 / 0.5);
}
.border-red-600\/50 {
  border-color: rgb(220 38 38 / 0.5);
}
.border-transparent {
  border-color: transparent;
}
.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}
.border-yellow-500\/30 {
  border-color: rgb(234 179 8 / 0.3);
}
.border-yellow-500\/50 {
  border-color: rgb(234 179 8 / 0.5);
}
.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-t-amber-400 {
  --tw-border-opacity: 1;
  border-top-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}
.border-t-blue-400 {
  --tw-border-opacity: 1;
  border-top-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.border-opacity-30 {
  --tw-border-opacity: 0.3;
}
.bg-amber-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}
.bg-amber-400\/10 {
  background-color: rgb(251 191 36 / 0.1);
}
.bg-amber-400\/70 {
  background-color: rgb(251 191 36 / 0.7);
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-amber-500\/10 {
  background-color: rgb(245 158 11 / 0.1);
}
.bg-amber-500\/20 {
  background-color: rgb(245 158 11 / 0.2);
}
.bg-amber-500\/30 {
  background-color: rgb(245 158 11 / 0.3);
}
.bg-amber-500\/5 {
  background-color: rgb(245 158 11 / 0.05);
}
.bg-amber-500\/50 {
  background-color: rgb(245 158 11 / 0.5);
}
.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}
.bg-amber-600\/30 {
  background-color: rgb(217 119 6 / 0.3);
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 51 / var(--tw-bg-opacity, 1));
}
.bg-black\/20 {
  background-color: rgb(51 51 51 / 0.2);
}
.bg-black\/50 {
  background-color: rgb(51 51 51 / 0.5);
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-400\/10 {
  background-color: rgb(96 165 250 / 0.1);
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/10 {
  background-color: rgb(59 130 246 / 0.1);
}
.bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-600\/30 {
  background-color: rgb(37 99 235 / 0.3);
}
.bg-blue-900\/10 {
  background-color: rgb(30 58 138 / 0.1);
}
.bg-blue-900\/20 {
  background-color: rgb(30 58 138 / 0.2);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/20 {
  background-color: rgb(107 114 128 / 0.2);
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-600\/20 {
  background-color: rgb(75 85 99 / 0.2);
}
.bg-gray-600\/30 {
  background-color: rgb(75 85 99 / 0.3);
}
.bg-gray-600\/50 {
  background-color: rgb(75 85 99 / 0.5);
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-gray-700\/30 {
  background-color: rgb(55 65 81 / 0.3);
}
.bg-gray-700\/50 {
  background-color: rgb(55 65 81 / 0.5);
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-800\/20 {
  background-color: rgb(31 41 55 / 0.2);
}
.bg-gray-800\/30 {
  background-color: rgb(31 41 55 / 0.3);
}
.bg-gray-800\/50 {
  background-color: rgb(31 41 55 / 0.5);
}
.bg-gray-800\/70 {
  background-color: rgb(31 41 55 / 0.7);
}
.bg-gray-800\/80 {
  background-color: rgb(31 41 55 / 0.8);
}
.bg-gray-800\/95 {
  background-color: rgb(31 41 55 / 0.95);
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-gray-900\/50 {
  background-color: rgb(17 24 39 / 0.5);
}
.bg-gray-900\/90 {
  background-color: rgb(17 24 39 / 0.9);
}
.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/10 {
  background-color: rgb(34 197 94 / 0.1);
}
.bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}
.bg-green-500\/30 {
  background-color: rgb(34 197 94 / 0.3);
}
.bg-green-500\/5 {
  background-color: rgb(34 197 94 / 0.05);
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-600\/20 {
  background-color: rgb(22 163 74 / 0.2);
}
.bg-green-600\/30 {
  background-color: rgb(22 163 74 / 0.3);
}
.bg-green-900\/20 {
  background-color: rgb(20 83 45 / 0.2);
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-orange-500\/20 {
  background-color: rgb(249 115 22 / 0.2);
}
.bg-orange-900\/20 {
  background-color: rgb(124 45 18 / 0.2);
}
.bg-orange-900\/30 {
  background-color: rgb(124 45 18 / 0.3);
}
.bg-pink-500\/20 {
  background-color: rgb(236 72 153 / 0.2);
}
.bg-primary-beige {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 220 / var(--tw-bg-opacity, 1));
}
.bg-primary-green {
  --tw-bg-opacity: 1;
  background-color: rgb(143 188 143 / var(--tw-bg-opacity, 1));
}
.bg-purple-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-500\/10 {
  background-color: rgb(168 85 247 / 0.1);
}
.bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2);
}
.bg-purple-500\/30 {
  background-color: rgb(168 85 247 / 0.3);
}
.bg-purple-500\/5 {
  background-color: rgb(168 85 247 / 0.05);
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-purple-600\/30 {
  background-color: rgb(147 51 234 / 0.3);
}
.bg-purple-900\/20 {
  background-color: rgb(88 28 135 / 0.2);
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/10 {
  background-color: rgb(239 68 68 / 0.1);
}
.bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2);
}
.bg-red-500\/30 {
  background-color: rgb(239 68 68 / 0.3);
}
.bg-red-500\/5 {
  background-color: rgb(239 68 68 / 0.05);
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-red-900\/20 {
  background-color: rgb(127 29 29 / 0.2);
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500\/20 {
  background-color: rgb(234 179 8 / 0.2);
}
.bg-yellow-500\/30 {
  background-color: rgb(234 179 8 / 0.3);
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500\/10 {
  background-color: rgb(234 179 8 / 0.1);
}
.bg-opacity-10 {
  --tw-bg-opacity: 0.1;
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-amber-500 {
  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/10 {
  --tw-gradient-from: rgb(245 158 11 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/20 {
  --tw-gradient-from: rgb(245 158 11 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-600\/20 {
  --tw-gradient-from: rgb(217 119 6 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-900\/15 {
  --tw-gradient-from: rgb(120 53 15 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(120 53 15 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-100 {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-800 {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900 {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900\/50 {
  --tw-gradient-from: rgb(17 24 39 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-amber-500\/30 {
  --tw-gradient-to: rgb(245 158 11 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(245 158 11 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gray-800 {
  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-amber-500\/10 {
  --tw-gradient-to: rgb(245 158 11 / 0.1) var(--tw-gradient-to-position);
}
.to-amber-500\/20 {
  --tw-gradient-to: rgb(245 158 11 / 0.2) var(--tw-gradient-to-position);
}
.to-amber-600 {
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}
.to-amber-600\/10 {
  --tw-gradient-to: rgb(217 119 6 / 0.1) var(--tw-gradient-to-position);
}
.to-amber-600\/20 {
  --tw-gradient-to: rgb(217 119 6 / 0.2) var(--tw-gradient-to-position);
}
.to-amber-800\/5 {
  --tw-gradient-to: rgb(146 64 14 / 0.05) var(--tw-gradient-to-position);
}
.to-black {
  --tw-gradient-to: #333333 var(--tw-gradient-to-position);
}
.to-gray-800 {
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}
.to-gray-800\/30 {
  --tw-gradient-to: rgb(31 41 55 / 0.3) var(--tw-gradient-to-position);
}
.to-gray-900 {
  --tw-gradient-to: #111827 var(--tw-gradient-to-position);
}
.to-purple-100 {
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}
.to-purple-400 {
  --tw-gradient-to: #c084fc var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.font-handwritten {
  font-family: Comic Sans MS, cursive, var(--font-family-primary);
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-primary {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
}
.text-2xl {
  font-size: 1.5rem;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-base {
  font-size: 1rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-sm {
  font-size: 0.875rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-xs {
  font-size: 0.75rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.text-amber-100 {
  --tw-text-opacity: 1;
  color: rgb(254 243 199 / var(--tw-text-opacity, 1));
}
.text-amber-200 {
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}
.text-amber-200\/70 {
  color: rgb(253 230 138 / 0.7);
}
.text-amber-200\/80 {
  color: rgb(253 230 138 / 0.8);
}
.text-amber-300 {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-amber-500\/70 {
  color: rgb(245 158 11 / 0.7);
}
.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity, 1));
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-300\/70 {
  color: rgb(147 197 253 / 0.7);
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-dark-gray {
  --tw-text-opacity: 1;
  color: rgb(102 102 102 / var(--tw-text-opacity, 1));
}
.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-200 {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-orange-200 {
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}
.text-orange-300 {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.text-pink-300 {
  --tw-text-opacity: 1;
  color: rgb(249 168 212 / var(--tw-text-opacity, 1));
}
.text-primary-green {
  --tw-text-opacity: 1;
  color: rgb(143 188 143 / var(--tw-text-opacity, 1));
}
.text-purple-200 {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.text-purple-300 {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-200 {
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}
.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.text-yellow-300\/70 {
  color: rgb(253 224 71 / 0.7);
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-90 {
  opacity: 0.9;
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-amber-500\/10 {
  --tw-shadow-color: rgb(245 158 11 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-amber-500\/20 {
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-amber-500\/25 {
  --tw-shadow-color: rgb(245 158 11 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-black\/20 {
  --tw-shadow-color: rgb(51 51 51 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-black\/30 {
  --tw-shadow-color: rgb(51 51 51 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/20 {
  --tw-shadow-color: rgb(59 130 246 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/20 {
  --tw-shadow-color: rgb(34 197 94 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/20 {
  --tw-shadow-color: rgb(168 85 247 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline {
  outline-style: solid;
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-amber-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity, 1));
}
.ring-amber-500\/20 {
  --tw-ring-color: rgb(245 158 11 / 0.2);
}
.ring-amber-500\/50 {
  --tw-ring-color: rgb(245 158 11 / 0.5);
}
.ring-blue-500\/50 {
  --tw-ring-color: rgb(59 130 246 / 0.5);
}
.ring-opacity-50 {
  --tw-ring-opacity: 0.5;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.\!filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 导入自定义样式 */

/* 宫崎骏风格色彩变量 */
:root {
  /* 主色调 - 宫崎骏风格 */
  --color-primary-green: #8FBC8F;      /* 海绿色 - 主要色彩 */
  --color-primary-beige: #F5F5DC;      /* 米色 - 背景色 */
  --color-primary-blue: #B0E0E6;       /* 粉蓝色 - 辅助色 */
  
  /* 扩展色彩 */
  --color-forest-green: #228B22;       /* 森林绿 */
  --color-sage-green: #9CAF88;         /* 鼠尾草绿 */
  --color-cream: #FFFDD0;              /* 奶油色 */
  --color-soft-yellow: #FFFFE0;        /* 柔和黄 */
  --color-lavender: #E6E6FA;           /* 薰衣草紫 */
  --color-peach: #FFCBA4;              /* 桃色 */
  
  /* 中性色 */
  --color-white: #FFFFFF;
  --color-light-gray: #F8F8F8;
  --color-gray: #CCCCCC;
  --color-dark-gray: #666666;
  --color-black: #333333;
  
  /* 功能色彩 */
  --color-success: #90EE90;            /* 成功绿 */
  --color-warning: #FFD700;            /* 警告金 */
  --color-error: #FFB6C1;              /* 错误粉 */
  --color-info: #87CEEB;               /* 信息蓝 */
  
  /* 字体 */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-family-handwritten: 'Comic Sans MS', cursive, var(--font-family-primary);
  --font-family-applied: var(--font-family-primary); /* 用户应用的字体 */
  
  /* 字体大小 */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  
  /* 间距 */
  --spacing-xs: 0.25rem;       /* 4px */
  --spacing-sm: 0.5rem;        /* 8px */
  --spacing-md: 1rem;          /* 16px */
  --spacing-lg: 1.5rem;        /* 24px */
  --spacing-xl: 2rem;          /* 32px */
  --spacing-2xl: 3rem;         /* 48px */
  --spacing-3xl: 4rem;         /* 64px */
  
  /* 圆角 */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.5rem;   /* 8px */
  --border-radius-lg: 1rem;     /* 16px */
  --border-radius-xl: 1.5rem;   /* 24px */
  --border-radius-full: 9999px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 手绘风格阴影 */
  --shadow-handdrawn: 2px 2px 8px rgba(143, 188, 143, 0.3);
  --shadow-handdrawn-hover: 4px 4px 12px rgba(143, 188, 143, 0.4);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* 手绘风格过渡 */
  --transition-handdrawn: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: var(--font-family-applied, var(--font-family-primary)) !important;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-black);
  background: linear-gradient(135deg, var(--color-primary-beige) 0%, var(--color-cream) 100%);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 白名单方式：只对指定元素应用用户字体 */
/* 页面标题和导航 */
header h1, header h2, header p,
nav, nav *,

/* 主要内容区域 */
.welcome-content, .welcome-content *,
.artwork-title, .card-title,

/* 按钮和交互元素（排除Monaco Editor） */
.btn:not(.monaco-editor .btn),
button:not(.monaco-editor button),

/* 卡片内容（排除Monaco Editor） */
.card:not(.monaco-editor .card), .card:not(.monaco-editor .card) *,

/* 特定的字体应用类 */
.font-applied, .font-handwritten, .font-primary,
.handdrawn-text,

/* 表单元素（排除Monaco Editor） */
input:not(.monaco-editor input),
textarea:not(.monaco-editor textarea),
label:not(.monaco-editor label),
select:not(.monaco-editor select),

/* 文本内容（排除Monaco Editor） */
p:not(.monaco-editor p),
span:not(.monaco-editor span),
div:not(.monaco-editor div):not(.monaco-editor),
a:not(.monaco-editor a),

/* 标题（排除Monaco Editor） */
h1:not(.monaco-editor h1), h2:not(.monaco-editor h2), h3:not(.monaco-editor h3), 
h4:not(.monaco-editor h4), h5:not(.monaco-editor h5), h6:not(.monaco-editor h6),

/* 编辑器页面特定元素 */
.file-tree, .file-tree *,
.ai-assistant, .ai-assistant *,
.editor-toolbar, .editor-toolbar *,
.editor-settings, .editor-settings *,
.file-name, .file-status,
.sidebar, .sidebar *,
.panel-header, .panel-content {
  font-family: var(--font-family-applied, var(--font-family-primary)) !important;
}

a {
  color: inherit;
  text-decoration: none;
}

/* 手绘风格动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

@keyframes gentle-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes handdrawn-wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(0.5deg); }
  75% { transform: rotate(-0.5deg); }
}

/* 加载屏幕 */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-primary-beige) 0%, var(--color-cream) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-animation {
  text-align: center;
  color: var(--color-primary-green);
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  font-family: var(--font-family-handwritten);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-gray);
  border-top: 3px solid var(--color-primary-green);
  border-radius: var(--border-radius-full);
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: var(--transition-handdrawn);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-primary-green);
  color: var(--color-white);
  box-shadow: var(--shadow-handdrawn);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-forest-green);
  box-shadow: var(--shadow-handdrawn-hover);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--color-primary-blue);
  color: var(--color-white);
  box-shadow: var(--shadow-handdrawn);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-info);
  box-shadow: var(--shadow-handdrawn-hover);
  transform: translateY(-2px);
}

/* 卡片基础样式 */
.card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  transition: var(--transition-handdrawn);
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(143, 188, 143, 0.2);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
  border-color: rgba(143, 188, 143, 0.4);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary-green), var(--color-primary-blue));
  border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

/* 手绘风格装饰类 */
.handdrawn-float {
  animation: float 3s ease-in-out infinite;
}

.handdrawn-pulse {
  animation: gentle-pulse 2s ease-in-out infinite;
}

.handdrawn-wiggle {
  animation: handdrawn-wiggle 4s ease-in-out infinite;
}

/* 手绘风格文本 */
.handdrawn-text {
  font-family: var(--font-family-handwritten);
  position: relative;
  display: inline-block;
}

.handdrawn-text::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    var(--color-primary-green) 20%, 
    var(--color-primary-green) 80%, 
    transparent 100%);
  opacity: 0.6;
  transform: scaleX(0);
  transition: var(--transition-handdrawn);
}

.handdrawn-text:hover::after {
  transform: scaleX(1);
}

/* 手绘风格按钮增强 */
.btn-handdrawn {
  position: relative;
  overflow: visible;
}

.btn-handdrawn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  border-radius: var(--border-radius-full);
  transform: translate(-50%, -50%);
  transition: var(--transition-fast);
}

.btn-handdrawn:hover::before {
  width: 120%;
  height: 120%;
}

/* 手绘风格卡片增强 */
.card-handdrawn {
  position: relative;
  background: #FFFFFF;
  border: 2px solid #8FBC8F;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-handdrawn::after {
  content: '';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, 
    var(--color-primary-green) 0%, 
    transparent 70%);
  border-radius: var(--border-radius-full);
  opacity: 0.2;
  animation: gentle-pulse 3s ease-in-out infinite;
}

/* Tailwind 自定义类 */

/* 全局自定义滚动条样式 - 宫崎骏手绘风格 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(143, 188, 143, 0.4) rgba(55, 65, 81, 0.2);
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.2);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(143, 188, 143, 0.4) 0%, rgba(143, 188, 143, 0.6) 100%);
  border-radius: 4px;
  border: 1px solid rgba(143, 188, 143, 0.2);
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(143, 188, 143, 0.6) 0%, rgba(143, 188, 143, 0.8) 100%);
}

*::-webkit-scrollbar-corner {
  background: rgba(55, 65, 81, 0.2);
}

/* 特殊容器的滚动条样式增强 */
.overflow-auto,
.overflow-y-auto,
.overflow-x-auto,
.overflow-scroll {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(143, 188, 143, 0.4) rgba(55, 65, 81, 0.2) !important;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .card-handdrawn::after {
    width: 15px;
    height: 15px;
    top: 8px;
    right: 8px;
  }
  
  /* 移动端滚动条调整 */
  *::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}
/* 
Monaco Editor 语法高亮样式覆盖 */
.monaco-editor {
  /* 确保Monaco Editor不受全局字体影响 */
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
}

/* Monaco Editor 语法高亮颜色覆盖 */
.monaco-editor .token.delimiter,
.monaco-editor .token.bracket,
.monaco-editor .token.delimiter.bracket,
.monaco-editor .token.delimiter.parenthesis,
.monaco-editor .token.delimiter.square,
.monaco-editor .token.delimiter.curly,
.monaco-editor .token.punctuation,
.monaco-editor .token.punctuation.bracket,
.monaco-editor .token.punctuation.definition,
.monaco-editor .token.operator {
  color: #FFD700 !important; /* 金色符号 */
}

.monaco-editor .token.string,
.monaco-editor .token.string.quoted,
.monaco-editor .token.string.quoted.double,
.monaco-editor .token.string.quoted.single {
  color: #98FB98 !important; /* 浅绿色字符串 */
}

/* Monaco Editor 特定元素样式 */
.monaco-editor .bracket-match {
  background: rgba(255, 215, 0, 0.2) !important; /* 括号匹配高亮 */
  border: 1px solid #FFD700 !important;
}

.monaco-editor .current-line {
  background: rgba(255, 215, 0, 0.1) !important; /* 当前行高亮 */
}

/* Monaco Editor 查找替换对话框样式 */
.monaco-editor .find-widget,
.monaco-editor .replace-widget {
  background: #2A2A1A !important;
  border: 1px solid #FFD700 !important;
  color: #E6D7B7 !important;
}

.monaco-editor .find-widget input,
.monaco-editor .replace-widget input {
  background: #1A1A1A !important;
  color: #E6D7B7 !important;
  border: 1px solid #FFD700 !important;
}

/* Monaco Editor 建议框样式 */
.monaco-editor .suggest-widget {
  background: #2A2A1A !important;
  border: 1px solid #FFD700 !important;
  color: #E6D7B7 !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row {
  color: #E6D7B7 !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.focused {
  background: rgba(255, 215, 0, 0.2) !important;
}

/* Monaco Editor 悬停提示样式 */
.monaco-editor .monaco-hover {
  background: #2A2A1A !important;
  border: 1px solid #FFD700 !important;
  color: #E6D7B7 !important;
}

/* Monaco Editor 参数提示样式 */
.monaco-editor .parameter-hints-widget {
  background: #2A2A1A !important;
  border: 1px solid #FFD700 !important;
  color: #E6D7B7 !important;
}/* 文本截断工具类 *
/
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(245, 158, 11, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(245, 158, 11, 0.5);
}

.hover\:bg-primary-green:hover {
    background-color: var(--color-primary-green);
  }

.focus-within\:border-amber-500:focus-within {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-amber-500:focus-within {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity, 1));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-l-4:hover {
  border-left-width: 4px;
}

.hover\:border-amber-400\/40:hover {
  border-color: rgb(251 191 36 / 0.4);
}

.hover\:border-amber-400\/70:hover {
  border-color: rgb(251 191 36 / 0.7);
}

.hover\:border-amber-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}

.hover\:border-amber-500\/30:hover {
  border-color: rgb(245 158 11 / 0.3);
}

.hover\:border-amber-500\/50:hover {
  border-color: rgb(245 158 11 / 0.5);
}

.hover\:border-amber-500\/70:hover {
  border-color: rgb(245 158 11 / 0.7);
}

.hover\:border-blue-400\/40:hover {
  border-color: rgb(96 165 250 / 0.4);
}

.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-500\/50:hover {
  border-color: rgb(59 130 246 / 0.5);
}

.hover\:border-gray-600\/50:hover {
  border-color: rgb(75 85 99 / 0.5);
}

.hover\:border-primary-green:hover {
  --tw-border-opacity: 1;
  border-color: rgb(143 188 143 / var(--tw-border-opacity, 1));
}

.hover\:border-red-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}

.hover\:border-red-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.hover\:border-red-500\/50:hover {
  border-color: rgb(239 68 68 / 0.5);
}

.hover\:border-yellow-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}

.hover\:bg-amber-400\/20:hover {
  background-color: rgb(251 191 36 / 0.2);
}

.hover\:bg-amber-500\/10:hover {
  background-color: rgb(245 158 11 / 0.1);
}

.hover\:bg-amber-500\/20:hover {
  background-color: rgb(245 158 11 / 0.2);
}

.hover\:bg-amber-500\/30:hover {
  background-color: rgb(245 158 11 / 0.3);
}

.hover\:bg-amber-500\/5:hover {
  background-color: rgb(245 158 11 / 0.05);
}

.hover\:bg-amber-600\/40:hover {
  background-color: rgb(217 119 6 / 0.4);
}

.hover\:bg-amber-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}

.hover\:bg-black\/20:hover {
  background-color: rgb(51 51 51 / 0.2);
}

.hover\:bg-blue-400\/20:hover {
  background-color: rgb(96 165 250 / 0.2);
}

.hover\:bg-blue-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-500\/10:hover {
  background-color: rgb(59 130 246 / 0.1);
}

.hover\:bg-blue-500\/20:hover {
  background-color: rgb(59 130 246 / 0.2);
}

.hover\:bg-blue-500\/30:hover {
  background-color: rgb(59 130 246 / 0.3);
}

.hover\:bg-blue-500\/5:hover {
  background-color: rgb(59 130 246 / 0.05);
}

.hover\:bg-blue-600\/40:hover {
  background-color: rgb(37 99 235 / 0.4);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-500\/30:hover {
  background-color: rgb(107 114 128 / 0.3);
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700\/20:hover {
  background-color: rgb(55 65 81 / 0.2);
}

.hover\:bg-gray-700\/50:hover {
  background-color: rgb(55 65 81 / 0.5);
}

.hover\:bg-gray-700\/70:hover {
  background-color: rgb(55 65 81 / 0.7);
}

.hover\:bg-gray-800\/50:hover {
  background-color: rgb(31 41 55 / 0.5);
}

.hover\:bg-green-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-500\/10:hover {
  background-color: rgb(34 197 94 / 0.1);
}

.hover\:bg-green-500\/20:hover {
  background-color: rgb(34 197 94 / 0.2);
}

.hover\:bg-green-500\/30:hover {
  background-color: rgb(34 197 94 / 0.3);
}

.hover\:bg-green-600\/40:hover {
  background-color: rgb(22 163 74 / 0.4);
}

.hover\:bg-orange-500\/30:hover {
  background-color: rgb(249 115 22 / 0.3);
}

.hover\:bg-primary-green:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(143 188 143 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-500\/10:hover {
  background-color: rgb(168 85 247 / 0.1);
}

.hover\:bg-purple-500\/20:hover {
  background-color: rgb(168 85 247 / 0.2);
}

.hover\:bg-purple-500\/30:hover {
  background-color: rgb(168 85 247 / 0.3);
}

.hover\:bg-purple-500\/40:hover {
  background-color: rgb(168 85 247 / 0.4);
}

.hover\:bg-purple-600\/40:hover {
  background-color: rgb(147 51 234 / 0.4);
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500\/10:hover {
  background-color: rgb(239 68 68 / 0.1);
}

.hover\:bg-red-500\/20:hover {
  background-color: rgb(239 68 68 / 0.2);
}

.hover\:bg-red-500\/30:hover {
  background-color: rgb(239 68 68 / 0.3);
}

.hover\:bg-red-500\/50:hover {
  background-color: rgb(239 68 68 / 0.5);
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-sage-green:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(156 175 136 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-500\/30:hover {
  background-color: rgb(234 179 8 / 0.3);
}

.hover\:bg-opacity-20:hover {
  --tw-bg-opacity: 0.2;
}

.hover\:bg-opacity-30:hover {
  --tw-bg-opacity: 0.3;
}

.hover\:from-amber-500\/30:hover {
  --tw-gradient-from: rgb(245 158 11 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-amber-600:hover {
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-amber-600\/30:hover {
  --tw-gradient-from: rgb(217 119 6 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-amber-900\/25:hover {
  --tw-gradient-from: rgb(120 53 15 / 0.25) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(120 53 15 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-amber-500\/30:hover {
  --tw-gradient-to: rgb(245 158 11 / 0.3) var(--tw-gradient-to-position);
}

.hover\:to-amber-600\/30:hover {
  --tw-gradient-to: rgb(217 119 6 / 0.3) var(--tw-gradient-to-position);
}

.hover\:to-amber-700:hover {
  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);
}

.hover\:to-amber-800\/15:hover {
  --tw-gradient-to: rgb(146 64 14 / 0.15) var(--tw-gradient-to-position);
}

.hover\:text-amber-100:hover {
  --tw-text-opacity: 1;
  color: rgb(254 243 199 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-200:hover {
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-300:hover {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

.hover\:text-amber-400:hover {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-100:hover {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-200:hover {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-400:hover {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.hover\:text-green-100:hover {
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}

.hover\:text-green-400:hover {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-100:hover {
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-200:hover {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-300:hover {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-400:hover {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.hover\:text-red-300:hover {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.hover\:text-red-400:hover {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-red-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.hover\:opacity-10:hover {
  opacity: 0.1;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-60:hover {
  opacity: 0.6;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-amber-500\/20:hover {
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-amber-500\/30:hover {
  --tw-shadow-color: rgb(245 158 11 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.focus\:border-amber-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}

.focus\:border-amber-500\/50:focus {
  border-color: rgb(245 158 11 / 0.5);
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-blue-500\/60:focus {
  border-color: rgb(59 130 246 / 0.6);
}

.focus\:border-primary-green:focus {
  --tw-border-opacity: 1;
  border-color: rgb(143 188 143 / var(--tw-border-opacity, 1));
}

.focus\:border-purple-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-amber-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity, 1));
}

.focus\:ring-amber-500\/30:focus {
  --tw-ring-color: rgb(245 158 11 / 0.3);
}

.focus\:ring-amber-500\/50:focus {
  --tw-ring-color: rgb(245 158 11 / 0.5);
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-500\/50:focus {
  --tw-ring-color: rgb(59 130 246 / 0.5);
}

.focus\:ring-green-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(187 247 208 / var(--tw-ring-opacity, 1));
}

.focus\:ring-purple-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500\/50:focus {
  --tw-ring-color: rgb(239 68 68 / 0.5);
}

.focus\:ring-opacity-50:focus {
  --tw-ring-opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-gray-600:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:hover\:scale-100:hover:disabled {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-amber-500\/15 {
  background-color: rgb(245 158 11 / 0.15);
}

.group:hover .group-hover\:bg-amber-500\/20 {
  background-color: rgb(245 158 11 / 0.2);
}

.group:hover .group-hover\:bg-amber-500\/30 {
  background-color: rgb(245 158 11 / 0.3);
}

.group:hover .group-hover\:bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}

.group:hover .group-hover\:text-amber-100 {
  --tw-text-opacity: 1;
  color: rgb(254 243 199 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

@media (min-width: 640px) {

  .sm\:max-h-\[90vh\] {
    max-height: 90vh;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:p-4 {
    padding: 1rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {

  .xl\:w-2\/5 {
    width: 40%;
  }

  .xl\:w-3\/5 {
    width: 60%;
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:flex-row {
    flex-direction: row;
  }
}

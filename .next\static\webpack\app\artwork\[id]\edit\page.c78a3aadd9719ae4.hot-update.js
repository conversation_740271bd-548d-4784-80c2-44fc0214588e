"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _dragEnhancerRef_current, _selectionManagerRef_current, _clipboardManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.destroy();\n        dragEnhancerRef.current = null;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_clipboardManagerRef_current = clipboardManagerRef.current) === null || _clipboardManagerRef_current === void 0 ? void 0 : _clipboardManagerRef_current.destroy();\n        clipboardManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 暂时禁用拖拽增强器，让SimpleMindMap原生拖拽功能正常工作\n            // TODO: 后续优化拖拽增强器与原生功能的兼容性\n            /*\r\n      if (!readonly && enableDragEnhancement) {\r\n        const enhancedDragConfig = {\r\n          ...dragConfig,\r\n          persistence: {\r\n            autoSave: dragConfig.persistence?.autoSave ?? true,\r\n            saveDelay: dragConfig.persistence?.saveDelay ?? 1000,\r\n            onSave: (data: any) => {\r\n              if (onDataChange) {\r\n                onDataChange(data);\r\n              }\r\n              if (dragConfig.persistence?.onSave) {\r\n                dragConfig.persistence.onSave(data);\r\n              }\r\n            }\r\n          }\r\n        };\r\n\r\n        dragEnhancerRef.current = new DragEnhancer(mindMapInstance, enhancedDragConfig);\r\n        dragEnhancerRef.current.initialize();\r\n      }\r\n      */ setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            }\n        }), [\n        resizeCanvas,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 462,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 485,\n        columnNumber: 5\n    }, undefined);\n}, \"n9iMgar1C7HJsnxniI5k3ycy3s4=\")), \"n9iMgar1C7HJsnxniI5k3ycy3s4=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts":
/*!*********************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/ClipboardManager.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClipboardManager: function() { return /* binding */ ClipboardManager; }\n/* harmony export */ });\n/**\n * 剪贴板管理器\n * 负责思维导图节点的复制粘贴功能\n */ class ClipboardManager {\n    /**\n   * 复制节点\n   */ copyNode(node) {\n        try {\n            if (!node || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的节点或思维导图实例\");\n                return false;\n            }\n            // 激活目标节点\n            this.mindMapInstance.renderer.clearActiveNodeList();\n            this.mindMapInstance.renderer.addNodeToActiveList(node);\n            // 使用SimpleMindMap的内部复制方法\n            const copiedData = this.mindMapInstance.renderer.copyNode();\n            if (copiedData && copiedData.length > 0) {\n                this.copiedNodeData = copiedData;\n                console.log(\"✅ 复制节点成功:\", copiedData);\n                return true;\n            } else {\n                console.warn(\"⚠️ 复制节点失败，没有获取到数据\");\n                return false;\n            }\n        } catch (error) {\n            console.error(\"❌ 复制节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 粘贴节点作为子节点\n   */ pasteAsChild(targetNode) {\n        try {\n            if (!this.hasCopiedData()) {\n                console.warn(\"⚠️ 没有可粘贴的数据\");\n                return false;\n            }\n            if (!targetNode || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的目标节点或思维导图实例\");\n                return false;\n            }\n            // 激活目标节点\n            this.mindMapInstance.renderer.clearActiveNodeList();\n            this.mindMapInstance.renderer.addNodeToActiveList(targetNode);\n            // 执行粘贴命令\n            this.mindMapInstance.execCommand(\"PASTE_NODE\", this.copiedNodeData);\n            console.log(\"✅ 粘贴为子节点成功\");\n            return true;\n        } catch (error) {\n            console.error(\"❌ 粘贴为子节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 粘贴节点作为同级节点\n   */ pasteAsSibling(targetNode) {\n        try {\n            if (!this.hasCopiedData()) {\n                console.warn(\"⚠️ 没有可粘贴的数据\");\n                return false;\n            }\n            if (!targetNode || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的目标节点或思维导图实例\");\n                return false;\n            }\n            // 根节点不能粘贴同级节点\n            if (targetNode.isRoot) {\n                console.warn(\"⚠️ 根节点不能粘贴同级节点\");\n                return false;\n            }\n            // 激活目标节点的父节点\n            if (targetNode.parent) {\n                this.mindMapInstance.renderer.clearActiveNodeList();\n                this.mindMapInstance.renderer.addNodeToActiveList(targetNode.parent);\n                // 执行粘贴命令\n                this.mindMapInstance.execCommand(\"PASTE_NODE\", this.copiedNodeData);\n                console.log(\"✅ 粘贴为同级节点成功\");\n                return true;\n            } else {\n                console.warn(\"⚠️ 目标节点没有父节点\");\n                return false;\n            }\n        } catch (error) {\n            console.error(\"❌ 粘贴为同级节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 检查是否有可粘贴的数据\n   */ hasCopiedData() {\n        return this.copiedNodeData !== null && Array.isArray(this.copiedNodeData) && this.copiedNodeData.length > 0;\n    }\n    /**\n   * 获取复制的数据\n   */ getCopiedData() {\n        return this.copiedNodeData;\n    }\n    /**\n   * 清空复制的数据\n   */ clearCopiedData() {\n        this.copiedNodeData = null;\n        console.log(\"✅ 清空复制数据\");\n    }\n    /**\n   * 获取复制数据的摘要信息\n   */ getCopiedDataSummary() {\n        var _this_copiedNodeData__data, _this_copiedNodeData_;\n        if (!this.hasCopiedData()) {\n            return \"无复制数据\";\n        }\n        const nodeCount = this.copiedNodeData.length;\n        const firstNodeText = ((_this_copiedNodeData_ = this.copiedNodeData[0]) === null || _this_copiedNodeData_ === void 0 ? void 0 : (_this_copiedNodeData__data = _this_copiedNodeData_.data) === null || _this_copiedNodeData__data === void 0 ? void 0 : _this_copiedNodeData__data.text) || \"未知节点\";\n        if (nodeCount === 1) {\n            return \"已复制: \".concat(firstNodeText);\n        } else {\n            return \"已复制: \".concat(firstNodeText, \" 等 \").concat(nodeCount, \" 个节点\");\n        }\n    }\n    /**\n   * 销毁剪贴板管理器\n   */ destroy() {\n        this.clearCopiedData();\n        this.mindMapInstance = null;\n        console.log(\"✅ 剪贴板管理器销毁完成\");\n    }\n    constructor(mindMapInstance){\n        this.copiedNodeData = null;\n        this.mindMapInstance = null;\n        this.mindMapInstance = mindMapInstance;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/EventManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventManager: function() { return /* binding */ EventManager; }\n/* harmony export */ });\n/**\r\n * EventManager - 事件管理器\r\n * 负责SimpleMindMap事件的统一管理和分发\r\n * \r\n * 职责：\r\n * - 监听SimpleMindMap官方事件\r\n * - 事件处理和回调分发\r\n * - 自定义事件逻辑\r\n */ class EventManager {\n    /**\r\n   * 初始化事件监听\r\n   */ initialize() {\n        this.setupNodeEvents();\n        this.setupDataEvents();\n        this.setupRenderEvents();\n        this.setupCanvasEvents();\n        this.isInitialized = true;\n        console.log(\"✅ 事件管理器初始化完成\");\n    }\n    /**\r\n   * 设置节点相关事件\r\n   */ setupNodeEvents() {\n        // 节点点击事件\n        const nodeClickHandler = (node)=>{\n            var _this_config_onNodeClick, _this_config;\n            console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node);\n            (_this_config_onNodeClick = (_this_config = this.config).onNodeClick) === null || _this_config_onNodeClick === void 0 ? void 0 : _this_config_onNodeClick.call(_this_config, node);\n        };\n        // 节点激活事件\n        const nodeActiveHandler = (node, activeNodeList)=>{\n            console.log(\"✨ 节点激活:\", {\n                node,\n                activeNodeList\n            });\n        // 可以在这里添加节点激活的自定义逻辑\n        };\n        // 节点右键菜单事件\n        const nodeContextMenuHandler = (event, node)=>{\n            console.log(\"\\uD83D\\uDDB1️ 节点右键菜单:\", {\n                event,\n                node\n            });\n            // 阻止默认右键菜单\n            event.preventDefault();\n            event.stopPropagation();\n            // 计算菜单位置并显示\n            const position = this.calculateMenuPosition(event);\n            this.showContextMenu({\n                position,\n                node\n            });\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_click\", nodeClickHandler);\n        this.mindMapInstance.on(\"node_active\", nodeActiveHandler);\n        this.mindMapInstance.on(\"node_contextmenu\", nodeContextMenuHandler);\n        // 存储事件处理器用于清理\n        this.eventListeners.set(\"node_click\", nodeClickHandler);\n        this.eventListeners.set(\"node_active\", nodeActiveHandler);\n        this.eventListeners.set(\"node_contextmenu\", nodeContextMenuHandler);\n    }\n    /**\r\n   * 设置数据相关事件\r\n   */ setupDataEvents() {\n        // 数据变更事件\n        const dataChangeHandler = (newData)=>{\n            var _this_config_onDataChange, _this_config;\n            console.log(\"\\uD83D\\uDCCA 数据变更:\", newData);\n            (_this_config_onDataChange = (_this_config = this.config).onDataChange) === null || _this_config_onDataChange === void 0 ? void 0 : _this_config_onDataChange.call(_this_config, newData);\n        };\n        // 数据设置前事件\n        const beforeSetDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 准备设置数据:\", data);\n        };\n        // 数据设置后事件\n        const setDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 数据设置完成:\", data);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"data_change\", dataChangeHandler);\n        this.mindMapInstance.on(\"before_set_data\", beforeSetDataHandler);\n        this.mindMapInstance.on(\"set_data\", setDataHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"data_change\", dataChangeHandler);\n        this.eventListeners.set(\"before_set_data\", beforeSetDataHandler);\n        this.eventListeners.set(\"set_data\", setDataHandler);\n    }\n    /**\r\n   * 设置渲染相关事件\r\n   */ setupRenderEvents() {\n        // 节点树渲染结束事件\n        const nodeTreeRenderEndHandler = ()=>{\n            var _this_config_onRenderComplete, _this_config;\n            console.log(\"\\uD83C\\uDFA8 节点树渲染完成\");\n            // 完全不在EventManager中处理视图适应，交给组件级别控制\n            // 这样可以确保用户的视图位置永远不会被意外重置\n            console.log(\"✅ 渲染完成，保持当前视图位置\");\n            (_this_config_onRenderComplete = (_this_config = this.config).onRenderComplete) === null || _this_config_onRenderComplete === void 0 ? void 0 : _this_config_onRenderComplete.call(_this_config);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n    }\n    /**\r\n   * 设置画布相关事件\r\n   */ setupCanvasEvents() {\n        // 画布点击事件\n        const drawClickHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布点击\");\n            // 隐藏右键菜单\n            if (this.contextMenuVisible) {\n                this.hideContextMenu();\n            }\n        };\n        // 画布拖拽事件\n        const drawDragHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布拖拽\");\n        };\n        // 缩放事件\n        const scaleHandler = (scale)=>{\n            console.log(\"\\uD83D\\uDD0D 缩放变化:\", scale);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"draw_click\", drawClickHandler);\n        this.mindMapInstance.on(\"view_data_change\", drawDragHandler);\n        this.mindMapInstance.on(\"scale\", scaleHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"draw_click\", drawClickHandler);\n        this.eventListeners.set(\"view_data_change\", drawDragHandler);\n        this.eventListeners.set(\"scale\", scaleHandler);\n    }\n    /**\r\n   * 手动触发事件\r\n   */ emit(event) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.emit(event, ...args);\n        } catch (error) {\n            console.error(\"❌ 触发事件失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 添加自定义事件监听器\r\n   */ addEventListener(event, handler) {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.on(event, handler);\n            this.eventListeners.set(event, handler);\n            console.log(\"✅ 添加事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 添加事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 移除事件监听器\r\n   */ removeEventListener(event) {\n        const handler = this.eventListeners.get(event);\n        if (!handler || !this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.off(event, handler);\n            this.eventListeners.delete(event);\n            console.log(\"✅ 移除事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 获取当前激活的节点列表\r\n   */ getActiveNodes() {\n        var _this_mindMapInstance;\n        if (!((_this_mindMapInstance = this.mindMapInstance) === null || _this_mindMapInstance === void 0 ? void 0 : _this_mindMapInstance.renderer)) return [];\n        try {\n            return this.mindMapInstance.renderer.activeNodeList || [];\n        } catch (error) {\n            console.error(\"❌ 获取激活节点失败:\", error);\n            return [];\n        }\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 计算菜单位置，确保不超出视口\r\n   */ calculateMenuPosition(event) {\n        const viewportWidth = window.innerWidth;\n        const viewportHeight = window.innerHeight;\n        let x = event.clientX;\n        let y = event.clientY;\n        // 预估菜单尺寸（实际尺寸会在组件中调整）\n        const estimatedMenuWidth = 200;\n        const estimatedMenuHeight = 300;\n        // 水平方向调整\n        if (x + estimatedMenuWidth > viewportWidth) {\n            x = viewportWidth - estimatedMenuWidth - 10;\n        }\n        if (x < 10) {\n            x = 10;\n        }\n        // 垂直方向调整\n        if (y + estimatedMenuHeight > viewportHeight) {\n            y = viewportHeight - estimatedMenuHeight - 10;\n        }\n        if (y < 10) {\n            y = 10;\n        }\n        return {\n            x,\n            y\n        };\n    }\n    /**\r\n   * 显示右键菜单\r\n   */ showContextMenu(param) {\n        let { position, node } = param;\n        var // 触发菜单显示回调\n        _this_config_onContextMenuShow, _this_config;\n        this.contextMenuPosition = position;\n        this.contextMenuNode = node;\n        this.contextMenuVisible = true;\n        (_this_config_onContextMenuShow = (_this_config = this.config).onContextMenuShow) === null || _this_config_onContextMenuShow === void 0 ? void 0 : _this_config_onContextMenuShow.call(_this_config, position, node);\n        console.log(\"✅ 显示右键菜单:\", {\n            position,\n            node\n        });\n    }\n    /**\r\n   * 隐藏右键菜单\r\n   */ hideContextMenu() {\n        var // 触发菜单隐藏回调\n        _this_config_onContextMenuHide, _this_config;\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        (_this_config_onContextMenuHide = (_this_config = this.config).onContextMenuHide) === null || _this_config_onContextMenuHide === void 0 ? void 0 : _this_config_onContextMenuHide.call(_this_config);\n        console.log(\"✅ 隐藏右键菜单\");\n    }\n    /**\r\n   * 获取右键菜单状态\r\n   */ getContextMenuState() {\n        return {\n            visible: this.contextMenuVisible,\n            position: this.contextMenuPosition,\n            node: this.contextMenuNode\n        };\n    }\n    /**\r\n   * 销毁事件管理器\r\n   */ destroy() {\n        // 移除所有事件监听器\n        for (const [event, handler] of this.eventListeners){\n            try {\n                this.mindMapInstance.off(event, handler);\n            } catch (error) {\n                console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n            }\n        }\n        // 清理右键菜单状态\n        this.hideContextMenu();\n        this.eventListeners.clear();\n        this.isInitialized = false;\n        // 不重置isFirstRender，保持首次渲染状态\n        console.log(\"✅ 事件管理器销毁完成\");\n    }\n    constructor(mindMapInstance, config){\n        this.eventListeners = new Map();\n        this.isInitialized = false;\n        this.isFirstRender = true;\n        // 右键菜单状态管理\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        this.mindMapInstance = mindMapInstance;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleManager: function() { return /* binding */ StyleManager; }\n/* harmony export */ });\n/**\r\n * StyleManager - 样式管理器\r\n * 负责SimpleMindMap主题和样式的统一管理\r\n * \r\n * 职责：\r\n * - 主题切换和配置\r\n * - 自定义样式应用\r\n * - 样式状态管理\r\n * \r\n * 设计原则：\r\n * - 官方API优先：使用SimpleMindMap官方主题API\r\n * - 简洁高效：最小化样式配置，避免过度自定义\r\n */ class StyleManager {\n    /**\r\n   * 初始化样式管理器\r\n   */ async initialize() {\n        try {\n            // 应用初始主题\n            await this.applyTheme(this.currentTheme);\n            this.isInitialized = true;\n            console.log(\"✅ 样式管理器初始化完成，主题:\", this.currentTheme);\n        } catch (error) {\n            console.error(\"❌ 样式管理器初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ async applyTheme(theme) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not available\");\n        }\n        try {\n            // 直接使用我们的自定义主题配置，不依赖SimpleMindMap内置主题\n            const themeConfig = this.getThemeConfig(theme);\n            this.mindMapInstance.setThemeConfig(themeConfig, false);\n            this.currentTheme = theme;\n            console.log(\"✅ 自定义主题应用成功: \".concat(theme), themeConfig);\n        } catch (error) {\n            console.error(\"❌ 主题应用失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式 - 金色高亮\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#FFD700\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 4,\n                fontSize: 13,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#000000\",\n                padding: [\n                    6,\n                    10\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 0.95\n            },\n            // 展开按钮样式 - 金色\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#FFD700\",\n                fillColor: \"#0a0a0a\",\n                strokeColor: \"#FFD700\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式 - 金色系\n            associativeLine: {\n                strokeColor: \"#DAA520\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式 - 亮金色\n            activeNodeStyle: {\n                strokeColor: \"#FFFF00\",\n                strokeWidth: 4,\n                fillColor: \"rgba(255, 215, 0, 0.15)\"\n            },\n            // 悬停状态样式 - 金色光晕\n            hoverNodeStyle: {\n                fillColor: \"rgba(255, 215, 0, 0.1)\",\n                strokeColor: \"#FFA500\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 更新主题\r\n   */ async updateTheme(theme) {\n        if (theme === this.currentTheme) return;\n        await this.applyTheme(theme);\n    }\n    /**\r\n   * 获取当前主题\r\n   */ getCurrentTheme() {\n        return this.currentTheme;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 销毁样式管理器\r\n   */ destroy() {\n        this.isInitialized = false;\n        console.log(\"✅ 样式管理器销毁完成\");\n    }\n    constructor(mindMapInstance, theme = \"dark\"){\n        this.isInitialized = false;\n        this.mindMapInstance = mindMapInstance;\n        this.currentTheme = theme;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CleanupState: function() { return /* binding */ CleanupState; }\n/* harmony export */ });\n/* harmony import */ var _mindmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mindmap */ \"(app-pages-browser)/./src/types/mindmap.ts\");\n/**\r\n * 作品展示平台 - TypeScript 类型定义\r\n */ // 作品内容类型\nvar CleanupState;\n(function(CleanupState) {\n    CleanupState[\"ACTIVE\"] = \"active\";\n    CleanupState[\"DESTROYING\"] = \"destroying\";\n    CleanupState[\"DESTROYED\"] = \"destroyed\"; // 已销毁完成\n})(CleanupState || (CleanupState = {}));\n// ==================== 思维导图相关类型定义 ====================\n// 导出思维导图相关类型\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ })

});
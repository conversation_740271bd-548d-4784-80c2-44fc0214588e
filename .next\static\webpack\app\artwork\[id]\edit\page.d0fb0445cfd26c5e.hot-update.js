"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 SaveManager 集成 - 替换原有的定时器管理\n    const saveManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(_services_SaveManager__WEBPACK_IMPORTED_MODULE_9__.SaveManager.getInstance());\n    const [saveState, setSaveState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 SaveManager 状态变更回调\n    const handleSaveStateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId, state, error)=>{\n        // 只处理当前文件的状态变更\n        if ((file === null || file === void 0 ? void 0 : file.id) === fileId) {\n            setSaveState(state);\n            if (error) {\n                console.error(\"\\uD83D\\uDCBE 保存状态错误:\", error);\n            }\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 初始化 SaveManager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        // 设置保存执行回调\n        saveManager.setSaveExecuteCallback(async (fileId, data, options)=>{\n            if (onContentChange && (file === null || file === void 0 ? void 0 : file.id) === fileId) {\n                try {\n                    onContentChange(data);\n                    return true;\n                } catch (error) {\n                    console.error(\"❌ 保存执行失败:\", error);\n                    return false;\n                }\n            }\n            return false;\n        });\n        // 添加状态变更监听器\n        saveManager.addStateChangeListener(handleSaveStateChange);\n        return ()=>{\n            // 清理监听器\n            saveManager.removeStateChangeListener(handleSaveStateChange);\n        };\n    }, [\n        onContentChange,\n        file === null || file === void 0 ? void 0 : file.id,\n        handleSaveStateChange\n    ]);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 🔧 文件切换时清除所有定时器\n        clearSaveTimer();\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        // 🔧 视图切换时清除所有定时器\n        clearSaveTimer();\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        if (!settings.autoSave || !onContentChange) return;\n        clearSaveTimer();\n        saveTimeoutRef.current = setTimeout(()=>{\n            onContentChange(content);\n        }, settings.autoSaveDelay);\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        onContentChange,\n        clearSaveTimer\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 🔧 增强文件状态验证 - 捕获当前文件ID用于闭包验证\n        const currentFileId = file === null || file === void 0 ? void 0 : file.id;\n        const currentMindMapMode = isMindMapMode;\n        // 基础状态验证\n        if (!file || !currentMindMapMode || !currentFileId) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 🔧 延迟验证，防止异步竞态条件\n        setTimeout(()=>{\n            // 二次验证文件ID一致性，防止跨文件保存\n            if ((file === null || file === void 0 ? void 0 : file.id) !== currentFileId) {\n                console.log(\"⚠️ 文件已切换，跳过保存 (原文件ID:\", currentFileId, \"当前文件ID:\", file === null || file === void 0 ? void 0 : file.id, \")\");\n                return;\n            }\n            // 验证思维导图模式状态\n            if (!isMindMapMode) {\n                console.log(\"⚠️ 已退出思维导图模式，跳过保存\");\n                return;\n            }\n            // 将思维导图数据转换为Markdown格式并保存\n            if (data && onContentChange) {\n                const markdownContent = convertMindMapToMarkdown(data);\n                handleSave(markdownContent);\n                console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存 (文件ID:\", currentFileId, \")\");\n            }\n        }, 0) // 使用微任务延迟，确保状态更新完成\n        ;\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        onContentChange,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearSaveTimer();\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, [\n        clearSaveTimer\n    ]);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 683,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 678,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 677,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 746,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 694,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 902,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 901,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 931,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 919,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 948,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 962,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 970,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 973,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 969,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 990,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 986,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1004,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 899,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 898,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1029,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1033,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1028,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1027,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1039,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1040,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1041,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1038,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1037,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1051,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1025,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1067,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1065,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1069,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1064,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1063,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1076,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1077,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1086,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1078,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1094,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1074,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1073,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1104,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1121,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1122,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1123,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1120,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1119,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1061,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1141,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1142,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1140,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1144,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1138,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1131,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1130,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 692,\n        columnNumber: 5\n    }, undefined);\n}, \"eHE3qNMPD7nMZY1MEjETOXwIYMc=\")), \"eHE3qNMPD7nMZY1MEjETOXwIYMc=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});
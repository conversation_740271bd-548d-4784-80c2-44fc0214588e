/**
 * DragEnhancer 测试文件
 * 验证 DragEnhancer 与 SaveManager 的集成
 */

import { DragEnhancer } from '../DragEnhancer';
import { SaveManager } from '@/services/SaveManager';

// Mock SaveManager
jest.mock('@/services/SaveManager');

describe('DragEnhancer SaveManager Integration', () => {
  let dragEnhancer: DragEnhancer;
  let mockMindMapInstance: any;
  let mockSaveManager: jest.Mocked<SaveManager>;

  beforeEach(() => {
    // 创建 mock MindMap 实例
    mockMindMapInstance = {
      on: jest.fn(),
      off: jest.fn(),
      getData: jest.fn().mockReturnValue({ test: 'data' })
    };

    // 获取 mock SaveManager
    mockSaveManager = SaveManager.getInstance() as jest.Mocked<SaveManager>;
    mockSaveManager.scheduleAutoSave = jest.fn();
    mockSaveManager.clearFileState = jest.fn();

    // 创建 DragEnhancer 实例
    dragEnhancer = new DragEnhancer(mockMindMapInstance, {
      persistence: {
        autoSave: true,
        saveDelay: 500
      }
    });
  });

  afterEach(() => {
    dragEnhancer.destroy();
    jest.clearAllMocks();
  });

  test('应该正确初始化 SaveManager', () => {
    expect(SaveManager.getInstance).toHaveBeenCalled();
  });

  test('应该能设置当前文件ID', () => {
    const fileId = 'test-file-1';
    
    dragEnhancer.setCurrentFileId(fileId);
    
    // 验证文件ID设置成功（通过后续的保存调用验证）
    expect(dragEnhancer).toBeDefined();
  });

  test('应该在数据变更时调用 SaveManager.scheduleAutoSave', () => {
    const fileId = 'test-file-1';
    const testData = { test: 'drag-data' };
    
    dragEnhancer.setCurrentFileId(fileId);
    
    // 模拟数据变更
    (dragEnhancer as any).handleDataChange(testData);
    
    expect(mockSaveManager.scheduleAutoSave).toHaveBeenCalledWith(
      fileId,
      testData,
      {
        debounceDelay: 500,
        saveType: 'drag',
        immediate: false
      }
    );
  });

  test('应该在没有文件ID时跳过保存', () => {
    const testData = { test: 'drag-data' };
    
    // 不设置文件ID
    (dragEnhancer as any).handleDataChange(testData);
    
    expect(mockSaveManager.scheduleAutoSave).not.toHaveBeenCalled();
  });

  test('应该在 autoSave 禁用时跳过保存', () => {
    const fileId = 'test-file-1';
    const testData = { test: 'drag-data' };
    
    // 创建禁用自动保存的实例
    const dragEnhancerNoSave = new DragEnhancer(mockMindMapInstance, {
      persistence: {
        autoSave: false,
        saveDelay: 500
      }
    });
    
    dragEnhancerNoSave.setCurrentFileId(fileId);
    (dragEnhancerNoSave as any).handleDataChange(testData);
    
    expect(mockSaveManager.scheduleAutoSave).not.toHaveBeenCalled();
    
    dragEnhancerNoSave.destroy();
  });

  test('应该在销毁时清理文件状态', () => {
    const fileId = 'test-file-1';
    
    dragEnhancer.setCurrentFileId(fileId);
    dragEnhancer.destroy();
    
    expect(mockSaveManager.clearFileState).toHaveBeenCalledWith(fileId);
  });

  test('应该在销毁时移除事件监听器', () => {
    dragEnhancer.destroy();
    
    expect(mockMindMapInstance.off).toHaveBeenCalledWith('node_dragging', expect.any(Function));
    expect(mockMindMapInstance.off).toHaveBeenCalledWith('node_dragend', expect.any(Function));
    expect(mockMindMapInstance.off).toHaveBeenCalledWith('data_change', expect.any(Function));
  });
});

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/EventManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventManager: function() { return /* binding */ EventManager; }\n/* harmony export */ });\n/**\r\n * EventManager - 事件管理器\r\n * 负责SimpleMindMap事件的统一管理和分发\r\n * \r\n * 职责：\r\n * - 监听SimpleMindMap官方事件\r\n * - 事件处理和回调分发\r\n * - 自定义事件逻辑\r\n */ class EventManager {\n    /**\r\n   * 初始化事件监听\r\n   */ initialize() {\n        this.setupNodeEvents();\n        this.setupDataEvents();\n        this.setupRenderEvents();\n        this.setupCanvasEvents();\n        this.isInitialized = true;\n        console.log(\"✅ 事件管理器初始化完成\");\n    }\n    /**\r\n   * 设置节点相关事件\r\n   */ setupNodeEvents() {\n        // 节点点击事件\n        const nodeClickHandler = (node)=>{\n            var _this_config_onNodeClick, _this_config;\n            console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node);\n            (_this_config_onNodeClick = (_this_config = this.config).onNodeClick) === null || _this_config_onNodeClick === void 0 ? void 0 : _this_config_onNodeClick.call(_this_config, node);\n        };\n        // 节点激活事件\n        const nodeActiveHandler = (node, activeNodeList)=>{\n            console.log(\"✨ 节点激活:\", {\n                node,\n                activeNodeList\n            });\n        // 可以在这里添加节点激活的自定义逻辑\n        };\n        // 节点右键菜单事件\n        const nodeContextMenuHandler = (event, node)=>{\n            console.log(\"\\uD83D\\uDDB1️ 节点右键菜单:\", {\n                event,\n                node\n            });\n            // 阻止默认右键菜单\n            event.preventDefault();\n            event.stopPropagation();\n            // 计算菜单位置并显示\n            const position = this.calculateMenuPosition(event);\n            this.showContextMenu({\n                position,\n                node\n            });\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_click\", nodeClickHandler);\n        this.mindMapInstance.on(\"node_active\", nodeActiveHandler);\n        this.mindMapInstance.on(\"node_contextmenu\", nodeContextMenuHandler);\n        // 存储事件处理器用于清理\n        this.eventListeners.set(\"node_click\", nodeClickHandler);\n        this.eventListeners.set(\"node_active\", nodeActiveHandler);\n        this.eventListeners.set(\"node_contextmenu\", nodeContextMenuHandler);\n    }\n    /**\r\n   * 设置数据相关事件\r\n   */ setupDataEvents() {\n        // 数据变更事件\n        const dataChangeHandler = (newData)=>{\n            var _this_config_onDataChange, _this_config;\n            console.log(\"\\uD83D\\uDCCA 数据变更:\", newData);\n            (_this_config_onDataChange = (_this_config = this.config).onDataChange) === null || _this_config_onDataChange === void 0 ? void 0 : _this_config_onDataChange.call(_this_config, newData);\n        };\n        // 数据设置前事件\n        const beforeSetDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 准备设置数据:\", data);\n        };\n        // 数据设置后事件\n        const setDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 数据设置完成:\", data);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"data_change\", dataChangeHandler);\n        this.mindMapInstance.on(\"before_set_data\", beforeSetDataHandler);\n        this.mindMapInstance.on(\"set_data\", setDataHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"data_change\", dataChangeHandler);\n        this.eventListeners.set(\"before_set_data\", beforeSetDataHandler);\n        this.eventListeners.set(\"set_data\", setDataHandler);\n    }\n    /**\r\n   * 设置渲染相关事件\r\n   */ setupRenderEvents() {\n        // 节点树渲染结束事件\n        const nodeTreeRenderEndHandler = ()=>{\n            var _this_config_onRenderComplete, _this_config;\n            console.log(\"\\uD83C\\uDFA8 节点树渲染完成\");\n            // 完全不在EventManager中处理视图适应，交给组件级别控制\n            // 这样可以确保用户的视图位置永远不会被意外重置\n            console.log(\"✅ 渲染完成，保持当前视图位置\");\n            (_this_config_onRenderComplete = (_this_config = this.config).onRenderComplete) === null || _this_config_onRenderComplete === void 0 ? void 0 : _this_config_onRenderComplete.call(_this_config);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n    }\n    /**\r\n   * 设置画布相关事件\r\n   */ setupCanvasEvents() {\n        // 画布点击事件\n        const drawClickHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布点击\");\n            // 隐藏右键菜单\n            if (this.contextMenuVisible) {\n                this.hideContextMenu();\n            }\n        };\n        // 画布拖拽事件\n        const drawDragHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布拖拽\");\n        };\n        // 缩放事件\n        const scaleHandler = (scale)=>{\n            console.log(\"\\uD83D\\uDD0D 缩放变化:\", scale);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"draw_click\", drawClickHandler);\n        this.mindMapInstance.on(\"view_data_change\", drawDragHandler);\n        this.mindMapInstance.on(\"scale\", scaleHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"draw_click\", drawClickHandler);\n        this.eventListeners.set(\"view_data_change\", drawDragHandler);\n        this.eventListeners.set(\"scale\", scaleHandler);\n    }\n    /**\r\n   * 手动触发事件\r\n   */ emit(event) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.emit(event, ...args);\n        } catch (error) {\n            console.error(\"❌ 触发事件失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 添加自定义事件监听器\r\n   */ addEventListener(event, handler) {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.on(event, handler);\n            this.eventListeners.set(event, handler);\n            console.log(\"✅ 添加事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 添加事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 移除事件监听器\r\n   */ removeEventListener(event) {\n        const handler = this.eventListeners.get(event);\n        if (!handler || !this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.off(event, handler);\n            this.eventListeners.delete(event);\n            console.log(\"✅ 移除事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 获取当前激活的节点列表\r\n   */ getActiveNodes() {\n        var _this_mindMapInstance;\n        if (!((_this_mindMapInstance = this.mindMapInstance) === null || _this_mindMapInstance === void 0 ? void 0 : _this_mindMapInstance.renderer)) return [];\n        try {\n            return this.mindMapInstance.renderer.activeNodeList || [];\n        } catch (error) {\n            console.error(\"❌ 获取激活节点失败:\", error);\n            return [];\n        }\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 计算菜单位置，确保不超出视口\r\n   */ calculateMenuPosition(event) {\n        const viewportWidth = window.innerWidth;\n        const viewportHeight = window.innerHeight;\n        let x = event.clientX;\n        let y = event.clientY;\n        // 预估菜单尺寸（实际尺寸会在组件中调整）\n        const estimatedMenuWidth = 200;\n        const estimatedMenuHeight = 300;\n        // 水平方向调整\n        if (x + estimatedMenuWidth > viewportWidth) {\n            x = viewportWidth - estimatedMenuWidth - 10;\n        }\n        if (x < 10) {\n            x = 10;\n        }\n        // 垂直方向调整\n        if (y + estimatedMenuHeight > viewportHeight) {\n            y = viewportHeight - estimatedMenuHeight - 10;\n        }\n        if (y < 10) {\n            y = 10;\n        }\n        return {\n            x,\n            y\n        };\n    }\n    /**\r\n   * 显示右键菜单\r\n   */ showContextMenu(param) {\n        let { position, node } = param;\n        var // 触发菜单显示回调\n        _this_config_onContextMenuShow, _this_config;\n        this.contextMenuPosition = position;\n        this.contextMenuNode = node;\n        this.contextMenuVisible = true;\n        (_this_config_onContextMenuShow = (_this_config = this.config).onContextMenuShow) === null || _this_config_onContextMenuShow === void 0 ? void 0 : _this_config_onContextMenuShow.call(_this_config, position, node);\n        console.log(\"✅ 显示右键菜单:\", {\n            position,\n            node\n        });\n    }\n    /**\r\n   * 隐藏右键菜单\r\n   */ hideContextMenu() {\n        var // 触发菜单隐藏回调\n        _this_config_onContextMenuHide, _this_config;\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        (_this_config_onContextMenuHide = (_this_config = this.config).onContextMenuHide) === null || _this_config_onContextMenuHide === void 0 ? void 0 : _this_config_onContextMenuHide.call(_this_config);\n        console.log(\"✅ 隐藏右键菜单\");\n    }\n    /**\r\n   * 获取右键菜单状态\r\n   */ getContextMenuState() {\n        return {\n            visible: this.contextMenuVisible,\n            position: this.contextMenuPosition,\n            node: this.contextMenuNode\n        };\n    }\n    /**\r\n   * 销毁事件管理器\r\n   */ destroy() {\n        // 移除所有事件监听器\n        for (const [event, handler] of this.eventListeners){\n            try {\n                this.mindMapInstance.off(event, handler);\n            } catch (error) {\n                console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n            }\n        }\n        // 清理右键菜单状态\n        this.hideContextMenu();\n        this.eventListeners.clear();\n        this.isInitialized = false;\n        // 不重置isFirstRender，保持首次渲染状态\n        console.log(\"✅ 事件管理器销毁完成\");\n    }\n    constructor(mindMapInstance, config){\n        this.eventListeners = new Map();\n        this.isInitialized = false;\n        this.isFirstRender = true;\n        // 右键菜单状态管理\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        this.mindMapInstance = mindMapInstance;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 SaveManager 集成 - 替换原有的定时器管理\n    const saveManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(_services_SaveManager__WEBPACK_IMPORTED_MODULE_9__.SaveManager.getInstance());\n    const [saveState, setSaveState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 SaveManager 状态变更回调\n    const handleSaveStateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId, state, error)=>{\n        // 只处理当前文件的状态变更\n        if ((file === null || file === void 0 ? void 0 : file.id) === fileId) {\n            setSaveState(state);\n            if (error) {\n                console.error(\"\\uD83D\\uDCBE 保存状态错误:\", error);\n            }\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 初始化 SaveManager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        // 设置保存执行回调\n        saveManager.setSaveExecuteCallback(async (fileId, data, options)=>{\n            if (onContentChange && (file === null || file === void 0 ? void 0 : file.id) === fileId) {\n                try {\n                    onContentChange(data);\n                    return true;\n                } catch (error) {\n                    console.error(\"❌ 保存执行失败:\", error);\n                    return false;\n                }\n            }\n            return false;\n        });\n        // 添加状态变更监听器\n        saveManager.addStateChangeListener(handleSaveStateChange);\n        return ()=>{\n            // 清理监听器\n            saveManager.removeStateChangeListener(handleSaveStateChange);\n        };\n    }, [\n        onContentChange,\n        file === null || file === void 0 ? void 0 : file.id,\n        handleSaveStateChange\n    ]);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        const previousFileId = saveManager.getCurrentFileId();\n        const currentFileId = (file === null || file === void 0 ? void 0 : file.id) || null;\n        // 🔧 文件切换时使用 SaveManager 处理\n        if (previousFileId !== currentFileId) {\n            saveManager.switchFile(previousFileId, currentFileId || \"\").then(()=>{\n                console.log(\"\\uD83D\\uDD04 文件切换完成:\", previousFileId, \"->\", currentFileId);\n            }).catch((error)=>{\n                console.error(\"❌ 文件切换失败:\", error);\n            });\n        }\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数 - 使用 SaveManager\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(content) {\n        let saveType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"editor\";\n        if (!settings.autoSave || !(file === null || file === void 0 ? void 0 : file.id)) return;\n        const saveManager = saveManagerRef.current;\n        saveManager.scheduleAutoSave(file.id, content, {\n            debounceDelay: settings.autoSaveDelay,\n            saveType,\n            immediate: false\n        });\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更 - 使用 SaveManager\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 基础状态验证\n        if (!(file === null || file === void 0 ? void 0 : file.id) || !isMindMapMode || !data) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 将思维导图数据转换为Markdown格式并保存\n        try {\n            const markdownContent = convertMindMapToMarkdown(data);\n            handleSave(markdownContent, \"mindmap\");\n            console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并调度保存 (文件ID:\", file.id, \")\");\n        } catch (error) {\n            console.error(\"❌ 思维导图数据转换失败:\", error);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearSaveTimer();\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, [\n        clearSaveTimer\n    ]);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 675,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 670,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 669,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 736,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 897,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 911,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 928,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 931,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 954,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 948,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 944,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 973,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 961,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 979,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 978,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 995,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 891,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 890,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1022,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1021,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1025,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1020,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1019,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1031,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1032,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1033,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1034,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1030,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1029,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1043,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1017,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1059,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1057,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1056,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1055,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1067,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1068,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1069,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1071,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1086,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1066,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1065,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1096,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1113,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1114,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1112,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1111,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1053,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1134,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1132,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1136,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1131,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1130,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1123,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1122,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 684,\n        columnNumber: 5\n    }, undefined);\n}, \"eHE3qNMPD7nMZY1MEjETOXwIYMc=\")), \"eHE3qNMPD7nMZY1MEjETOXwIYMc=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});
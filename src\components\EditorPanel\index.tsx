/**
 * 编辑器面板组件
 * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮
 */

'use client'

import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle, useCallback } from 'react'
import Editor from '@monaco-editor/react'
import { EditorFile, EditorSettings } from '@/types'
import { EditorDiffData, DiffRequest } from '@/types/diff'
import { EditorDiffCalculator } from '@/services/editorDiffService'
import DiffViewerContainer from '@/components/DiffViewer/DiffViewerContainer'
import { EditorIntegration } from '@/services/editorIntegration'
import MindMapRenderer from '@/components/MindMapRenderer'
import { parseDocumentFile } from '@/services/enhancedDocumentParser'
import { DocumentParseResult, MindMapNode } from '@/types'
import { getFileTypeFromName, isMindMapFile, isMarkdownFile } from '@/utils/fileTypeUtils'
import { MarkdownConverter } from '@/components/MindMapRenderer/managers/MarkdownConverter'
import { SaveManager } from '@/services/SaveManager'
import { SaveState, SaveStateChangeCallback } from '@/types'
import SaveStatusIndicator from './SaveStatusIndicator'

interface EditorPanelProps {
  file: EditorFile | null
  onContentChange?: (content: string) => void
  onSettingsChange?: (settings: EditorSettings) => void
  onFileRename?: (fileId: string, newName: string) => void
  settings?: EditorSettings
  className?: string
  // 自动关联功能相关
  onAutoAssociationToggle?: (enabled: boolean) => void
  autoAssociationEnabled?: boolean
  // diff功能相关
  artworkId?: string
  onOpenDetailedDiff?: (diffRequest: DiffRequest) => void
  // 布局变化回调
  onLayoutChange?: () => void
}

// 默认编辑器设置 - 优化文字创作体验
const DEFAULT_SETTINGS: EditorSettings = {
  fontSize: 16,
  fontWeight: 400,
  fontFamily: '"Noto Serif SC", "Source Han Serif SC", "思源宋体", Georgia, "Times New Roman", serif',
  theme: 'dark',
  wordWrap: true,
  wordWrapColumn: 56, // 默认56字符换行，平衡可读性和屏幕利用率
  showRulers: false, // 默认不显示标尺，保持界面简洁
  rulers: [56], // 默认标尺位置与换行字符数一致
  showLineNumbers: false, // 文字创作默认隐藏行号
  enablePreview: false,
  tabSize: 2,
  insertSpaces: true,
  autoSave: true,
  autoSaveDelay: 1000
}

const EditorPanel = forwardRef<any, EditorPanelProps>(({
  file,
  onContentChange,
  onSettingsChange,
  onFileRename,
  settings = DEFAULT_SETTINGS,
  className = '',
  onAutoAssociationToggle,
  autoAssociationEnabled: propAutoAssociationEnabled,
  artworkId,
  onOpenDetailedDiff,
  onLayoutChange
}, ref) => {
  
  const [editorContent, setEditorContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [isRenamingFile, setIsRenamingFile] = useState(false)
  const [renamingValue, setRenamingValue] = useState('')
  const [availableFonts, setAvailableFonts] = useState<Array<{name: string, family: string}>>([])
  // 自动关联功能状态
  const [autoAssociationEnabled, setAutoAssociationEnabled] = useState(propAutoAssociationEnabled ?? true)
  // diff功能状态
  const [viewMode, setViewMode] = useState<'normal' | 'diff'>('normal')
  const [diffData, setDiffData] = useState<EditorDiffData | null>(null)
  const [diffLoading, setDiffLoading] = useState(false)
  const [diffError, setDiffError] = useState<string | null>(null)

  // 表格渲染功能状态
  const [tableRenderingEnabled, setTableRenderingEnabled] = useState(true)
  const [tableIntegration, setTableIntegration] = useState<any>(null)
  
  // 思维导图相关状态
  const [mindMapData, setMindMapData] = useState<MindMapNode | null>(null)
  const [mindMapParseResult, setMindMapParseResult] = useState<DocumentParseResult | null>(null)
  const [mindMapLoading, setMindMapLoading] = useState(false)
  const [mindMapError, setMindMapError] = useState<string | null>(null)
  // Markdown 转换器实例
  const [markdownConverter] = useState(() => new MarkdownConverter({
    debug: process.env.NODE_ENV === 'development'
  }))
  // 思维导图模式状态（针对 Markdown 文件）
  const [isMindMapMode, setIsMindMapMode] = useState(false)
  const editorRef = useRef<any>(null)
  const mindMapRendererRef = useRef<any>(null) // 新增：MindMapRenderer 引用
  const fileNameInputRef = useRef<HTMLInputElement>(null)
  const tableIntegrationRef = useRef<any>(null)

  // 🔧 SaveManager 集成 - 替换原有的定时器管理
  const saveManagerRef = useRef<SaveManager>(SaveManager.getInstance())
  const [saveState, setSaveState] = useState<SaveState>(SaveState.idle)
  const [saveError, setSaveError] = useState<string | undefined>()

  // 动态计算文件类型（基于文件名后缀）
  const currentFileType = file ? getFileTypeFromName(file.name) : 'text'

  // 🔧 SaveManager 状态变更回调
  const handleSaveStateChange = useCallback<SaveStateChangeCallback>((fileId, state, error) => {
    // 只处理当前文件的状态变更
    if (file?.id === fileId) {
      setSaveState(state)
      setSaveError(error)
      if (error) {
        console.error('💾 保存状态错误:', error)
      }
    }
  }, [file?.id])

  // 🔧 保存重试处理
  const handleSaveRetry = useCallback(async () => {
    if (!file?.id) return

    const saveManager = saveManagerRef.current
    try {
      const success = await saveManager.retryFileSave(file.id)
      if (success) {
        console.log('✅ 保存重试成功:', file.id)
      } else {
        console.warn('⚠️ 保存重试失败:', file.id)
      }
    } catch (error) {
      console.error('❌ 保存重试异常:', error)
    }
  }, [file?.id])

  // 🔧 初始化 SaveManager
  useEffect(() => {
    const saveManager = saveManagerRef.current

    // 设置保存执行回调
    saveManager.setSaveExecuteCallback(async (fileId, data, options) => {
      console.log('🔧 SaveManager 执行保存回调:', {
        fileId,
        currentFileId: file?.id,
        dataType: typeof data,
        dataLength: typeof data === 'string' ? data.length : 'N/A',
        saveType: options?.saveType,
        dataPreview: typeof data === 'string' ? data.substring(0, 100) : String(data).substring(0, 100)
      })

      if (onContentChange && file?.id === fileId) {
        try {
          // 确保数据是字符串类型
          if (typeof data !== 'string') {
            console.error('❌ 保存数据类型错误，期望字符串，实际:', typeof data, data)
            return false
          }

          if (data.trim() === '') {
            console.warn('⚠️ 保存空文件内容')
            // 允许保存空文件，继续执行
          }

          onContentChange(data)
          return true
        } catch (error) {
          console.error('❌ 保存执行失败:', error)
          return false
        }
      }

      console.log('⚠️ 保存跳过，文件ID不匹配或回调不存在:', {
        hasCallback: !!onContentChange,
        fileIdMatch: file?.id === fileId
      })
      return false
    })

    // 添加状态变更监听器
    saveManager.addStateChangeListener(handleSaveStateChange)

    return () => {
      // 清理监听器
      saveManager.removeStateChangeListener(handleSaveStateChange)
    }
  }, [onContentChange, file?.id, handleSaveStateChange])

  // 当文件变化时更新编辑器内容
  useEffect(() => {
    const saveManager = saveManagerRef.current
    const previousFileId = saveManager.getCurrentFileId()
    const currentFileId = file?.id || null

    // 🔧 文件切换时使用 SaveManager 处理
    if (previousFileId !== currentFileId) {
      saveManager.switchFile(previousFileId, currentFileId || '').then(() => {
        console.log('🔄 文件切换完成:', previousFileId, '->', currentFileId)
      }).catch(error => {
        console.error('❌ 文件切换失败:', error)
      })
    }

    if (file) {
      // 设置编辑器内容
      setEditorContent(file.content || '')

      // 检查文件类型并处理思维导图模式
      const isMarkdown = isMarkdownFile(file.name)
      const isMindMap = isMindMapFile(file.name)

      if (isMindMap) {
        // .mindmap 文件始终使用思维导图模式
        setIsMindMapMode(true)
        parseMindMapContent(file)
      } else if (isMarkdown) {
        // .md 文件检查是否有思维导图模式的历史状态
        const savedMode = localStorage.getItem(`mindMapMode_${file.id}`)
        const shouldUseMindMapMode = savedMode === 'true'
        setIsMindMapMode(shouldUseMindMapMode)

        if (shouldUseMindMapMode) {
          parseMindMapContent(file)
        } else {
          // 清除思维导图状态
          setMindMapData(null)
          setMindMapParseResult(null)
          setMindMapError(null)
        }
      } else {
        // 其他文件类型，清除思维导图状态
        setIsMindMapMode(false)
        setMindMapData(null)
        setMindMapParseResult(null)
        setMindMapError(null)
      }
    } else {
      setEditorContent('')
      setIsMindMapMode(false)
      setMindMapData(null)
      setMindMapParseResult(null)
      setMindMapError(null)
    }
  }, [file?.id, file?.name]) // 只监听文件ID和名称变化，不监听内容变化

  // 将思维导图数据转换为Markdown格式
  const convertMindMapToMarkdown = (data: MindMapNode): string => {
    console.log('🔄 开始转换思维导图数据到Markdown:', data)

    if (!data || !data.data || !data.data.text) {
      console.error('❌ 思维导图数据无效:', data)
      return '# 思维导图\n\n内容为空'
    }

    const convertNode = (node: MindMapNode, level: number = 1): string => {
      if (!node || !node.data || !node.data.text) {
        console.warn('⚠️ 跳过无效节点:', node)
        return ''
      }

      const prefix = '#'.repeat(level)
      let result = `${prefix} ${node.data.text}\n\n`

      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          result += convertNode(child, level + 1)
        }
      }

      return result
    }

    const markdownResult = convertNode(data).trim()
    console.log('✅ 思维导图转换完成，结果长度:', markdownResult.length, '内容预览:', markdownResult.substring(0, 100))

    return markdownResult || '# 思维导图\n\n内容为空'
  }

  // 解析思维导图内容
  const parseMindMapContent = useCallback(async (file: EditorFile) => {
    setMindMapLoading(true)
    setMindMapError(null)
    
    try {
      let result: DocumentParseResult

      // 统一使用 MarkdownConverter，消除双解析器冲突
      if (isMindMapFile(file.name) || (isMarkdownFile(file.name) && isMindMapMode)) {
        const fileType = isMindMapFile(file.name) ? '.mindmap' : '.md'
        console.log(`🔄 使用统一 MarkdownConverter 解析 ${fileType} 文件`, {
          fileName: file.name,
          contentType: typeof file.content,
          contentLength: file.content ? file.content.length : 0,
          contentPreview: file.content ? file.content.substring(0, 100) : 'null/undefined'
        })

        // 检查文件内容是否有效
        if (!file.content || typeof file.content !== 'string' || file.content.trim() === '') {
          console.warn('⚠️ 文件内容为空或无效，使用默认内容')
          // 为空文件提供默认的思维导图内容
          const defaultContent = '# 思维导图\n\n## 主要分支\n\n- 子节点1\n- 子节点2'
          const conversionResult = await markdownConverter.convertFromMarkdown(defaultContent)

          if (conversionResult.success && conversionResult.data) {
            result = {
              success: true,
              data: conversionResult.data as any, // 临时解决类型兼容问题
              error: undefined,
              metadata: {
                fileName: file.name,
                fileSize: defaultContent.length,
                format: fileType,
                parseTime: 0,
                originalContent: defaultContent
              }
            } as DocumentParseResult
            console.log(`✅ ${fileType} 文件使用默认内容解析成功`)
          } else {
            throw new Error(conversionResult.error || `${fileType}文件默认内容转换失败`)
          }
        } else {
          const conversionResult = await markdownConverter.convertFromMarkdown(file.content)

          if (conversionResult.success && conversionResult.data) {
            result = {
              success: true,
              data: conversionResult.data as any, // 临时解决类型兼容问题
              error: undefined,
              metadata: {
                fileName: file.name,
                fileSize: file.content.length,
                format: fileType,
                parseTime: 0,
                originalContent: file.content
              }
            } as DocumentParseResult
            console.log(`✅ ${fileType} 文件解析成功`)
          } else {
            throw new Error(conversionResult.error || `${fileType}文件转换失败`)
          }
        }
      } else {
        throw new Error('不支持的文件类型或模式')
      }
      
      if (result.success && result.data) {
        // 批量更新状态，减少重渲染
        setMindMapData(result.data)
        setMindMapParseResult(result)
        console.log('✅ 思维导图解析成功:', result.data)
      } else {
        setMindMapError(result.error || '思维导图解析失败')
        console.error('❌ 思维导图解析失败:', result.error)
      }
    } catch (error) {
      console.error('思维导图解析错误:', error)
      setMindMapError(error instanceof Error ? error.message : '思维导图解析出错')
    } finally {
      setMindMapLoading(false)
    }
  }, [markdownConverter, isMindMapMode]) // 添加依赖项

  // 切换思维导图模式（仅对 Markdown 文件有效）
  const toggleMindMapMode = useCallback(() => {
    if (!file || !isMarkdownFile(file.name)) return

    const newMode = !isMindMapMode
    setIsMindMapMode(newMode)

    // 保存模式状态到 localStorage
    localStorage.setItem(`mindMapMode_${file.id}`, newMode.toString())

    if (newMode) {
      // 切换到思维导图模式，解析当前内容
      parseMindMapContent(file)
    } else {
      // 切换到编辑器模式，清除思维导图状态
      setMindMapData(null)
      setMindMapParseResult(null)
      setMindMapError(null)
    }

    console.log('🔄 思维导图模式切换:', newMode ? '开启' : '关闭')
  }, [file, isMindMapMode, parseMindMapContent])

  // 处理布局变化的回调函数
  const handleLayoutChange = useCallback(() => {
    console.log('🔄 接收到布局变化通知，更新画布尺寸')
    // 延迟执行，确保DOM布局更新完成
    setTimeout(() => {
      if (mindMapRendererRef.current?.resize) {
        mindMapRendererRef.current.resize()
      }
    }, 100)
  }, [])

  // 🔧 统一的保存处理函数 - 使用 SaveManager
  const handleSave = useCallback((content: string, saveType: 'editor' | 'mindmap' = 'editor') => {
    if (!settings.autoSave || !file?.id) return

    const saveManager = saveManagerRef.current
    saveManager.scheduleAutoSave(file.id, content, {
      debounceDelay: settings.autoSaveDelay,
      saveType,
      immediate: false
    })
  }, [settings.autoSave, settings.autoSaveDelay, file?.id])

  // 处理编辑器内容变化
  const handleEditorChange = (value: string | undefined | any) => {
    let content: string

    // 如果是思维导图数据对象，转换为JSON字符串
    if (typeof value === 'object' && value !== null) {
      content = JSON.stringify(value, null, 2)
    } else {
      content = value || ''
    }

    setEditorContent(content)
    handleSave(content)
  }

  // 处理思维导图数据变更 - 使用 SaveManager
  const handleMindMapDataChange = useCallback((data: any) => {
    console.log('📊 思维导图数据变更:', {
      hasData: !!data,
      dataType: typeof data,
      fileId: file?.id,
      isMindMapMode,
      dataPreview: data ? JSON.stringify(data).substring(0, 200) : 'null'
    })

    // 基础状态验证
    if (!file?.id || !isMindMapMode || !data) {
      console.log('🔒 文件状态已变化或非思维导图模式，跳过保存', {
        fileId: file?.id,
        isMindMapMode,
        hasData: !!data
      })
      return
    }

    // 将思维导图数据转换为Markdown格式并保存
    try {
      const markdownContent = convertMindMapToMarkdown(data)
      console.log('💾 准备保存转换后的内容:', {
        fileId: file.id,
        contentLength: markdownContent.length,
        contentPreview: markdownContent.substring(0, 100)
      })
      handleSave(markdownContent, 'mindmap')
      console.log('✅ 思维导图数据已转换为Markdown并调度保存')
    } catch (error) {
      console.error('❌ 思维导图数据转换失败:', error)
    }
  }, [file?.id, isMindMapMode, convertMindMapToMarkdown, handleSave])

  // 处理编辑器挂载
  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor
    
    // 初始化表格渲染功能（仅对Markdown文件）
    if (currentFileType === 'markdown') {
      try {
        const integration = new EditorIntegration(editor)
        integration.initialize()
        
        // 保存集成实例到状态和编辑器
        setTableIntegration(integration)
        ;(editor as any).__tableIntegration = integration
        
        console.log('✅ 表格渲染功能已集成到编辑器')
      } catch (error) {
        console.error('❌ 表格渲染功能初始化失败:', error)
        setTableIntegration(null)
      }
    }
    
    // 配置文字创作专用主题 - 温暖舒适的阅读体验
    monaco.editor.defineTheme('miyazaki-writing', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        // Markdown 语法高亮优化
        { token: 'keyword.md', foreground: '#F4A460', fontStyle: 'bold' }, // 标题关键字
        { token: 'string.md', foreground: '#E6D7B7' }, // 普通文本 - 温暖的米色
        { token: 'emphasis.md', foreground: '#DEB887', fontStyle: 'italic' }, // 斜体
        { token: 'strong.md', foreground: '#F0E68C', fontStyle: 'bold' }, // 粗体
        { token: 'variable.md', foreground: '#98D8C8' }, // 链接
        { token: 'string.link.md', foreground: '#87CEEB' }, // 链接URL
        { token: 'comment.md', foreground: '#8FBC8F', fontStyle: 'italic' }, // 引用块
        { token: 'number.md', foreground: '#DDA0DD' }, // 列表标记
        { token: 'delimiter.md', foreground: '#D2B48C' }, // 分隔符
        
        // 通用语法
        { token: 'comment', foreground: '#8FBC8F', fontStyle: 'italic' },
        { token: 'keyword', foreground: '#F4A460' },
        { token: 'string', foreground: '#98FB98' }, // 改为更有对比度的浅绿色
        { token: 'string.quoted', foreground: '#98FB98' }, // 双引号字符串
        { token: 'string.quoted.double', foreground: '#98FB98' }, // 双引号字符串
        { token: 'string.quoted.single', foreground: '#98FB98' }, // 单引号字符串
        { token: 'number', foreground: '#DDA0DD' },
        { token: 'regexp', foreground: '#FF6B6B' },
        { token: 'type', foreground: '#98D8C8' },
        { token: 'class', foreground: '#98D8C8' },
        { token: 'function', foreground: '#F0E68C' },
        { token: 'variable', foreground: '#E6D7B7' },
        { token: 'constant', foreground: '#87CEEB' },
        { token: 'property', foreground: '#E6D7B7' },
        { token: 'operator', foreground: '#FFD700' }, // 改为金色，更醒目
        { token: 'delimiter', foreground: '#FFD700' }, // 分隔符也用金色
        { token: 'delimiter.bracket', foreground: '#FFD700' }, // 方括号
        { token: 'delimiter.parenthesis', foreground: '#FFD700' }, // 圆括号
        { token: 'delimiter.square', foreground: '#FFD700' }, // 方括号
        { token: 'delimiter.curly', foreground: '#FFD700' }, // 大括号
        { token: 'punctuation', foreground: '#FFD700' }, // 标点符号
        { token: 'punctuation.bracket', foreground: '#FFD700' }, // 括号标点
        { token: 'punctuation.definition', foreground: '#FFD700' } // 定义标点
      ],
      colors: {
        // 背景色 - 深色但温暖
        'editor.background': '#1A1A1A',
        'editor.foreground': '#E6D7B7', // 主文字颜色 - 温暖的米色
        
        // 行号
        'editorLineNumber.foreground': '#6B5B73',
        'editorLineNumber.activeForeground': '#8B7B8B',
        
        // 选择和高亮
        'editor.selectionBackground': '#4A4A2A', // 选择背景 - 温暖的深色
        'editor.selectionHighlightBackground': '#3A3A1A26',
        'editor.wordHighlightBackground': '#4A4A2A88',
        'editor.wordHighlightStrongBackground': '#5A5A3A88',
        
        // 光标和当前行
        'editorCursor.foreground': '#F4A460', // 光标颜色 - 温暖的橙色
        'editor.lineHighlightBackground': '#2A2A1A', // 当前行背景
        
        // 滚动条
        'scrollbarSlider.background': '#4A4A2A66',
        'scrollbarSlider.hoverBackground': '#5A5A3A88',
        'scrollbarSlider.activeBackground': '#6A6A4A',
        
        // 边框和分割线
        'editorWidget.border': '#F4A46033',
        'editorHoverWidget.background': '#2A2A1A',
        'editorHoverWidget.border': '#F4A46066',
        
        // 建议框
        'editorSuggestWidget.background': '#2A2A1A',
        'editorSuggestWidget.border': '#F4A46033',
        'editorSuggestWidget.foreground': '#E6D7B7',
        'editorSuggestWidget.selectedBackground': '#4A4A2A',
        
        // 查找框
        'editorFindMatch.background': '#5A5A3A88',
        'editorFindMatchHighlight.background': '#4A4A2A66',
        'editorFindRangeHighlight.background': '#3A3A1A33'
      }
    })
    
    // 设置主题
    monaco.editor.setTheme('miyazaki-writing')
  }

  // 处理设置变化
  const handleSettingsChange = (newSettings: Partial<EditorSettings>) => {
    const updatedSettings = { ...settings, ...newSettings }
    if (onSettingsChange) {
      onSettingsChange(updatedSettings)
    }
  }

  // 🔧 直接处理字符数变更，不使用防抖
  const handleWordWrapColumnChange = (value: number) => {
    handleSettingsChange({ wordWrapColumn: value })
  }

  // 处理换行模式变更
  const handleWrapModeChange = (mode: string) => {
    if (mode === 'wordWrapColumn') {
      // 切换到按字符数换行时，确保有默认字符数
      const wordWrapColumn = settings.wordWrapColumn || 56
      handleSettingsChange({ 
        wordWrap: 'wordWrapColumn',
        wordWrapColumn: wordWrapColumn
      })
    } else {
      handleSettingsChange({ wordWrap: mode === 'on' })
    }
  }

  // 处理标尺显示切换
  const handleRulersToggle = () => {
    const newShowRulers = !settings.showRulers
    // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置
    const rulers = newShowRulers ? (settings.rulers || [settings.wordWrapColumn || 56]) : settings.rulers
    handleSettingsChange({ 
      showRulers: newShowRulers,
      rulers: rulers
    })
  }

  // 加载可用字体
  useEffect(() => {
    const loadAvailableFonts = async () => {
      try {
        const { FontService } = await import('@/services/fontService')
        const fontService = FontService.getInstance()
        const result = await fontService.getAllFonts()
        
        if (result.success && result.data) {
          const fonts = result.data.map(font => ({
            name: font.name,
            family: font.family
          }))
          
          // 添加系统默认字体
          const systemFonts = [
            { name: '思源宋体', family: '"Noto Serif SC", "Source Han Serif SC", "思源宋体", Georgia, serif' },
            { name: '思源黑体', family: '"Noto Sans SC", "Source Han Sans SC", "思源黑体", Arial, sans-serif' },
            { name: 'Monaco', family: 'Monaco, Consolas, "Courier New", monospace' },
            { name: 'Georgia', family: 'Georgia, "Times New Roman", serif' },
            { name: 'Arial', family: 'Arial, sans-serif' }
          ]
          
          setAvailableFonts([...systemFonts, ...fonts])
        }
      } catch (error) {
        console.error('加载字体列表失败:', error)
        // 使用默认字体列表
        setAvailableFonts([
          { name: '思源宋体', family: '"Noto Serif SC", "Source Han Serif SC", "思源宋体", Georgia, serif' },
          { name: '思源黑体', family: '"Noto Sans SC", "Source Han Sans SC", "思源黑体", Arial, sans-serif' },
          { name: 'Monaco', family: 'Monaco, Consolas, "Courier New", monospace' }
        ])
      }
    }
    
    loadAvailableFonts()
  }, [])

  // 自动关联功能状态管理
  useEffect(() => {
    // 从localStorage加载自动关联状态
    const saved = localStorage.getItem('autoAssociationEnabled')
    if (saved !== null) {
      const savedValue = JSON.parse(saved)
      setAutoAssociationEnabled(savedValue)
    }
  }, [])

  // 同步外部传入的自动关联状态
  useEffect(() => {
    if (propAutoAssociationEnabled !== undefined) {
      setAutoAssociationEnabled(propAutoAssociationEnabled)
    }
  }, [propAutoAssociationEnabled])

  // 处理自动关联开关切换
  const toggleAutoAssociation = () => {
    const newValue = !autoAssociationEnabled
    setAutoAssociationEnabled(newValue)
    localStorage.setItem('autoAssociationEnabled', JSON.stringify(newValue))
    
    if (onAutoAssociationToggle) {
      onAutoAssociationToggle(newValue)
    }
    
    console.log('🎛️ 自动关联功能', newValue ? '开启' : '关闭')
  }

  // 处理打开详细差异对比
  const handleOpenDetailedDiff = async (diffRequest: DiffRequest) => {
    if (!artworkId || !file) {
      console.error('❌ 缺少必要参数：artworkId 或 file')
      return
    }

    try {
      setDiffLoading(true)
      setDiffError(null)
      
      console.log('🔄 开始计算diff数据:', {
        artworkId,
        filePath: diffRequest.filePath,
        operation: diffRequest.operation,
        contentLength: diffRequest.content.length
      })
      
      // 使用EditorDiffCalculator计算diff数据
      const diffCalculator = EditorDiffCalculator.getInstance()
      const result = await diffCalculator.calculateEditorDiff(
        artworkId,
        diffRequest.filePath,
        diffRequest.content,
        diffRequest.operation
      )
      
      if (result.success && result.data) {
        console.log('✅ 成功生成diff数据:', {
          additions: result.data.additions,
          deletions: result.data.deletions,
          modifications: result.data.modifications,
          hunksCount: result.data.hunks.length
        })
        
        setDiffData(result.data)
        setViewMode('diff')
      } else {
        throw new Error(result.error || '生成diff数据失败')
      }
    } catch (error) {
      console.error('❌ 生成diff数据失败:', error)
      setDiffError(error instanceof Error ? error.message : '未知错误')
    } finally {
      setDiffLoading(false)
    }
  }

  // 使用useImperativeHandle暴露方法给父组件
  useImperativeHandle(ref, () => ({
    handleOpenDetailedDiff,
    handleLayoutChange // 新增：暴露布局变化处理方法
  }), [handleOpenDetailedDiff, handleLayoutChange])

  // 处理关闭diff视图
  const handleCloseDiff = () => {
    setViewMode('normal')
    setDiffData(null)
    setDiffError(null)
  }

  // 处理应用diff更改
  const handleApplyDiffChanges = (content: string) => {
    if (onContentChange) {
      onContentChange(content)
      setEditorContent(content)
    }
    handleCloseDiff()
    console.log('✅ 已应用diff更改')
  }

  // 处理diff错误
  const handleDiffError = (error: string) => {
    setDiffError(error)
    console.error('❌ Diff视图错误:', error)
  }

  // 🔧 清理表格集成和资源
  useEffect(() => {
    return () => {
      // 清理表格渲染集成
      const editor = editorRef.current
      const integration = editor?.__tableIntegration
      if (integration) {
        integration.dispose()
        delete editor.__tableIntegration
      }

      // 清理 Markdown 转换器
      if (markdownConverter) {
        markdownConverter.destroy()
      }
    }
  }, [])

  // 如果没有文件，显示欢迎界面
  if (!file) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <div className="text-6xl mb-4">📝</div>
          <h3 className="text-xl font-handwritten text-amber-200 mb-2">
            选择一个文件开始编辑
          </h3>
          <p className="text-gray-400 text-sm font-handwritten">
            从左侧文件树中选择文件，或创建新文件
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 编辑器工具栏 */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20">
        {/* 文件信息 */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-amber-500"></div>
            {isRenamingFile ? (
              <input
                ref={fileNameInputRef}
                type="text"
                value={renamingValue}
                onChange={(e) => setRenamingValue(e.target.value)}
                onBlur={() => {
                  if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {
                    onFileRename(file.id, renamingValue.trim())
                  }
                  setIsRenamingFile(false)
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {
                      onFileRename(file.id, renamingValue.trim())
                    }
                    setIsRenamingFile(false)
                  } else if (e.key === 'Escape') {
                    setRenamingValue(file.name)
                    setIsRenamingFile(false)
                  }
                }}
                className="text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                autoFocus
              />
            ) : (
              <span 
                className="text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10"
                onClick={() => {
                  setRenamingValue(file.name)
                  setIsRenamingFile(true)
                }}
                title="点击编辑文件名"
              >
                {file.name}
              </span>
            )}
          </div>
          {file.isDirty && (
            <div className="w-2 h-2 rounded-full bg-orange-500" title="未保存的更改"></div>
          )}
        </div>

        {/* 工具按钮 */}
        <div className="flex items-center gap-2">
          {/* 功能按钮组 */}
          <div className="flex items-center gap-2">
            {/* 保存状态指示器 */}
            <SaveStatusIndicator
              saveState={saveState}
              error={saveError}
              onRetry={handleSaveRetry}
            />

            {/* 文件类型指示器 */}
            <span className="text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded">
              {currentFileType}
            </span>
            
            {/* 思维导图模式切换按钮 - 对 Markdown 文件显示 */}
            {isMarkdownFile(file.name) && (
              <button
                onClick={toggleMindMapMode}
                className={`px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 ${
                  isMindMapMode
                    ? 'text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20'
                    : 'text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20'
                }`}
                title={`思维导图模式 (${isMindMapMode ? '已开启' : '已关闭'})`}
              >
                <div className="flex items-center gap-2">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                    {isMindMapMode ? (
                      // 思维导图图标（开启状态）
                      <path d="M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z"/>
                    ) : (
                      // 编辑器图标（关闭状态）
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    )}
                  </svg>
                  <span>
                    {isMindMapMode ? '思维导图' : '编辑器'}
                  </span>
                </div>
              </button>
            )}
            
            {/* 思维导图文件类型指示器 */}
            {isMindMapFile(file.name) && (
              <div className="flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-green-400">
                  <path d="M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z"/>
                </svg>
                <span className="text-xs text-green-200 font-medium">
                  自动解析
                </span>
              </div>
            )}
            
            {/* 表格渲染按钮 - 仅对非思维导图模式的 Markdown 文件启用 */}
            <button
              onClick={() => {
                if (currentFileType !== 'markdown' || isMindMapMode) {
                  if (isMindMapMode) {
                    alert('表格渲染功能在思维导图模式下不可用！\n请切换到编辑器模式来使用此功能。')
                  } else {
                    alert('表格渲染功能仅支持Markdown文件！\n请打开.md文件来使用此功能。')
                  }
                  return
                }
                
                console.log('🔄 表格渲染按钮被点击')
                console.log('📊 当前表格集成状态:', tableIntegration)
                console.log('📊 当前渲染状态:', tableRenderingEnabled)
                
                if (tableIntegration) {
                  try {
                    tableIntegration.toggleTableRendering()
                    const newState = tableIntegration.isTableRenderingEnabled()
                    setTableRenderingEnabled(newState)
                    console.log('✅ 表格渲染状态已切换为:', newState)
                  } catch (error) {
                    console.error('❌ 切换表格渲染失败:', error)
                  }
                } else {
                  console.warn('⚠️ 表格集成未初始化，尝试重新初始化...')
                  const editor = editorRef.current
                  if (editor) {
                    try {
                      const integration = new EditorIntegration(editor)
                      integration.initialize()
                      setTableIntegration(integration)
                      ;(editor as any).__tableIntegration = integration
                      console.log('✅ 表格集成重新初始化成功')
                    } catch (error) {
                      console.error('❌ 表格集成重新初始化失败:', error)
                    }
                  }
                }
              }}
              disabled={currentFileType !== 'markdown' || isMindMapMode}
              className={`px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 ${
                currentFileType !== 'markdown' || isMindMapMode
                  ? 'text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed'
                  : tableRenderingEnabled
                  ? 'text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20'
                  : 'text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20'
              }`}
              title={
                currentFileType !== 'markdown' 
                  ? '表格渲染功能仅支持Markdown文件' 
                  : isMindMapMode
                  ? '表格渲染功能在思维导图模式下不可用'
                  : `表格渲染功能 (${tableRenderingEnabled ? '已开启' : '已关闭'})`
              }
            >
              <div className="flex items-center gap-2">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z"/>
                </svg>
                <span>
                  {currentFileType !== 'markdown' 
                    ? '表格功能' 
                    : tableRenderingEnabled ? '表格 ON' : '表格 OFF'
                  }
                </span>
              </div>
            </button>
          </div>
          
          {/* 自动关联开关按钮 */}
          <button
            onClick={toggleAutoAssociation}
            className={`p-2 rounded-md transition-all duration-200 ${
              autoAssociationEnabled
                ? 'text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20'
                : 'text-gray-400 hover:text-amber-400 hover:bg-amber-500/10'
            }`}
            title={`自动关联当前编辑文件到AI助手 (${autoAssociationEnabled ? '已开启' : '已关闭'})`}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              {autoAssociationEnabled ? (
                // 开启状态：循环箭头图标
                <path d="M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z"/>
              ) : (
                // 关闭状态：断开的链接图标
                <path d="M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z"/>
              )}
            </svg>
          </button>
          
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200"
            title="编辑器设置"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
            </svg>
          </button>
        </div>
      </div>

      {/* 设置面板 */}
      {showSettings && (
        <div className="px-4 py-3 bg-gray-900/50 border-b border-amber-500/20">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {/* 字体选择 */}
            <div>
              <label className="block text-xs font-handwritten text-amber-200 mb-1">
                字体选择
              </label>
              <select
                value={settings.fontFamily}
                onChange={(e) => handleSettingsChange({ fontFamily: e.target.value })}
                className="w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              >
                {availableFonts.map((font, index) => (
                  <option key={index} value={font.family}>
                    {font.name}
                  </option>
                ))}
              </select>
            </div>

            {/* 字体大小 */}
            <div>
              <label className="block text-xs font-handwritten text-amber-200 mb-1">
                字体大小
              </label>
              <input
                type="range"
                min="10"
                max="24"
                value={settings.fontSize}
                onChange={(e) => handleSettingsChange({ fontSize: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="text-xs text-gray-400 mt-1">{settings.fontSize}px</div>
            </div>

            {/* 字体粗细 */}
            <div>
              <label className="block text-xs font-handwritten text-amber-200 mb-1">
                字体粗细
              </label>
              <input
                type="range"
                min="100"
                max="900"
                step="100"
                value={settings.fontWeight}
                onChange={(e) => handleSettingsChange({ fontWeight: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="text-xs text-gray-400 mt-1">{settings.fontWeight}</div>
            </div>

            {/* 换行模式 */}
            <div>
              <label className="block text-xs font-handwritten text-amber-200 mb-1">
                换行模式
              </label>
              <select
                value={settings.wordWrap === 'wordWrapColumn' ? 'wordWrapColumn' : settings.wordWrap ? 'on' : 'off'}
                onChange={(e) => handleWrapModeChange(e.target.value)}
                className="w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              >
                <option value="off">不换行</option>
                <option value="on">自动换行</option>
                <option value="wordWrapColumn">按字符数换行</option>
              </select>
            </div>

            {/* 换行字符数 - 仅在按字符数换行时显示 */}
            {settings.wordWrap === 'wordWrapColumn' && (
              <div>
                <label className="block text-xs font-handwritten text-amber-200 mb-1">
                  换行字符数
                </label>
                <input
                  type="range"
                  min="40"
                  max="120"
                  value={settings.wordWrapColumn || 56}
                  onChange={(e) => handleWordWrapColumnChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <div className="text-xs text-gray-400 mt-1">{settings.wordWrapColumn || 56}字符</div>
              </div>
            )}

            {/* 标尺显示 */}
            <div>
              <label className="block text-xs font-handwritten text-amber-200 mb-1">
                标尺线
              </label>
              <button
                onClick={handleRulersToggle}
                className={`w-full px-3 py-1 text-xs rounded-md transition-all duration-200 ${
                  settings.showRulers
                    ? 'bg-amber-500/20 text-amber-200 border border-amber-500/50'
                    : 'bg-gray-700 text-gray-400 border border-gray-600'
                }`}
              >
                {settings.showRulers ? '显示' : '隐藏'}
              </button>
            </div>

            {/* 行号显示 */}
            <div>
              <label className="block text-xs font-handwritten text-amber-200 mb-1">
                行号显示
              </label>
              <button
                onClick={() => handleSettingsChange({ showLineNumbers: !settings.showLineNumbers })}
                className={`w-full px-3 py-1 text-xs rounded-md transition-all duration-200 ${
                  settings.showLineNumbers
                    ? 'bg-amber-500/20 text-amber-200 border border-amber-500/50'
                    : 'bg-gray-700 text-gray-400 border border-gray-600'
                }`}
              >
                {settings.showLineNumbers ? '显示' : '隐藏'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 主内容区域 - 根据viewMode和文件类型条件渲染 */}
      {viewMode === 'diff' ? (
        /* Diff视图模式 */
        <div className="flex-1 relative">
          {diffLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center gap-4 text-purple-400">
                <div className="relative">
                  <div className="w-8 h-8 border-3 border-purple-400/30 rounded-full"></div>
                  <div className="absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
                </div>
                <span className="text-sm font-handwritten">正在生成差异对比...</span>
              </div>
            </div>
          ) : diffError ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-6xl mb-4">⚠️</div>
                <h3 className="text-xl font-handwritten text-red-400 mb-2">差异对比失败</h3>
                <p className="text-gray-400 text-sm mb-4">{diffError}</p>
                <button
                  onClick={handleCloseDiff}
                  className="px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200"
                >
                  返回编辑器
                </button>
              </div>
            </div>
          ) : diffData ? (
            <DiffViewerContainer
              diffData={diffData}
              onClose={handleCloseDiff}
              onApplyChanges={handleApplyDiffChanges}
              onError={handleDiffError}
            />
          ) : null}
        </div>
      ) : currentFileType === 'mindmap' || (isMarkdownFile(file.name) && isMindMapMode) ? (
        /* 思维导图渲染模式 */
        <div className="flex-1 relative">
          {mindMapLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center gap-4 text-green-400">
                <div className="relative">
                  <div className="w-8 h-8 border-3 border-green-400/30 rounded-full"></div>
                  <div className="absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin"></div>
                </div>
                <span className="text-sm font-handwritten">正在解析思维导图...</span>
              </div>
            </div>
          ) : mindMapError ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-6xl mb-4">🧠</div>
                <h3 className="text-xl font-handwritten text-red-400 mb-2">思维导图解析失败</h3>
                <p className="text-gray-400 text-sm mb-4">{mindMapError}</p>
                <div className="flex gap-2 justify-center mb-4">
                  <button
                    onClick={() => parseMindMapContent(file!)}
                    className="px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten"
                  >
                    重新解析
                  </button>
                  {isMarkdownFile(file!.name) && (
                    <button
                      onClick={toggleMindMapMode}
                      className="px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten"
                    >
                      切换到编辑器
                    </button>
                  )}
                </div>
                <p className="text-gray-500 text-xs">
                  {isMarkdownFile(file!.name) 
                    ? '可以切换到编辑器模式查看原始 Markdown 内容' 
                    : '将显示原始文本内容'
                  }
                </p>
              </div>
            </div>
          ) : mindMapData ? (
            /* 使用新的统一SimpleMindMap实现 */
            <MindMapRenderer
              ref={mindMapRendererRef}
              data={mindMapData}
              fileId={file?.id}
              width="100%"
              height="100%"
              readonly={false}
              theme="dark"
              loading={false}
              onRenderComplete={() => console.log('✅ 思维导图渲染完成')}
              onNodeClick={(node) => console.log('🖱️ 节点点击:', node)}
              onDataChange={handleMindMapDataChange}
              onSelectionComplete={(nodes) => console.log('📦 框选完成:', nodes)}
              className="mind-map-editor"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-6xl mb-4">🧠</div>
                <h3 className="text-xl font-handwritten text-amber-200 mb-2">思维导图文件</h3>
                <p className="text-gray-400 text-sm">正在加载思维导图内容...</p>
              </div>
            </div>
          )}
        </div>
      ) : (
        /* 普通文本编辑器模式 */
        <div className="flex-1 relative">
          <Editor
            height="100%"
            language={currentFileType === 'markdown' ? 'markdown' : 'plaintext'}
            value={editorContent}
            onChange={handleEditorChange}
            onMount={handleEditorDidMount}
            loading={
              <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center gap-4 text-amber-400">
                  <div className="relative">
                    <div className="w-8 h-8 border-3 border-amber-400/30 rounded-full"></div>
                    <div className="absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <span className="text-sm font-handwritten">正在加载编辑器...</span>
                </div>
              </div>
            }
            options={{
              fontSize: settings.fontSize,
              fontWeight: settings.fontWeight.toString(),
              fontFamily: settings.fontFamily,
              wordWrap: settings.wordWrap === 'wordWrapColumn' ? 'wordWrapColumn' : settings.wordWrap ? 'on' : 'off',
              wordWrapColumn: settings.wordWrapColumn || 56,
              rulers: settings.showRulers ? settings.rulers : [],
              lineNumbers: settings.showLineNumbers ? 'on' : 'off',
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              automaticLayout: true,
              tabSize: settings.tabSize,
              insertSpaces: settings.insertSpaces,
              renderWhitespace: 'selection',
              cursorBlinking: 'smooth',
              cursorSmoothCaretAnimation: 'on',
              smoothScrolling: true,
              mouseWheelZoom: true,
              contextmenu: true,
              selectOnLineNumbers: true,
              roundedSelection: false,
              readOnly: false,
              cursorStyle: 'line',
              glyphMargin: false,
              folding: true,
              showFoldingControls: 'mouseover',
              matchBrackets: 'always',
              renderLineHighlight: 'line',
              scrollbar: {
                vertical: 'auto',
                horizontal: 'auto',
                useShadows: false,
                verticalHasArrows: false,
                horizontalHasArrows: false,
                verticalScrollbarSize: 10,
                horizontalScrollbarSize: 10
              }
            }}
          />
        </div>
      )}
    </div>
  )
})

EditorPanel.displayName = 'EditorPanel'

export default EditorPanel
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/DragEnhancer */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _dragEnhancerRef_current, _selectionManagerRef_current, _clipboardManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.destroy();\n        dragEnhancerRef.current = null;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_clipboardManagerRef_current = clipboardManagerRef.current) === null || _clipboardManagerRef_current === void 0 ? void 0 : _clipboardManagerRef_current.destroy();\n        clipboardManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 启用拖拽增强器，使用 SaveManager 统一保存机制\n            if (!readonly && enableDragEnhancement) {\n                var _dragConfig_persistence, _dragConfig_persistence1;\n                var _dragConfig_persistence_autoSave, _dragConfig_persistence_saveDelay;\n                const enhancedDragConfig = {\n                    ...dragConfig,\n                    persistence: {\n                        autoSave: (_dragConfig_persistence_autoSave = (_dragConfig_persistence = dragConfig.persistence) === null || _dragConfig_persistence === void 0 ? void 0 : _dragConfig_persistence.autoSave) !== null && _dragConfig_persistence_autoSave !== void 0 ? _dragConfig_persistence_autoSave : true,\n                        saveDelay: (_dragConfig_persistence_saveDelay = (_dragConfig_persistence1 = dragConfig.persistence) === null || _dragConfig_persistence1 === void 0 ? void 0 : _dragConfig_persistence1.saveDelay) !== null && _dragConfig_persistence_saveDelay !== void 0 ? _dragConfig_persistence_saveDelay : 1000\n                    }\n                };\n                dragEnhancerRef.current = new _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__.DragEnhancer(mindMapInstance, enhancedDragConfig);\n                dragEnhancerRef.current.initialize();\n                console.log(\"\\uD83C\\uDFAF DragEnhancer 已启用并集成 SaveManager\");\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            }\n        }), [\n        resizeCanvas,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 442,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 438,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 459,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 455,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 495,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 518,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 478,\n        columnNumber: 5\n    }, undefined);\n}, \"n9iMgar1C7HJsnxniI5k3ycy3s4=\")), \"n9iMgar1C7HJsnxniI5k3ycy3s4=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});
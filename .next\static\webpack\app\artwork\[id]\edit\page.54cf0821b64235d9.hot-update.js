"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _SaveStatusIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SaveStatusIndicator */ \"(app-pages-browser)/./src/components/EditorPanel/SaveStatusIndicator.tsx\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 SaveManager 集成 - 替换原有的定时器管理\n    const saveManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(_services_SaveManager__WEBPACK_IMPORTED_MODULE_9__.SaveManager.getInstance());\n    const [saveState, setSaveState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 SaveManager 状态变更回调\n    const handleSaveStateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId, state, error)=>{\n        // 只处理当前文件的状态变更\n        if ((file === null || file === void 0 ? void 0 : file.id) === fileId) {\n            setSaveState(state);\n            setSaveError(error);\n            if (error) {\n                console.error(\"\\uD83D\\uDCBE 保存状态错误:\", error);\n            }\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 保存重试处理\n    const handleSaveRetry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(file === null || file === void 0 ? void 0 : file.id)) return;\n        const saveManager = saveManagerRef.current;\n        try {\n            const success = await saveManager.retryFileSave(file.id);\n            if (success) {\n                console.log(\"✅ 保存重试成功:\", file.id);\n            } else {\n                console.warn(\"⚠️ 保存重试失败:\", file.id);\n            }\n        } catch (error) {\n            console.error(\"❌ 保存重试异常:\", error);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 初始化 SaveManager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        // 设置保存执行回调\n        saveManager.setSaveExecuteCallback(async (fileId, data, options)=>{\n            console.log(\"\\uD83D\\uDD27 SaveManager 执行保存回调:\", {\n                fileId,\n                currentFileId: file === null || file === void 0 ? void 0 : file.id,\n                dataType: typeof data,\n                dataLength: typeof data === \"string\" ? data.length : \"N/A\",\n                saveType: options === null || options === void 0 ? void 0 : options.saveType,\n                dataPreview: typeof data === \"string\" ? data.substring(0, 100) : String(data).substring(0, 100)\n            });\n            if (onContentChange && (file === null || file === void 0 ? void 0 : file.id) === fileId) {\n                try {\n                    // 确保数据是字符串类型\n                    if (typeof data !== \"string\") {\n                        console.error(\"❌ 保存数据类型错误，期望字符串，实际:\", typeof data, data);\n                        return false;\n                    }\n                    if (data.trim() === \"\") {\n                        console.error(\"❌ 保存数据为空字符串\");\n                        return false;\n                    }\n                    onContentChange(data);\n                    return true;\n                } catch (error) {\n                    console.error(\"❌ 保存执行失败:\", error);\n                    return false;\n                }\n            }\n            console.log(\"⚠️ 保存跳过，文件ID不匹配或回调不存在:\", {\n                hasCallback: !!onContentChange,\n                fileIdMatch: (file === null || file === void 0 ? void 0 : file.id) === fileId\n            });\n            return false;\n        });\n        // 添加状态变更监听器\n        saveManager.addStateChangeListener(handleSaveStateChange);\n        return ()=>{\n            // 清理监听器\n            saveManager.removeStateChangeListener(handleSaveStateChange);\n        };\n    }, [\n        onContentChange,\n        file === null || file === void 0 ? void 0 : file.id,\n        handleSaveStateChange\n    ]);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        const previousFileId = saveManager.getCurrentFileId();\n        const currentFileId = (file === null || file === void 0 ? void 0 : file.id) || null;\n        // 🔧 文件切换时使用 SaveManager 处理\n        if (previousFileId !== currentFileId) {\n            saveManager.switchFile(previousFileId, currentFileId || \"\").then(()=>{\n                console.log(\"\\uD83D\\uDD04 文件切换完成:\", previousFileId, \"->\", currentFileId);\n            }).catch((error)=>{\n                console.error(\"❌ 文件切换失败:\", error);\n            });\n        }\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        console.log(\"\\uD83D\\uDD04 开始转换思维导图数据到Markdown:\", data);\n        if (!data || !data.data || !data.data.text) {\n            console.error(\"❌ 思维导图数据无效:\", data);\n            return \"# 思维导图\\n\\n内容为空\";\n        }\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            if (!node || !node.data || !node.data.text) {\n                console.warn(\"⚠️ 跳过无效节点:\", node);\n                return \"\";\n            }\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        const markdownResult = convertNode(data).trim();\n        console.log(\"✅ 思维导图转换完成，结果长度:\", markdownResult.length, \"内容预览:\", markdownResult.substring(0, 100));\n        return markdownResult || \"# 思维导图\\n\\n内容为空\";\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"), {\n                    fileName: file.name,\n                    contentType: typeof file.content,\n                    contentLength: file.content ? file.content.length : 0,\n                    contentPreview: file.content ? file.content.substring(0, 100) : \"null/undefined\"\n                });\n                // 检查文件内容是否有效\n                if (!file.content || typeof file.content !== \"string\" || file.content.trim() === \"\") {\n                    console.warn(\"⚠️ 文件内容为空或无效，使用默认内容\");\n                    // 为空文件提供默认的思维导图内容\n                    const defaultContent = \"# 思维导图\\n\\n## 主要分支\\n\\n- 子节点1\\n- 子节点2\";\n                    const conversionResult = await markdownConverter.convertFromMarkdown(defaultContent);\n                    if (conversionResult.success && conversionResult.data) {\n                        result = {\n                            success: true,\n                            data: conversionResult.data,\n                            error: undefined,\n                            metadata: {\n                                nodeCount: 0,\n                                maxDepth: 0,\n                                processingTime: 0\n                            }\n                        };\n                        console.log(\"✅ \".concat(fileType, \" 文件使用默认内容解析成功\"));\n                    } else {\n                        throw new Error(conversionResult.error || \"\".concat(fileType, \"文件默认内容转换失败\"));\n                    }\n                } else {\n                    const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                    if (conversionResult.success && conversionResult.data) {\n                        result = {\n                            success: true,\n                            data: conversionResult.data,\n                            error: null,\n                            metadata: {\n                                nodeCount: 0,\n                                maxDepth: 0,\n                                processingTime: 0\n                            }\n                        };\n                        console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                    } else {\n                        throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                    }\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数 - 使用 SaveManager\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(content) {\n        let saveType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"editor\";\n        if (!settings.autoSave || !(file === null || file === void 0 ? void 0 : file.id)) return;\n        const saveManager = saveManagerRef.current;\n        saveManager.scheduleAutoSave(file.id, content, {\n            debounceDelay: settings.autoSaveDelay,\n            saveType,\n            immediate: false\n        });\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更 - 使用 SaveManager\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", {\n            hasData: !!data,\n            dataType: typeof data,\n            fileId: file === null || file === void 0 ? void 0 : file.id,\n            isMindMapMode,\n            dataPreview: data ? JSON.stringify(data).substring(0, 200) : \"null\"\n        });\n        // 基础状态验证\n        if (!(file === null || file === void 0 ? void 0 : file.id) || !isMindMapMode || !data) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\", {\n                fileId: file === null || file === void 0 ? void 0 : file.id,\n                isMindMapMode,\n                hasData: !!data\n            });\n            return;\n        }\n        // 将思维导图数据转换为Markdown格式并保存\n        try {\n            const markdownContent = convertMindMapToMarkdown(data);\n            console.log(\"\\uD83D\\uDCBE 准备保存转换后的内容:\", {\n                fileId: file.id,\n                contentLength: markdownContent.length,\n                contentPreview: markdownContent.substring(0, 100)\n            });\n            handleSave(markdownContent, \"mindmap\");\n            console.log(\"✅ 思维导图数据已转换为Markdown并调度保存\");\n        } catch (error) {\n            console.error(\"❌ 思维导图数据转换失败:\", error);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理表格集成和资源\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, []);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 778,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 781,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 776,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 775,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 795,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 794,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SaveStatusIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        saveState: saveState,\n                                        error: saveError,\n                                        onRetry: handleSaveRetry\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 888,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 958,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 954,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 981,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 969,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 989,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 842,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 792,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1010,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1006,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1025,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1028,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1053,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1040,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1058,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1066,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1068,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1061,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1057,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1075,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1086,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1074,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1092,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1095,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1091,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1108,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1003,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1136,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1134,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1138,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1133,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1132,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1144,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1145,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1146,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1147,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1143,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1142,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1156,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1130,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1170,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1169,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1168,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1180,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1181,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1182,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1191,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1183,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1199,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1179,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1178,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    fileId: file === null || file === void 0 ? void 0 : file.id,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1209,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1227,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1228,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1229,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1226,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1225,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1166,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1247,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1246,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1245,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1244,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1237,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1236,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 790,\n        columnNumber: 5\n    }, undefined);\n}, \"yI+jkYuw0q9xdoIt2vRx4438wso=\")), \"yI+jkYuw0q9xdoIt2vRx4438wso=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});
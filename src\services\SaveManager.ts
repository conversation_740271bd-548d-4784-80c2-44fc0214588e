/**
 * SaveManager - 统一保存管理器
 * 负责管理所有文件的保存操作，解决多个独立定时器系统导致的竞态条件问题
 * 
 * 功能特性：
 * - 统一的防抖机制
 * - 文件状态管理和锁定
 * - 保存队列处理
 * - 错误处理和重试机制
 * - 保存状态回调通知
 */

import { 
  SaveState, 
  SaveOptions, 
  FileSaveState, 
  SaveStateChangeCallback, 
  SaveExecuteCallback 
} from '@/types';

export class SaveManager {
  private static instance: SaveManager;
  
  /** 文件保存状态映射 */
  private fileSaveStates: Map<string, FileSaveState> = new Map();
  
  /** 当前活动文件ID */
  private currentFileId: string | null = null;
  
  /** 保存状态变更回调 */
  private stateChangeCallbacks: Set<SaveStateChangeCallback> = new Set();
  
  /** 保存执行回调 */
  private saveExecuteCallback: SaveExecuteCallback | null = null;
  
  /** 默认保存选项 */
  private readonly defaultOptions: Required<SaveOptions> = {
    debounceDelay: 1000,
    saveType: 'editor',
    immediate: false,
    retryCount: 3
  };

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  /**
   * 获取 SaveManager 单例实例
   */
  public static getInstance(): SaveManager {
    if (!SaveManager.instance) {
      SaveManager.instance = new SaveManager();
    }
    return SaveManager.instance;
  }

  /**
   * 设置保存执行回调
   */
  public setSaveExecuteCallback(callback: SaveExecuteCallback): void {
    this.saveExecuteCallback = callback;
  }

  /**
   * 添加保存状态变更监听器
   */
  public addStateChangeListener(callback: SaveStateChangeCallback): void {
    this.stateChangeCallbacks.add(callback);
  }

  /**
   * 移除保存状态变更监听器
   */
  public removeStateChangeListener(callback: SaveStateChangeCallback): void {
    this.stateChangeCallbacks.delete(callback);
  }

  /**
   * 通知保存状态变更
   */
  private notifyStateChange(fileId: string, state: SaveState, error?: string): void {
    // 添加详细的状态变更日志
    const timestamp = new Date().toLocaleTimeString('zh-CN', { hour12: false });
    console.log(`📊 [${timestamp}] 保存状态变更:`, {
      fileId,
      state,
      error: error || '无',
      callbackCount: this.stateChangeCallbacks.size
    });

    this.stateChangeCallbacks.forEach(callback => {
      try {
        callback(fileId, state, error);
      } catch (err) {
        console.error('❌ 保存状态回调执行失败:', err);
      }
    });
  }

  /**
   * 获取文件保存统计信息
   */
  public getFileSaveStats(fileId: string): {
    totalSaves: number;
    lastSaveTime: number;
    errorCount: number;
    currentState: SaveState;
  } | null {
    const fileState = this.fileSaveStates.get(fileId);
    if (!fileState) return null;

    return {
      totalSaves: fileState.retryCount > 0 ? 1 : (fileState.lastSaveTime > 0 ? 1 : 0),
      lastSaveTime: fileState.lastSaveTime,
      errorCount: fileState.retryCount,
      currentState: fileState.state
    };
  }

  /**
   * 强制重试保存
   */
  public async retryFileSave(fileId: string): Promise<boolean> {
    const fileState = this.fileSaveStates.get(fileId);
    if (!fileState || fileState.state !== SaveState.error) {
      console.warn('⚠️ 无法重试保存，文件状态不正确:', fileId, fileState?.state);
      return false;
    }

    console.log('🔄 手动重试保存:', fileId);
    fileState.retryCount = 0; // 重置重试计数
    return await this.executeSave(fileId);
  }

  /**
   * 获取文件保存状态
   */
  public getFileSaveState(fileId: string): FileSaveState | null {
    return this.fileSaveStates.get(fileId) || null;
  }

  /**
   * 设置当前活动文件
   */
  public setCurrentFile(fileId: string): void {
    this.currentFileId = fileId;
    console.log('📁 设置当前活动文件:', fileId);
  }

  /**
   * 获取当前活动文件ID
   */
  public getCurrentFileId(): string | null {
    return this.currentFileId;
  }

  /**
   * 调度自动保存
   */
  public scheduleAutoSave(fileId: string, data: any, options: Partial<SaveOptions> = {}): void {
    const mergedOptions = { ...this.defaultOptions, ...options };
    
    // 获取或创建文件保存状态
    let fileState = this.fileSaveStates.get(fileId);
    if (!fileState) {
      fileState = {
        fileId,
        state: SaveState.idle,
        lastSaveTime: 0,
        retryCount: 0
      };
      this.fileSaveStates.set(fileId, fileState);
    }

    // 如果当前正在保存，跳过
    if (fileState.state === SaveState.saving) {
      console.log('⏳ 文件正在保存中，跳过新的保存请求:', fileId);
      return;
    }

    // 清除现有定时器
    if (fileState.timer) {
      clearTimeout(fileState.timer);
    }

    // 更新状态
    fileState.state = SaveState.pending;
    fileState.pendingData = data;
    fileState.options = mergedOptions;
    
    this.notifyStateChange(fileId, SaveState.pending);

    if (mergedOptions.immediate) {
      // 立即执行保存
      this.executeSave(fileId);
    } else {
      // 设置防抖定时器
      fileState.timer = setTimeout(() => {
        this.executeSave(fileId);
      }, mergedOptions.debounceDelay);
    }

    console.log(`💾 调度自动保存 [${mergedOptions.saveType}]:`, fileId, 
                `延迟: ${mergedOptions.immediate ? '立即' : mergedOptions.debounceDelay + 'ms'}`);
  }

  /**
   * 强制立即保存
   */
  public async forceSave(fileId: string): Promise<boolean> {
    console.log('🚀 强制立即保存:', fileId);
    
    const fileState = this.fileSaveStates.get(fileId);
    if (!fileState || !fileState.pendingData) {
      console.log('📝 没有待保存的数据:', fileId);
      return true;
    }

    // 清除定时器
    if (fileState.timer) {
      clearTimeout(fileState.timer);
      fileState.timer = undefined;
    }

    return await this.executeSave(fileId);
  }

  /**
   * 执行保存操作
   */
  private async executeSave(fileId: string): Promise<boolean> {
    const fileState = this.fileSaveStates.get(fileId);
    if (!fileState || !fileState.pendingData) {
      console.warn('⚠️ 执行保存时找不到文件状态或数据:', fileId);
      return false;
    }

    // 验证文件是否仍然是当前活动文件（防止文件切换后的延迟保存）
    if (this.currentFileId && this.currentFileId !== fileId) {
      console.log('🔒 文件已切换，跳过保存:', fileId, '当前文件:', this.currentFileId);
      fileState.state = SaveState.idle;
      this.notifyStateChange(fileId, SaveState.idle);
      return false;
    }

    try {
      // 更新状态为保存中
      fileState.state = SaveState.saving;
      fileState.error = undefined;
      this.notifyStateChange(fileId, SaveState.saving);

      console.log('💾 开始执行保存:', fileId);

      // 执行保存回调
      if (!this.saveExecuteCallback) {
        throw new Error('保存执行回调未设置');
      }

      const success = await this.saveExecuteCallback(fileId, fileState.pendingData, fileState.options);

      if (success) {
        // 保存成功
        fileState.state = SaveState.saved;
        fileState.lastSaveTime = Date.now();
        fileState.retryCount = 0;
        fileState.pendingData = undefined;
        
        this.notifyStateChange(fileId, SaveState.saved);
        console.log('✅ 保存成功:', fileId);
        
        // 3秒后自动重置状态为空闲
        setTimeout(() => {
          if (fileState && fileState.state === SaveState.saved) {
            fileState.state = SaveState.idle;
            this.notifyStateChange(fileId, SaveState.idle);
          }
        }, 3000);
        
        return true;
      } else {
        throw new Error('保存操作返回失败');
      }
    } catch (error) {
      // 保存失败
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      fileState.state = SaveState.error;
      fileState.error = errorMessage;
      fileState.retryCount++;
      
      this.notifyStateChange(fileId, SaveState.error, errorMessage);
      console.error('❌ 保存失败:', fileId, errorMessage);

      // 如果还有重试次数，安排重试
      if (fileState.retryCount < (fileState.options?.retryCount || this.defaultOptions.retryCount)) {
        console.log(`🔄 安排重试保存 (${fileState.retryCount}/${fileState.options?.retryCount || this.defaultOptions.retryCount}):`, fileId);
        setTimeout(() => {
          this.executeSave(fileId);
        }, 2000 * fileState.retryCount); // 递增延迟重试
      }
      
      return false;
    }
  }

  /**
   * 文件切换处理
   */
  public async switchFile(fromFileId: string | null, toFileId: string): Promise<void> {
    console.log('🔄 文件切换:', fromFileId, '->', toFileId);

    // 强制保存当前文件
    if (fromFileId) {
      await this.forceSave(fromFileId);
    }

    // 设置新的当前文件
    this.setCurrentFile(toFileId);
  }

  /**
   * 清理文件保存状态
   */
  public clearFileState(fileId: string): void {
    const fileState = this.fileSaveStates.get(fileId);
    if (fileState) {
      // 清除定时器
      if (fileState.timer) {
        clearTimeout(fileState.timer);
      }
      
      // 移除状态
      this.fileSaveStates.delete(fileId);
      console.log('🗑️ 清理文件保存状态:', fileId);
    }
  }

  /**
   * 获取所有文件的保存状态
   */
  public getAllFileSaveStates(): Map<string, FileSaveState> {
    return new Map(this.fileSaveStates);
  }

  /**
   * 销毁管理器（清理所有资源）
   */
  public destroy(): void {
    // 清除所有定时器
    this.fileSaveStates.forEach(fileState => {
      if (fileState.timer) {
        clearTimeout(fileState.timer);
      }
    });

    // 清理状态
    this.fileSaveStates.clear();
    this.stateChangeCallbacks.clear();
    this.saveExecuteCallback = null;
    this.currentFileId = null;

    console.log('🗑️ SaveManager 已销毁');
  }
}

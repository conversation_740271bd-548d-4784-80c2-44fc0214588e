"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    // 🔧 统一的定时器管理 - 只保留一个定时器用于自动保存\n    const saveTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 统一的定时器清理函数\n    const clearSaveTimer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (saveTimeoutRef.current) {\n            clearTimeout(saveTimeoutRef.current);\n            saveTimeoutRef.current = undefined;\n            console.log(\"\\uD83D\\uDD12 清除保存定时器\");\n        }\n    }, []);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 🔧 文件切换时清除所有定时器\n        clearSaveTimer();\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        // 🔧 视图切换时清除所有定时器\n        clearSaveTimer();\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        if (!settings.autoSave || !onContentChange) return;\n        clearSaveTimer();\n        saveTimeoutRef.current = setTimeout(()=>{\n            onContentChange(content);\n        }, settings.autoSaveDelay);\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        onContentChange,\n        clearSaveTimer\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 🔧 增强文件状态验证 - 捕获当前文件ID用于闭包验证\n        const currentFileId = file === null || file === void 0 ? void 0 : file.id;\n        const currentMindMapMode = isMindMapMode;\n        // 基础状态验证\n        if (!file || !currentMindMapMode || !currentFileId) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 🔧 延迟验证，防止异步竞态条件\n        setTimeout(()=>{\n            // 二次验证文件ID一致性，防止跨文件保存\n            if ((file === null || file === void 0 ? void 0 : file.id) !== currentFileId) {\n                console.log(\"⚠️ 文件已切换，跳过保存 (原文件ID:\", currentFileId, \"当前文件ID:\", file === null || file === void 0 ? void 0 : file.id, \")\");\n                return;\n            }\n            // 验证思维导图模式状态\n            if (!isMindMapMode) {\n                console.log(\"⚠️ 已退出思维导图模式，跳过保存\");\n                return;\n            }\n            // 将思维导图数据转换为Markdown格式并保存\n            if (data && onContentChange) {\n                const markdownContent = convertMindMapToMarkdown(data);\n                handleSave(markdownContent);\n                console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存 (文件ID:\", currentFileId, \")\");\n            }\n        }, 0) // 使用微任务延迟，确保状态更新完成\n        ;\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        onContentChange,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearSaveTimer();\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, [\n        clearSaveTimer\n    ]);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 645,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 644,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 661,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 869,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 902,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 919,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 948,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 953,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 971,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 970,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 866,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 865,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 997,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 996,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1000,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 995,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 994,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1006,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1007,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1009,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1005,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1018,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 992,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1033,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1032,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1030,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1043,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1044,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1046,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1045,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1041,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1040,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1071,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1089,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1087,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1086,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1028,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1108,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1109,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1111,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1106,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1105,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1098,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1097,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 659,\n        columnNumber: 5\n    }, undefined);\n}, \"PG2CD78xuZgttSDmLmbn9ucF1P0=\")), \"PG2CD78xuZgttSDmLmbn9ucF1P0=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var lib0_mutex__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/mutex */ \"(app-pages-browser)/./node_modules/lib0/mutex.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n *\r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \n\n\nclass CoreManager {\n    /**\r\n   * 实现ICleanable接口 - 是否已销毁\r\n   */ get isDestroyed() {\n        return this._cleanupState === _types__WEBPACK_IMPORTED_MODULE_1__.CleanupState.DESTROYED;\n    }\n    /**\r\n   * 实现ICleanable接口 - 当前清理状态\r\n   */ get cleanupState() {\n        return this._cleanupState;\n    }\n    /**\r\n   * 生成唯一标识符\r\n   */ generateUniqueId() {\n        return \"mindmap_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    /**\r\n   * 生成数据版本号\r\n   */ generateDataVersion(data) {\n        const dataString = JSON.stringify(data);\n        return \"v_\".concat(Date.now(), \"_\").concat(this.hashCode(dataString));\n    }\n    /**\r\n   * 简单哈希函数\r\n   */ hashCode(str) {\n        let hash = 0;\n        if (str.length === 0) return hash.toString();\n        for(let i = 0; i < str.length; i++){\n            const char = str.charCodeAt(i);\n            hash = (hash << 5) - hash + char;\n            hash = hash & hash; // Convert to 32bit integer\n        }\n        return Math.abs(hash).toString(36);\n    }\n    /**\r\n   * 为数据添加元数据\r\n   */ addDataMetadata(data) {\n        const dataVersion = this.generateDataVersion(data);\n        const enhancedData = {\n            ...data,\n            _metadata: {\n                dataVersion,\n                instanceId: this.instanceId,\n                timestamp: Date.now()\n            }\n        };\n        this.currentDataVersion = dataVersion;\n        return enhancedData;\n    }\n    /**\r\n   * 验证数据一致性\r\n   */ validateDataConsistency(data) {\n        if (!data._metadata) {\n            // 没有元数据的数据被认为是有效的（向后兼容）\n            return true;\n        }\n        // 检查实例ID是否匹配\n        if (data._metadata.instanceId !== this.instanceId) {\n            console.warn(\"⚠️ 数据实例ID不匹配，可能来自不同的思维导图实例\");\n            return false;\n        }\n        return true;\n    }\n    /**\r\n   * 移除数据元数据（用于传递给SimpleMindMap）\r\n   */ removeDataMetadata(data) {\n        const { _metadata, ...cleanData } = data;\n        return cleanData;\n    }\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        return new Promise((resolve, reject)=>{\n            this.initMutex(async ()=>{\n                try {\n                    // 动态导入SimpleMindMap完整版（包含所有插件，特别是Drag插件）\n                    const { default: SimpleMindMap } = await Promise.all(/*! import() */[__webpack_require__.e(\"css-node_modules_quill_dist_quill_snow_css\"), __webpack_require__.e(\"_app-pages-browser_node_modules_simple-mind-map_full_js\")]).then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map/full.js */ \"(app-pages-browser)/./node_modules/simple-mind-map/full.js\"));\n                    // 清理现有实例（使用强制销毁）\n                    if (this.mindMapInstance) {\n                        await this.forceDestroy();\n                    }\n                    // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器\n                    const finalConfig = {\n                        ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                        ...this.config.config,\n                        el: this.config.container,\n                        data: this.config.data,\n                        readonly: this.config.readonly\n                    };\n                    // 创建实例\n                    this.mindMapInstance = new SimpleMindMap(finalConfig);\n                    this.isInitialized = true;\n                    // 设置事件处理器并保存引用\n                    this.setupEventHandlers();\n                    resolve(this.mindMapInstance);\n                } catch (error) {\n                    console.error(\"❌ SimpleMindMap初始化失败:\", error);\n                    const errorMessage = \"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\");\n                    reject(new Error(errorMessage));\n                }\n            });\n        });\n    }\n    /**\r\n   * 设置事件处理器并保存引用\r\n   */ setupEventHandlers() {\n        if (!this.mindMapInstance) return;\n        // 清理旧的事件处理器\n        this.eventHandlers.clear();\n        // 定义事件处理器\n        const dataChangeHandler = (data)=>{\n            console.log(\"\\uD83D\\uDD04 SimpleMindMap数据变更:\", data);\n        };\n        const nodeActiveHandler = (node)=>{\n            console.log(\"\\uD83C\\uDFAF 节点激活:\", node);\n        };\n        // 保存事件处理器引用\n        this.eventHandlers.set(\"data_change\", dataChangeHandler);\n        this.eventHandlers.set(\"node_active\", nodeActiveHandler);\n        // 绑定事件监听器\n        try {\n            this.mindMapInstance.on(\"data_change\", dataChangeHandler);\n            this.mindMapInstance.on(\"node_active\", nodeActiveHandler);\n            console.log(\"✅ 事件处理器设置完成，已保存引用\");\n        } catch (error) {\n            console.warn(\"⚠️ 事件处理器设置失败:\", error);\n        }\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据（带版本控制）\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            var _enhancedData__metadata;\n            // 添加数据元数据\n            const enhancedData = this.addDataMetadata(data);\n            // 验证数据一致性\n            if (!this.validateDataConsistency(enhancedData)) {\n                console.warn(\"⚠️ 数据版本不匹配，跳过设置\");\n                return;\n            }\n            // 移除元数据后传递给SimpleMindMap\n            const cleanData = this.removeDataMetadata(enhancedData);\n            this.mindMapInstance.setData(cleanData);\n            console.log(\"✅ 数据设置成功，版本:\", (_enhancedData__metadata = enhancedData._metadata) === null || _enhancedData__metadata === void 0 ? void 0 : _enhancedData__metadata.dataVersion);\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 容器尺寸变化后调整画布\r\n   */ resize() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 先更新容器位置和尺寸信息\n            this.mindMapInstance.getElRectInfo();\n            // 然后调整画布尺寸\n            this.mindMapInstance.resize();\n        } catch (error) {\n            console.error(\"❌ 画布尺寸调整失败:\", error);\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 实现ICleanable接口 - 销毁方法\r\n   */ async destroy() {\n        if (this._cleanupState !== _types__WEBPACK_IMPORTED_MODULE_1__.CleanupState.ACTIVE) {\n            console.log(\"\\uD83D\\uDD12 CoreManager已在清理中或已销毁，跳过重复清理\");\n            return;\n        }\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_1__.CleanupState.DESTROYING;\n        console.log(\"\\uD83E\\uDDF9 开始清理CoreManager...\");\n        try {\n            await this.forceDestroy();\n            this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_1__.CleanupState.DESTROYED;\n            console.log(\"✅ CoreManager清理完成\");\n        } catch (error) {\n            console.error(\"❌ CoreManager清理失败:\", error);\n            this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_1__.CleanupState.ACTIVE; // 重置状态以允许重试\n            throw error;\n        }\n    }\n    /**\r\n   * 强制销毁实例（完整清理版本）\r\n   */ async forceDestroy() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用正确的事件处理器引用进行解绑\n            if (this.mindMapInstance && this.eventHandlers.size > 0) {\n                try {\n                    console.log(\"\\uD83D\\uDD27 开始解绑事件处理器...\");\n                    for (const [eventName, handler] of this.eventHandlers){\n                        try {\n                            const instance = this.mindMapInstance;\n                            if (instance && typeof instance.off === \"function\") {\n                                instance.off(eventName, handler);\n                                console.log(\"✅ 已解绑事件: \".concat(eventName));\n                            }\n                        } catch (error) {\n                            console.warn(\"⚠️ 解绑事件失败 \".concat(eventName, \":\"), error);\n                        }\n                    }\n                    // 清理事件处理器映射\n                    this.eventHandlers.clear();\n                    console.log(\"✅ 所有事件处理器已解绑并清理\");\n                } catch (error) {\n                    console.warn(\"⚠️ 事件解绑过程失败:\", error);\n                }\n            }\n            // 销毁实例\n            this.mindMapInstance.destroy();\n            // 清理DOM引用\n            this.clearDOMReferences();\n            // 等待销毁完成\n            await this.waitForDestruction();\n            console.log(\"✅ SimpleMindMap实例强制销毁完成\");\n        } catch (error) {\n            console.error(\"❌ 强制销毁SimpleMindMap实例失败:\", error);\n            throw error;\n        } finally{\n            this.mindMapInstance = null;\n            this.isInitialized = false;\n            this.eventHandlers.clear();\n        }\n    }\n    /**\r\n   * 清理DOM引用\r\n   */ clearDOMReferences() {\n        try {\n            // 清理容器中可能残留的SimpleMindMap相关DOM元素\n            if (this.config.container) {\n                const container = this.config.container;\n                // 移除所有子元素\n                while(container.firstChild){\n                    container.removeChild(container.firstChild);\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ DOM引用清理失败:\", error);\n        }\n    }\n    /**\r\n   * 等待销毁完成\r\n   */ async waitForDestruction() {\n        // 给予适当的等待时间确保异步销毁完成\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 100);\n        });\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.initMutex = (0,lib0_mutex__WEBPACK_IMPORTED_MODULE_2__.createMutex)();\n        // 数据版本控制\n        this.currentDataVersion = null;\n        this.instanceId = this.generateUniqueId();\n        // 清理状态管理\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_1__.CleanupState.ACTIVE;\n        this.eventHandlers = new Map();\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CleanupState: function() { return /* binding */ CleanupState; }\n/* harmony export */ });\n/* harmony import */ var _mindmap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mindmap */ \"(app-pages-browser)/./src/types/mindmap.ts\");\n/**\r\n * 作品展示平台 - TypeScript 类型定义\r\n */ // 作品内容类型\nvar CleanupState;\n(function(CleanupState) {\n    CleanupState[\"ACTIVE\"] = \"active\";\n    CleanupState[\"DESTROYING\"] = \"destroying\";\n    CleanupState[\"DESTROYED\"] = \"destroyed\"; // 已销毁完成\n})(CleanupState || (CleanupState = {}));\n// ==================== 思维导图相关类型定义 ====================\n// 导出思维导图相关类型\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/mindmap.ts":
/*!******************************!*\
  !*** ./src/types/mindmap.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/**\r\n * MindMap Type Definitions\r\n * 思维导图相关类型定义\r\n */ /**\r\n * SimpleMindMap 节点数据结构\r\n */ /**\r\n * 主题配置\r\n */ \n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/mindmap.ts\n"));

/***/ })

});
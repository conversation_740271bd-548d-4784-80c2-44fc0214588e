"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/EventManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventManager: function() { return /* binding */ EventManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * EventManager - 事件管理器\r\n * 负责SimpleMindMap事件的统一管理和分发\r\n * \r\n * 职责：\r\n * - 监听SimpleMindMap官方事件\r\n * - 事件处理和回调分发\r\n * - 自定义事件逻辑\r\n */ \nclass EventManager {\n    /**\r\n   * 初始化事件监听\r\n   */ initialize() {\n        this.setupNodeEvents();\n        this.setupDataEvents();\n        this.setupRenderEvents();\n        this.setupCanvasEvents();\n        this.isInitialized = true;\n        console.log(\"✅ 事件管理器初始化完成\");\n    }\n    /**\r\n   * 设置节点相关事件\r\n   */ setupNodeEvents() {\n        // 节点点击事件\n        const nodeClickHandler = (node)=>{\n            var _this_config_onNodeClick, _this_config;\n            console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node);\n            (_this_config_onNodeClick = (_this_config = this.config).onNodeClick) === null || _this_config_onNodeClick === void 0 ? void 0 : _this_config_onNodeClick.call(_this_config, node);\n        };\n        // 节点激活事件\n        const nodeActiveHandler = (node, activeNodeList)=>{\n            console.log(\"✨ 节点激活:\", {\n                node,\n                activeNodeList\n            });\n        // 可以在这里添加节点激活的自定义逻辑\n        };\n        // 节点右键菜单事件\n        const nodeContextMenuHandler = (event, node)=>{\n            console.log(\"\\uD83D\\uDDB1️ 节点右键菜单:\", {\n                event,\n                node\n            });\n            // 阻止默认右键菜单\n            event.preventDefault();\n            event.stopPropagation();\n            // 计算菜单位置并显示\n            const position = this.calculateMenuPosition(event);\n            this.showContextMenu({\n                position,\n                node\n            });\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_click\", nodeClickHandler);\n        this.mindMapInstance.on(\"node_active\", nodeActiveHandler);\n        this.mindMapInstance.on(\"node_contextmenu\", nodeContextMenuHandler);\n        // 存储事件处理器用于清理\n        this.eventListeners.set(\"node_click\", nodeClickHandler);\n        this.eventListeners.set(\"node_active\", nodeActiveHandler);\n        this.eventListeners.set(\"node_contextmenu\", nodeContextMenuHandler);\n    }\n    /**\r\n   * 设置数据相关事件\r\n   */ setupDataEvents() {\n        // 数据变更事件\n        const dataChangeHandler = (newData)=>{\n            var _this_config_onDataChange, _this_config;\n            console.log(\"\\uD83D\\uDCCA 数据变更:\", newData);\n            (_this_config_onDataChange = (_this_config = this.config).onDataChange) === null || _this_config_onDataChange === void 0 ? void 0 : _this_config_onDataChange.call(_this_config, newData);\n        };\n        // 数据设置前事件\n        const beforeSetDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 准备设置数据:\", data);\n        };\n        // 数据设置后事件\n        const setDataHandler = (data)=>{\n            console.log(\"\\uD83D\\uDCCB 数据设置完成:\", data);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"data_change\", dataChangeHandler);\n        this.mindMapInstance.on(\"before_set_data\", beforeSetDataHandler);\n        this.mindMapInstance.on(\"set_data\", setDataHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"data_change\", dataChangeHandler);\n        this.eventListeners.set(\"before_set_data\", beforeSetDataHandler);\n        this.eventListeners.set(\"set_data\", setDataHandler);\n    }\n    /**\r\n   * 设置渲染相关事件\r\n   */ setupRenderEvents() {\n        // 节点树渲染结束事件\n        const nodeTreeRenderEndHandler = ()=>{\n            var _this_config_onRenderComplete, _this_config;\n            console.log(\"\\uD83C\\uDFA8 节点树渲染完成\");\n            // 完全不在EventManager中处理视图适应，交给组件级别控制\n            // 这样可以确保用户的视图位置永远不会被意外重置\n            console.log(\"✅ 渲染完成，保持当前视图位置\");\n            (_this_config_onRenderComplete = (_this_config = this.config).onRenderComplete) === null || _this_config_onRenderComplete === void 0 ? void 0 : _this_config_onRenderComplete.call(_this_config);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"node_tree_render_end\", nodeTreeRenderEndHandler);\n    }\n    /**\r\n   * 设置画布相关事件\r\n   */ setupCanvasEvents() {\n        // 画布点击事件\n        const drawClickHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布点击\");\n            // 隐藏右键菜单\n            if (this.contextMenuVisible) {\n                this.hideContextMenu();\n            }\n        };\n        // 画布拖拽事件\n        const drawDragHandler = ()=>{\n            console.log(\"\\uD83D\\uDDBC️ 画布拖拽\");\n        };\n        // 缩放事件\n        const scaleHandler = (scale)=>{\n            console.log(\"\\uD83D\\uDD0D 缩放变化:\", scale);\n        };\n        // 绑定事件\n        this.mindMapInstance.on(\"draw_click\", drawClickHandler);\n        this.mindMapInstance.on(\"view_data_change\", drawDragHandler);\n        this.mindMapInstance.on(\"scale\", scaleHandler);\n        // 存储事件处理器\n        this.eventListeners.set(\"draw_click\", drawClickHandler);\n        this.eventListeners.set(\"view_data_change\", drawDragHandler);\n        this.eventListeners.set(\"scale\", scaleHandler);\n    }\n    /**\r\n   * 手动触发事件\r\n   */ emit(event) {\n        for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n            args[_key - 1] = arguments[_key];\n        }\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.emit(event, ...args);\n        } catch (error) {\n            console.error(\"❌ 触发事件失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 添加自定义事件监听器\r\n   */ addEventListener(event, handler) {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.on(event, handler);\n            this.eventListeners.set(event, handler);\n            console.log(\"✅ 添加事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 添加事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 移除事件监听器\r\n   */ removeEventListener(event) {\n        const handler = this.eventListeners.get(event);\n        if (!handler || !this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.off(event, handler);\n            this.eventListeners.delete(event);\n            console.log(\"✅ 移除事件监听器: \".concat(event));\n        } catch (error) {\n            console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n        }\n    }\n    /**\r\n   * 获取当前激活的节点列表\r\n   */ getActiveNodes() {\n        var _this_mindMapInstance;\n        if (!((_this_mindMapInstance = this.mindMapInstance) === null || _this_mindMapInstance === void 0 ? void 0 : _this_mindMapInstance.renderer)) return [];\n        try {\n            return this.mindMapInstance.renderer.activeNodeList || [];\n        } catch (error) {\n            console.error(\"❌ 获取激活节点失败:\", error);\n            return [];\n        }\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 计算菜单位置，确保不超出视口\r\n   */ calculateMenuPosition(event) {\n        const viewportWidth = window.innerWidth;\n        const viewportHeight = window.innerHeight;\n        let x = event.clientX;\n        let y = event.clientY;\n        // 预估菜单尺寸（实际尺寸会在组件中调整）\n        const estimatedMenuWidth = 200;\n        const estimatedMenuHeight = 300;\n        // 水平方向调整\n        if (x + estimatedMenuWidth > viewportWidth) {\n            x = viewportWidth - estimatedMenuWidth - 10;\n        }\n        if (x < 10) {\n            x = 10;\n        }\n        // 垂直方向调整\n        if (y + estimatedMenuHeight > viewportHeight) {\n            y = viewportHeight - estimatedMenuHeight - 10;\n        }\n        if (y < 10) {\n            y = 10;\n        }\n        return {\n            x,\n            y\n        };\n    }\n    /**\r\n   * 显示右键菜单\r\n   */ showContextMenu(param) {\n        let { position, node } = param;\n        var // 触发菜单显示回调\n        _this_config_onContextMenuShow, _this_config;\n        this.contextMenuPosition = position;\n        this.contextMenuNode = node;\n        this.contextMenuVisible = true;\n        (_this_config_onContextMenuShow = (_this_config = this.config).onContextMenuShow) === null || _this_config_onContextMenuShow === void 0 ? void 0 : _this_config_onContextMenuShow.call(_this_config, position, node);\n        console.log(\"✅ 显示右键菜单:\", {\n            position,\n            node\n        });\n    }\n    /**\r\n   * 隐藏右键菜单\r\n   */ hideContextMenu() {\n        var // 触发菜单隐藏回调\n        _this_config_onContextMenuHide, _this_config;\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        (_this_config_onContextMenuHide = (_this_config = this.config).onContextMenuHide) === null || _this_config_onContextMenuHide === void 0 ? void 0 : _this_config_onContextMenuHide.call(_this_config);\n        console.log(\"✅ 隐藏右键菜单\");\n    }\n    /**\r\n   * 获取右键菜单状态\r\n   */ getContextMenuState() {\n        return {\n            visible: this.contextMenuVisible,\n            position: this.contextMenuPosition,\n            node: this.contextMenuNode\n        };\n    }\n    /**\r\n   * 销毁事件管理器\r\n   */ destroy() {\n        // 移除所有事件监听器\n        for (const [event, handler] of this.eventListeners){\n            try {\n                this.mindMapInstance.off(event, handler);\n            } catch (error) {\n                console.error(\"❌ 移除事件监听器失败 \".concat(event, \":\"), error);\n            }\n        }\n        // 清理右键菜单状态\n        this.hideContextMenu();\n        this.eventListeners.clear();\n        this.isInitialized = false;\n        // 不重置isFirstRender，保持首次渲染状态\n        console.log(\"✅ 事件管理器销毁完成\");\n    }\n    constructor(mindMapInstance, config){\n        this.eventListeners = new Map();\n        this.isInitialized = false;\n        this.isFirstRender = true;\n        // 右键菜单状态管理\n        this.contextMenuVisible = false;\n        this.contextMenuPosition = {\n            x: 0,\n            y: 0\n        };\n        this.contextMenuNode = null;\n        // 清理状态管理\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE;\n        this.mindMapInstance = mindMapInstance;\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\n"));

/***/ })

});
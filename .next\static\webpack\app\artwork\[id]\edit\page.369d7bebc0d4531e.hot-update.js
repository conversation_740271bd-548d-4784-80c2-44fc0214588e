"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/artwork/[id]/edit/page.tsx":
/*!********************************************!*\
  !*** ./src/app/artwork/[id]/edit/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArtworkEditorPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_artworkService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/artworkService */ \"(app-pages-browser)/./src/services/artworkService.ts\");\n/* harmony import */ var _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/* harmony import */ var _services_fileTreeEventService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/fileTreeEventService */ \"(app-pages-browser)/./src/services/fileTreeEventService.ts\");\n/* harmony import */ var _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/chatHistoryService */ \"(app-pages-browser)/./src/services/chatHistoryService.ts\");\n/* harmony import */ var _components_FileTreePanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/FileTreePanel */ \"(app-pages-browser)/./src/components/FileTreePanel/index.tsx\");\n/* harmony import */ var _components_EditorPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/EditorPanel */ \"(app-pages-browser)/./src/components/EditorPanel/index.tsx\");\n/* harmony import */ var _components_AIAssistant__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/AIAssistant */ \"(app-pages-browser)/./src/components/AIAssistant/index.tsx\");\n/* harmony import */ var _components_ResizableLayout__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ResizableLayout */ \"(app-pages-browser)/./src/components/ResizableLayout/index.tsx\");\n/**\r\n * 作品编辑页面\r\n * 三栏布局的编辑器界面\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置\nconst DEFAULT_EDITOR_SETTINGS = {\n    fontSize: 14,\n    fontWeight: 400,\n    fontFamily: 'Monaco, Consolas, \"Courier New\", monospace',\n    theme: \"light\",\n    wordWrap: true,\n    showLineNumbers: true,\n    enablePreview: true,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\n// 默认文件树结构\nconst DEFAULT_FILE_TREE = {\n    id: \"root\",\n    name: \"root\",\n    type: \"folder\",\n    parentId: null,\n    path: \"/\",\n    createdAt: Date.now(),\n    updatedAt: Date.now(),\n    isExpanded: true,\n    children: [\n        {\n            id: \"folder-roles\",\n            name: \"角色\",\n            type: \"folder\",\n            parentId: \"root\",\n            path: \"/角色\",\n            createdAt: Date.now(),\n            updatedAt: Date.now(),\n            isExpanded: false,\n            children: []\n        },\n        {\n            id: \"folder-outline\",\n            name: \"大纲\",\n            type: \"folder\",\n            parentId: \"root\",\n            path: \"/大纲\",\n            createdAt: Date.now(),\n            updatedAt: Date.now(),\n            isExpanded: false,\n            children: []\n        },\n        {\n            id: \"folder-knowledge\",\n            name: \"知识库\",\n            type: \"folder\",\n            parentId: \"root\",\n            path: \"/知识库\",\n            createdAt: Date.now(),\n            updatedAt: Date.now(),\n            isExpanded: false,\n            children: []\n        }\n    ]\n};\nfunction ArtworkEditorPage() {\n    var _editorState_currentFile, _editorState_currentFile1, _editorState_currentFile2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const artworkId = params === null || params === void 0 ? void 0 : params.id;\n    // 编辑器状态\n    const [editorState, setEditorState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentFile: null,\n        fileTree: DEFAULT_FILE_TREE,\n        aiHistory: [],\n        settings: DEFAULT_EDITOR_SETTINGS,\n        isLoading: true,\n        unsavedChanges: false,\n        error: null\n    });\n    // 文件树刷新状态已移除，现在使用事件系统自动刷新\n    // 作品数据\n    const [artwork, setArtwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatHistoryService = _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_6__.ChatHistoryService.getInstance();\n    // 自动关联状态反馈\n    const [autoAssociationStatus, setAutoAssociationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"idle\",\n        message: \"\"\n    });\n    // 监听当前文件变化，实现自动关联\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoAssociationEnabled && editorState.currentFile) {\n            const fileId = editorState.currentFile.id;\n            const fileName = editorState.currentFile.name;\n            console.log(\"\\uD83D\\uDD04 自动关联当前编辑文件:\", fileId);\n            // 🔧 静默关联，不显示处理状态，避免不必要的UI更新\n            // 使用现有的聚焦文件方法实现自动关联\n            chatHistoryService.focusCurrentEditingFile(fileId).then(()=>{\n                console.log(\"✅ 已自动关联文件:\", fileName);\n            // 🔧 移除状态更新，避免触发重新渲染\n            }).catch((error)=>{\n                console.error(\"❌ 自动关联文件失败:\", error);\n            // 🔧 只在控制台记录错误，不更新UI状态\n            });\n        }\n    }, [\n        (_editorState_currentFile = editorState.currentFile) === null || _editorState_currentFile === void 0 ? void 0 : _editorState_currentFile.id,\n        autoAssociationEnabled,\n        chatHistoryService\n    ]) // 🔧 只依赖文件ID，避免不必要的重新执行\n    ;\n    // 处理自动关联开关变化\n    const handleAutoAssociationToggle = (enabled)=>{\n        setAutoAssociationEnabled(enabled);\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", enabled ? \"开启\" : \"关闭\");\n    };\n    // 加载保存的编辑器设置\n    const loadSavedSettings = async ()=>{\n        try {\n            const { DatabaseService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\"));\n            const dbService = DatabaseService.getInstance();\n            // 尝试获取保存的设置\n            const result = await dbService.get(\"editor-settings\", artworkId);\n            if (result.success && result.data && result.data.settings) {\n                // 更新编辑器状态\n                setEditorState((prev)=>({\n                        ...prev,\n                        settings: result.data.settings\n                    }));\n                console.log(\"✅ 已加载保存的编辑器设置:\", result.data.settings);\n            } else {\n                console.log(\"\\uD83D\\uDCDD 未找到保存的设置，使用默认设置\");\n            }\n        } catch (error) {\n            console.error(\"❌ 加载编辑器设置失败:\", error);\n        // 使用默认设置\n        }\n    };\n    // 🔧 智能文件内容更新事件监听 - 只响应外部工具更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fileTreeEventService = _services_fileTreeEventService__WEBPACK_IMPORTED_MODULE_5__.FileTreeEventService.getInstance();\n        const handleFileContentUpdate = async (updatedFileId)=>{\n            console.log(\"\\uD83D\\uDD14 收到文件内容更新事件:\", updatedFileId);\n            // 如果更新的文件是当前打开的文件，重新加载文件内容\n            if (editorState.currentFile && editorState.currentFile.id === updatedFileId) {\n                console.log(\"\\uD83D\\uDD04 当前文件内容已被外部工具更新，重新加载编辑器内容\");\n                try {\n                    const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n                    const result = await fileTreeService.getFile(updatedFileId);\n                    if (result.success && result.data) {\n                        const updatedEditorFile = {\n                            id: result.data.id,\n                            name: result.data.name,\n                            content: result.data.content,\n                            type: result.data.type,\n                            path: result.data.path,\n                            lastModified: result.data.updatedAt,\n                            isDirty: false,\n                            isReadOnly: false\n                        };\n                        setEditorState((prev)=>({\n                                ...prev,\n                                currentFile: updatedEditorFile,\n                                unsavedChanges: false\n                            }));\n                        console.log(\"✅ 编辑器内容已自动更新（外部工具）:\", result.data.name);\n                    }\n                } catch (error) {\n                    console.error(\"❌ 重新加载文件内容失败:\", error);\n                }\n            }\n        };\n        // 订阅文件内容更新事件\n        fileTreeEventService.subscribeFileContentUpdate(handleFileContentUpdate);\n        console.log(\"✅ 已订阅文件内容更新事件（仅外部工具）\");\n        return ()=>{\n            // 组件卸载时清理事件监听器\n            fileTreeEventService.unsubscribeFileContentUpdate(handleFileContentUpdate);\n            console.log(\"\\uD83E\\uDDF9 已清理文件内容更新事件监听器\");\n        };\n    }, [\n        (_editorState_currentFile1 = editorState.currentFile) === null || _editorState_currentFile1 === void 0 ? void 0 : _editorState_currentFile1.id\n    ]) // 依赖当前文件ID，确保监听器能正确判断\n    ;\n    // 加载作品数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadArtwork = async ()=>{\n            if (!artworkId) {\n                setEditorState((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"无效的作品ID\"\n                    }));\n                return;\n            }\n            try {\n                const artworkService = _services_artworkService__WEBPACK_IMPORTED_MODULE_3__.ArtworkService.getInstance();\n                const result = await artworkService.getArtwork(artworkId);\n                if (result.success && result.data) {\n                    setArtwork(result.data);\n                    setEditorState((prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: null\n                        }));\n                    // 加载保存的设置\n                    await loadSavedSettings();\n                    // 恢复字体应用状态（字体持久化功能）\n                    try {\n                        console.log(\"\\uD83D\\uDD04 尝试恢复字体应用状态...\");\n                        const { FontPersistenceService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontPersistenceService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontPersistenceService */ \"(app-pages-browser)/./src/services/fontPersistenceService.ts\"));\n                        const persistenceService = FontPersistenceService.getInstance();\n                        // 延迟一点时间确保DOM完全加载\n                        await new Promise((resolve)=>setTimeout(resolve, 300));\n                        const restoreResult = await persistenceService.restoreFontApplication();\n                        if (restoreResult.success && restoreResult.data) {\n                            console.log(\"✅ 字体应用状态已恢复\");\n                            // 获取当前应用的字体配置\n                            const configResult = await persistenceService.getActiveConfig();\n                            if (configResult.success && configResult.data) {\n                                const fontFamily = configResult.data.fontFamily;\n                                console.log(\"\\uD83C\\uDFAF 当前应用的字体:\", fontFamily);\n                                // 立即更新CSS变量\n                                document.documentElement.style.setProperty(\"--font-family-applied\", \"'\".concat(fontFamily, \"', sans-serif\"));\n                                document.documentElement.style.setProperty(\"--font-family-handwritten\", \"'\".concat(fontFamily, \"', cursive, var(--font-family-primary)\"));\n                                // 强制应用到所有元素\n                                const applyFontToAllElements = ()=>{\n                                    // 应用到body\n                                    document.body.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                    // 白名单方式：只对指定的元素应用用户字体\n                                    const whitelistSelectors = [\n                                        // 页面标题和导航\n                                        \"header h1, header h2, header p\",\n                                        \"nav, nav *\",\n                                        // 文件树面板\n                                        \".file-tree, .file-tree *\",\n                                        // AI助手面板\n                                        \".ai-assistant, .ai-assistant *\",\n                                        // 按钮和交互元素（非Monaco Editor内）\n                                        \".btn:not(.monaco-editor .btn)\",\n                                        \"button:not(.monaco-editor button)\",\n                                        // 特定的字体应用类\n                                        \".font-applied, .font-handwritten, .font-primary\",\n                                        \".handdrawn-text\",\n                                        // 表单元素（非编辑器内）\n                                        \"input:not(.monaco-editor input)\",\n                                        \"textarea:not(.monaco-editor textarea)\",\n                                        \"label:not(.monaco-editor label)\",\n                                        \"select:not(.monaco-editor select)\",\n                                        // 编辑器工具栏和设置面板\n                                        \".editor-toolbar, .editor-toolbar *\",\n                                        \".editor-settings, .editor-settings *\",\n                                        // 文件名和状态信息\n                                        \".file-name, .file-status\",\n                                        // 侧边栏内容\n                                        \".sidebar, .sidebar *\",\n                                        \".panel-header, .panel-content\"\n                                    ];\n                                    whitelistSelectors.forEach((selector)=>{\n                                        try {\n                                            const elements = document.querySelectorAll(selector);\n                                            elements.forEach((element)=>{\n                                                element.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                            });\n                                        } catch (e) {\n                                            // 忽略选择器错误\n                                            console.warn(\"字体应用选择器错误:\", selector, e);\n                                        }\n                                    });\n                                    // 强制重排\n                                    document.body.offsetHeight;\n                                };\n                                // 立即应用\n                                applyFontToAllElements();\n                                // 延迟再次应用，确保所有动态加载的元素也能应用字体\n                                setTimeout(applyFontToAllElements, 500);\n                                setTimeout(applyFontToAllElements, 1000);\n                            }\n                        } else {\n                            console.log(\"\\uD83D\\uDCDD 没有需要恢复的字体配置\");\n                        }\n                    } catch (fontError) {\n                        console.warn(\"⚠️ 字体恢复失败，但不影响编辑器启动:\", fontError);\n                    }\n                    // 预加载所有字体，确保字体立即可用\n                    try {\n                        const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                        const fontService = FontService.getInstance();\n                        await fontService.preloadAllFonts();\n                        console.log(\"✅ 所有字体已预加载\");\n                    } catch (error) {\n                        console.error(\"❌ 预加载字体失败:\", error);\n                    }\n                } else {\n                    setEditorState((prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: result.error || \"作品不存在\"\n                        }));\n                }\n            } catch (error) {\n                console.error(\"加载作品失败:\", error);\n                setEditorState((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"加载作品失败\"\n                    }));\n            }\n        };\n        loadArtwork();\n    }, [\n        artworkId\n    ]);\n    // 返回主页\n    const handleGoBack = ()=>{\n        if (editorState.unsavedChanges) {\n            const confirmed = window.confirm(\"您有未保存的更改，确定要离开吗？\");\n            if (!confirmed) return;\n        }\n        router.push(\"/\");\n    };\n    // 处理文件内容变化\n    const handleContentChange = async (content)=>{\n        var _editorState_currentFile;\n        if (!editorState.currentFile) return;\n        // 🔧 获取当前文件ID作为基准，防止文件切换时的竞态条件\n        const currentFileId = editorState.currentFile.id;\n        // 更新编辑器状态\n        const updatedFile = {\n            ...editorState.currentFile,\n            content,\n            isDirty: true,\n            lastModified: Date.now()\n        };\n        setEditorState((prev)=>({\n                ...prev,\n                currentFile: updatedFile,\n                unsavedChanges: true\n            }));\n        // 🔧 短暂延迟确保状态同步，然后验证文件ID一致性\n        await new Promise((resolve)=>setTimeout(resolve, 10));\n        // 🔧 验证文件ID一致性，防止文件切换过程中的错误保存\n        if (((_editorState_currentFile = editorState.currentFile) === null || _editorState_currentFile === void 0 ? void 0 : _editorState_currentFile.id) !== currentFileId) {\n            console.log(\"\\uD83D\\uDD12 文件已切换，跳过自动保存，避免内容错乱\");\n            return;\n        }\n        // 自动保存到数据库\n        try {\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            // 🔧 用户编辑时使用 'user' 来源，不会触发文件内容更新事件\n            await fileTreeService.updateFileContent(updatedFile.id, content, \"user\");\n            // 保存成功后更新状态\n            setEditorState((prev)=>({\n                    ...prev,\n                    currentFile: {\n                        ...updatedFile,\n                        isDirty: false\n                    },\n                    unsavedChanges: false\n                }));\n            console.log(\"✅ 文件自动保存成功\");\n        } catch (error) {\n            console.error(\"❌ 文件保存失败:\", error);\n        }\n    };\n    // 处理编辑器设置变化并持久化\n    const handleSettingsChange = async (newSettings)=>{\n        try {\n            // 更新状态\n            setEditorState((prev)=>({\n                    ...prev,\n                    settings: newSettings\n                }));\n            // 持久化设置到IndexedDB\n            const { DatabaseService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\"));\n            const dbService = DatabaseService.getInstance();\n            // 保存设置，使用artworkId作为键\n            await dbService.put(\"editor-settings\", {\n                id: artworkId,\n                settings: newSettings,\n                updatedAt: Date.now()\n            });\n            console.log(\"✅ 编辑器设置已保存\");\n        } catch (error) {\n            console.error(\"❌ 保存编辑器设置失败:\", error);\n        }\n    };\n    // 处理文件选择（从文件树）\n    const handleFileSelect = async (file)=>{\n        if (file.type !== \"file\") {\n            console.log(\"选择的不是文件，跳过:\", file.name);\n            return;\n        }\n        // 🔧 文件切换前强制保存当前文件\n        if (editorState.currentFile && editorState.currentFile.id !== file.id) {\n            console.log(\"\\uD83D\\uDCBE 文件切换前保存当前文件:\", editorState.currentFile.name);\n            try {\n                // 强制保存当前文件的内容\n                await handleContentChange(editorState.currentFile.content);\n                console.log(\"✅ 当前文件保存完成，开始切换\");\n            } catch (error) {\n                console.warn(\"⚠️ 当前文件保存失败，但继续切换:\", error);\n            }\n        }\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载文件:\", file.name);\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            const fileResult = await fileTreeService.getFile(file.id);\n            if (fileResult.success && fileResult.data) {\n                const storedFile = fileResult.data;\n                // 将StoredFile转换为EditorFile\n                const editorFile = {\n                    id: storedFile.id,\n                    name: storedFile.name,\n                    content: storedFile.content || \"\",\n                    type: storedFile.type,\n                    path: storedFile.path,\n                    lastModified: storedFile.updatedAt,\n                    isDirty: false,\n                    isReadOnly: false\n                };\n                // 更新编辑器状态\n                setEditorState((prev)=>({\n                        ...prev,\n                        currentFile: editorFile,\n                        unsavedChanges: false,\n                        error: null\n                    }));\n                console.log(\"✅ 文件选择成功:\", editorFile.name);\n            } else {\n                console.error(\"❌ 文件加载失败:\", fileResult.error);\n                setEditorState((prev)=>({\n                        ...prev,\n                        error: fileResult.error || \"文件加载失败\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"❌ 文件选择异常:\", error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"文件选择时发生错误\"\n                }));\n        }\n    };\n    // 根据文件ID查找文件节点的辅助函数\n    const findFileById = (rootNode, targetId)=>{\n        if (rootNode.id === targetId) {\n            return rootNode;\n        }\n        if (rootNode.children) {\n            for (const child of rootNode.children){\n                const found = findFileById(child, targetId);\n                if (found) {\n                    return found;\n                }\n            }\n        }\n        return null;\n    };\n    // 根据目录名查找目录节点的辅助函数\n    const findDirectoryByName = (rootNode, dirName, parentId)=>{\n        // 递归搜索函数\n        const searchNode = (node)=>{\n            // 检查是否是目标目录\n            if (node.type === \"folder\" && node.name === dirName && node.parentId === parentId) {\n                return node;\n            }\n            // 递归检查子节点\n            if (node.children && Array.isArray(node.children)) {\n                for (const child of node.children){\n                    const found = searchNode(child);\n                    if (found) return found;\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 根据文件路径查找文件节点的辅助函数\n    const findFileByPath = (rootNode, targetPath)=>{\n        // 增强的路径标准化函数\n        const normalizePath = (path)=>{\n            if (!path) return \"\";\n            // 移除开头的斜杠，统一分隔符，但保持大小写（中文路径敏感）\n            return path.replace(/^\\/+/, \"\").replace(/\\\\/g, \"/\").replace(/\\/+/g, \"/\");\n        };\n        const normalizedTarget = normalizePath(targetPath);\n        // 递归搜索函数\n        const searchNode = (node)=>{\n            const normalizedNodePath = normalizePath(node.path || \"\");\n            const normalizedNodeName = normalizePath(node.name || \"\");\n            console.log(\"\\uD83D\\uDD0D 编辑器页面查找文件:\", {\n                targetPath,\n                normalizedTarget,\n                nodeName: node.name,\n                nodePath: node.path,\n                normalizedNodePath,\n                normalizedNodeName,\n                nodeType: node.type\n            });\n            // 🔧 排除根目录：根目录不应该被匹配为文件\n            if (node.name === \"root\" || node.path === \"/\" || normalizedNodePath === \"\") {\n                console.log(\"⏭️ 跳过根目录，继续搜索子节点\");\n                // 直接搜索子节点，不匹配根目录本身\n                if (node.children && Array.isArray(node.children)) {\n                    for (const child of node.children){\n                        const found = searchNode(child);\n                        if (found) return found;\n                    }\n                }\n                return null;\n            }\n            // 🔧 只匹配文件类型的节点，排除文件夹\n            const isFile = node.type === \"file\" || !node.children || node.children.length === 0;\n            // 1. 精确路径匹配（仅限文件）\n            if (isFile && normalizedNodePath === normalizedTarget) {\n                console.log(\"✅ 编辑器页面精确路径匹配（文件）:\", normalizedNodePath);\n                return node;\n            }\n            // 2. 文件名匹配（仅限文件）\n            if (isFile && normalizedNodeName === normalizedTarget) {\n                console.log(\"✅ 编辑器页面文件名匹配（文件）:\", normalizedNodeName);\n                return node;\n            }\n            // 3. 路径末尾匹配（仅限文件）\n            const targetParts = normalizedTarget.split(\"/\").filter((p)=>p);\n            const nodeParts = normalizedNodePath.split(\"/\").filter((p)=>p);\n            // 检查目标路径是否是节点路径的后缀\n            if (isFile && targetParts.length <= nodeParts.length) {\n                const nodePathSuffix = nodeParts.slice(-targetParts.length).join(\"/\");\n                if (nodePathSuffix === normalizedTarget) {\n                    console.log(\"✅ 编辑器页面路径后缀匹配（文件）:\", {\n                        nodePathSuffix,\n                        normalizedTarget\n                    });\n                    return node;\n                }\n            }\n            // 4. 文件名部分匹配（仅限文件）\n            const targetFileName = targetParts[targetParts.length - 1];\n            if (isFile && targetFileName && normalizedNodeName === targetFileName) {\n                console.log(\"✅ 编辑器页面文件名部分匹配（文件）:\", {\n                    targetFileName,\n                    normalizedNodeName\n                });\n                return node;\n            }\n            // 5. 模糊匹配（仅限文件）\n            if (isFile && (normalizedNodePath.includes(normalizedTarget) || normalizedTarget.includes(normalizedNodePath))) {\n                console.log(\"✅ 编辑器页面模糊路径匹配（文件）:\", {\n                    normalizedNodePath,\n                    normalizedTarget\n                });\n                return node;\n            }\n            // 递归检查子节点\n            if (node.children && Array.isArray(node.children)) {\n                for (const child of node.children){\n                    const found = searchNode(child);\n                    if (found) return found;\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 处理文件选择（通过文件ID，用于DF工具跳转）\n    const handleFileSelectById = async (fileId)=>{\n        // 🔧 文件切换前强制保存当前文件\n        if (editorState.currentFile && editorState.currentFile.id !== fileId) {\n            console.log(\"\\uD83D\\uDCBE 文件切换前保存当前文件:\", editorState.currentFile.name);\n            try {\n                await handleContentChange(editorState.currentFile.content);\n                console.log(\"✅ 当前文件保存完成，开始切换\");\n            } catch (error) {\n                console.warn(\"⚠️ 当前文件保存失败，但继续切换:\", error);\n            }\n        }\n        try {\n            console.log(\"\\uD83D\\uDD0D 根据ID查找文件:\", fileId);\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const file = findFileById(fileTreeResult.data, fileId);\n                if (file) {\n                    await handleFileSelect(file);\n                    console.log(\"✅ 通过ID选择文件成功:\", file.name);\n                } else {\n                    console.warn(\"⚠️ 未找到指定ID的文件:\", fileId);\n                    setEditorState((prev)=>({\n                            ...prev,\n                            error: \"未找到指定的文件\"\n                        }));\n                }\n            } else {\n                console.error(\"❌ 获取文件树失败:\", fileTreeResult.error);\n                setEditorState((prev)=>({\n                        ...prev,\n                        error: \"获取文件树失败\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"❌ 通过ID选择文件异常:\", error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"文件选择时发生错误\"\n                }));\n        }\n    };\n    // 处理AI内容插入到编辑器\n    const handleContentInsert = async (content, options)=>{\n        if (!editorState.currentFile) {\n            console.warn(\"没有打开的文件，无法插入内容\");\n            return;\n        }\n        try {\n            const currentContent = editorState.currentFile.content;\n            let newContent = \"\";\n            // 根据插入选项处理内容\n            switch(options.position){\n                case \"start\":\n                    newContent = content + (options.addNewlines ? \"\\n\" : \"\") + currentContent;\n                    break;\n                case \"end\":\n                    newContent = currentContent + (options.addNewlines ? \"\\n\" : \"\") + content;\n                    break;\n                case \"replace\":\n                    newContent = content;\n                    break;\n                case \"cursor\":\n                default:\n                    // 对于光标位置，我们暂时插入到文件末尾\n                    // 在实际实现中，这里需要与Monaco编辑器集成获取光标位置\n                    newContent = currentContent + (options.addNewlines ? \"\\n\" : \"\") + content;\n                    break;\n            }\n            // 更新文件内容\n            await handleContentChange(newContent);\n            console.log(\"✅ AI内容已插入到编辑器\");\n        } catch (error) {\n            console.error(\"❌ 插入内容失败:\", error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"插入内容失败\"\n                }));\n        }\n    };\n    // EditorPanel的ref\n    const editorPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        console.log(\"\\uD83D\\uDD17 编辑页面收到详细对比请求:\", diffRequest);\n        try {\n            // 1. 首先确保目标文件已打开\n            if (!editorState.currentFile || editorState.currentFile.path !== diffRequest.filePath) {\n                console.log(\"\\uD83D\\uDD04 切换到目标文件:\", diffRequest.filePath);\n                // 根据文件路径查找并打开文件\n                const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n                const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n                if (fileTreeResult.success && fileTreeResult.data) {\n                    const targetFile = findFileByPath(fileTreeResult.data, diffRequest.filePath);\n                    if (targetFile) {\n                        await handleFileSelect(targetFile);\n                        // 等待文件加载完成\n                        await new Promise((resolve)=>setTimeout(resolve, 100));\n                    } else {\n                        console.warn(\"⚠️ 未找到目标文件:\", diffRequest.filePath);\n                        // 尝试创建文件\n                        if (diffRequest.operation === \"replace\" || diffRequest.operation === \"append\") {\n                            console.log(\"\\uD83D\\uDD04 尝试创建文件:\", diffRequest.filePath);\n                            // 解析文件路径，获取目录和文件名\n                            const pathParts = diffRequest.filePath.split(\"/\");\n                            const fileName = pathParts.pop() || \"\";\n                            const dirPath = pathParts.join(\"/\");\n                            try {\n                                // 首先确保目录存在\n                                let parentId = \"\".concat(artworkId, \"-root\") // 默认根目录\n                                ;\n                                // 如果有目录路径，需要创建或查找目录\n                                if (dirPath) {\n                                    const dirParts = dirPath.split(\"/\").filter((p)=>p);\n                                    let currentParentId = \"\".concat(artworkId, \"-root\");\n                                    // 逐级创建或查找目录\n                                    for (const dirName of dirParts){\n                                        console.log(\"\\uD83D\\uDD0D 查找或创建目录:\", dirName);\n                                        // 重新获取文件树，查找是否已存在该目录\n                                        const updatedTreeResult = await fileTreeService.getFileTree(artworkId);\n                                        if (updatedTreeResult.success && updatedTreeResult.data) {\n                                            const existingDir = findDirectoryByName(updatedTreeResult.data, dirName, currentParentId);\n                                            if (existingDir) {\n                                                console.log(\"✅ 找到已存在的目录:\", dirName);\n                                                currentParentId = existingDir.id;\n                                            } else {\n                                                console.log(\"\\uD83D\\uDD04 创建新目录:\", dirName);\n                                                const createDirResult = await fileTreeService.createFolder(artworkId, currentParentId, dirName);\n                                                if (createDirResult.success && createDirResult.data) {\n                                                    currentParentId = createDirResult.data.id;\n                                                    console.log(\"✅ 目录创建成功:\", dirName);\n                                                } else {\n                                                    throw new Error(\"创建目录失败: \".concat(createDirResult.error));\n                                                }\n                                            }\n                                        }\n                                    }\n                                    parentId = currentParentId;\n                                }\n                                // 创建文件\n                                console.log(\"\\uD83D\\uDD04 创建文件:\", {\n                                    fileName,\n                                    parentId\n                                });\n                                const createResult = await fileTreeService.createFile(artworkId, parentId, fileName, \"text\", \"\");\n                                if (createResult.success && createResult.data) {\n                                    console.log(\"✅ 文件创建成功:\", createResult.data);\n                                    // 重新加载文件树并选择新创建的文件\n                                    const updatedTreeResult = await fileTreeService.getFileTree(artworkId);\n                                    if (updatedTreeResult.success && updatedTreeResult.data) {\n                                        const newFile = findFileByPath(updatedTreeResult.data, diffRequest.filePath);\n                                        if (newFile) {\n                                            await handleFileSelect(newFile);\n                                            await new Promise((resolve)=>setTimeout(resolve, 300));\n                                        }\n                                    }\n                                } else {\n                                    console.error(\"❌ 文件创建失败:\", createResult.error);\n                                    return;\n                                }\n                            } catch (createError) {\n                                console.error(\"❌ 文件创建异常:\", createError);\n                                return;\n                            }\n                        } else {\n                            return;\n                        }\n                    }\n                }\n            }\n            // 2. 调用EditorPanel的diff方法\n            if (editorPanelRef.current && editorPanelRef.current.handleOpenDetailedDiff) {\n                console.log(\"✅ 调用EditorPanel的diff方法\");\n                editorPanelRef.current.handleOpenDetailedDiff(diffRequest);\n            } else {\n                console.warn(\"⚠️ EditorPanel的diff方法不可用\");\n            }\n        } catch (error) {\n            console.error(\"❌ 打开详细差异对比失败:\", error);\n        }\n    };\n    // 处理创建文件/文件夹\n    const handleCreateFile = async (type)=>{\n        try {\n            var _existingFiles_data;\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            // 在根目录下创建文件/文件夹\n            const rootFolderId = \"\".concat(artworkId, \"-root\");\n            // 生成唯一名称，避免重名冲突\n            const baseName = type === \"file\" ? \"新文件\" : \"新文件夹\";\n            const extension = type === \"file\" ? \".md\" : \"\";\n            let finalName = \"\".concat(baseName).concat(extension);\n            let counter = 1;\n            // 检查是否存在同名文件，如果存在则添加数字后缀\n            const existingFiles = await fileTreeService.getFileTree(artworkId);\n            if (existingFiles.success && ((_existingFiles_data = existingFiles.data) === null || _existingFiles_data === void 0 ? void 0 : _existingFiles_data.children)) {\n                const existingNames = new Set(existingFiles.data.children.map((child)=>child.name));\n                while(existingNames.has(finalName)){\n                    finalName = \"\".concat(baseName).concat(counter).concat(extension);\n                    counter++;\n                }\n            }\n            if (type === \"file\") {\n                await fileTreeService.createFile(artworkId, rootFolderId, finalName, \"text\");\n            } else {\n                await fileTreeService.createFolder(artworkId, rootFolderId, finalName);\n            }\n            console.log(\"✅ 成功在根目录创建\".concat(type, \":\"), finalName);\n        // 文件树会通过事件自动刷新，不再需要手动触发\n        } catch (error) {\n            console.error(\"❌ 创建\".concat(type, \"失败:\"), error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"创建\".concat(type, \"失败: \").concat(error instanceof Error ? error.message : String(error))\n                }));\n        }\n    };\n    // 加载状态\n    if (editorState.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 text-amber-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 918,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-handwritten\",\n                        children: \"加载编辑器中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 919,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 917,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 916,\n            columnNumber: 7\n        }, this);\n    }\n    // 错误状态\n    if (editorState.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 mx-auto mb-6 rounded-lg bg-red-500/20 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"32\",\n                            height: \"32\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"none\",\n                            className: \"text-red-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                    x1: \"15\",\n                                    y1: \"9\",\n                                    x2: \"9\",\n                                    y2: \"15\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                    x1: \"9\",\n                                    y1: \"9\",\n                                    x2: \"15\",\n                                    y2: \"15\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-400 text-xl mb-6 font-handwritten\",\n                        children: editorState.error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 937,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleGoBack,\n                        className: \"px-6 py-3 bg-amber-500/20 hover:bg-amber-500/30 text-amber-400 hover:text-amber-300 border border-amber-500/50 rounded-lg transition-all duration-200 font-handwritten\",\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 940,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 929,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 928,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-gray-900 to-gray-800 backdrop-blur-sm border-b border-amber-500/30 px-6 py-4 shadow-lg shadow-black/20 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"p-2 rounded-lg hover:bg-amber-500/20 transition-colors text-amber-400 hover:text-amber-300\",\n                                    title: \"返回主页\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"20\",\n                                        height: \"20\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-amber-100 font-handwritten\",\n                                            children: (artwork === null || artwork === void 0 ? void 0 : artwork.title) || \"编辑作品\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 967,\n                                            columnNumber: 15\n                                        }, this),\n                                        (artwork === null || artwork === void 0 ? void 0 : artwork.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300 mt-1\",\n                                            children: artwork.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 971,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                editorState.unsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-amber-600 font-medium\",\n                                    children: \"未保存的更改\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 980,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-400 rounded-full\",\n                                    title: \"已连接\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 978,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 955,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 954,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"h-[calc(100vh-80px)] overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResizableLayout__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    leftPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-amber-500/30 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-amber-100 font-handwritten\",\n                                            children: \"文件管理\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-1.5 rounded-md bg-amber-500/20 hover:bg-amber-500/30 transition-colors group\",\n                                                    title: \"新建文件\",\n                                                    onClick: ()=>handleCreateFile(\"file\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"14\",\n                                                        height: \"14\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        className: \"text-amber-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1006,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"14,2 14,8 20,8\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"12\",\n                                                                y1: \"18\",\n                                                                x2: \"12\",\n                                                                y2: \"12\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1008,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"9\",\n                                                                y1: \"15\",\n                                                                x2: \"15\",\n                                                                y2: \"15\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1009,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 1000,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-1.5 rounded-md bg-amber-500/20 hover:bg-amber-500/30 transition-colors group\",\n                                                    title: \"新建文件夹\",\n                                                    onClick: ()=>handleCreateFile(\"folder\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"14\",\n                                                        height: \"14\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        className: \"text-amber-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1018,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"12\",\n                                                                y1: \"14\",\n                                                                x2: \"12\",\n                                                                y2: \"10\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1019,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"10\",\n                                                                y1: \"12\",\n                                                                x2: \"14\",\n                                                                y2: \"12\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1020,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 1012,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 994,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileTreePanel__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    artworkId: artworkId,\n                                    currentFileId: (_editorState_currentFile2 = editorState.currentFile) === null || _editorState_currentFile2 === void 0 ? void 0 : _editorState_currentFile2.id,\n                                    onFileSelect: handleFileSelect,\n                                    onFileCreate: (parentId, name, type)=>{\n                                        console.log(\"创建文件:\", {\n                                            parentId,\n                                            name,\n                                            type\n                                        });\n                                    },\n                                    onFileDelete: (fileId)=>{\n                                        console.log(\"删除文件:\", fileId);\n                                    },\n                                    onFileRename: (fileId, newName)=>{\n                                        console.log(\"重命名文件:\", {\n                                            fileId,\n                                            newName\n                                        });\n                                    },\n                                    className: \"h-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 1026,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 993,\n                        columnNumber: 13\n                    }, void 0),\n                    centerPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EditorPanel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ref: editorPanelRef,\n                        file: editorState.currentFile,\n                        onContentChange: handleContentChange,\n                        onSettingsChange: handleSettingsChange,\n                        onFileRename: async (fileId, newName)=>{\n                            try {\n                                const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n                                await fileTreeService.renameFile(fileId, newName);\n                                // 更新当前文件状态\n                                if (editorState.currentFile && editorState.currentFile.id === fileId) {\n                                    setEditorState((prev)=>({\n                                            ...prev,\n                                            currentFile: {\n                                                ...prev.currentFile,\n                                                name: newName\n                                            }\n                                        }));\n                                }\n                                // 文件树会通过事件自动刷新，不再需要手动触发\n                                console.log(\"✅ 文件重命名成功:\", newName);\n                            } catch (error) {\n                                console.error(\"❌ 文件重命名失败:\", error);\n                                setEditorState((prev)=>({\n                                        ...prev,\n                                        error: \"文件重命名失败\"\n                                    }));\n                            }\n                        },\n                        settings: editorState.settings,\n                        onAutoAssociationToggle: handleAutoAssociationToggle,\n                        autoAssociationEnabled: autoAssociationEnabled,\n                        artworkId: artworkId,\n                        onOpenDetailedDiff: handleOpenDetailedDiff,\n                        className: \"h-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 1046,\n                        columnNumber: 13\n                    }, void 0),\n                    rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIAssistant__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        onContentInsert: handleContentInsert,\n                        artworkId: artworkId,\n                        onFileSelect: handleFileSelectById,\n                        onOpenDetailedDiff: handleOpenDetailedDiff,\n                        className: \"h-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 1087,\n                        columnNumber: 13\n                    }, void 0),\n                    initialLeftWidth: 20,\n                    initialRightWidth: 30,\n                    minLeftWidth: 15,\n                    minRightWidth: 20,\n                    minCenterWidth: 30,\n                    onLayoutChange: (leftWidth, rightWidth, leftCollapsed, rightCollapsed)=>{\n                        var _editorPanelRef_current;\n                        console.log(\"布局变化:\", {\n                            leftWidth,\n                            rightWidth,\n                            leftCollapsed,\n                            rightCollapsed\n                        });\n                        // 通知 EditorPanel 处理布局变化\n                        if ((_editorPanelRef_current = editorPanelRef.current) === null || _editorPanelRef_current === void 0 ? void 0 : _editorPanelRef_current.handleLayoutChange) {\n                            editorPanelRef.current.handleLayoutChange();\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 991,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 990,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n        lineNumber: 952,\n        columnNumber: 5\n    }, this);\n}\n_s(ArtworkEditorPage, \"pMbrCOGRuoRcZjaeYkmxq1lVqjI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ArtworkEditorPage;\nvar _c;\n$RefreshReg$(_c, \"ArtworkEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/artwork/[id]/edit/page.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts":
/*!*****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/StyleManager.ts ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StyleManager: function() { return /* binding */ StyleManager; }\n/* harmony export */ });\n/**\r\n * StyleManager - 样式管理器\r\n * 负责SimpleMindMap主题和样式的统一管理\r\n * \r\n * 职责：\r\n * - 主题切换和配置\r\n * - 自定义样式应用\r\n * - 样式状态管理\r\n * \r\n * 设计原则：\r\n * - 官方API优先：使用SimpleMindMap官方主题API\r\n * - 简洁高效：最小化样式配置，避免过度自定义\r\n */ class StyleManager {\n    /**\r\n   * 初始化样式管理器\r\n   */ async initialize() {\n        try {\n            // 应用初始主题\n            await this.applyTheme(this.currentTheme);\n            this.isInitialized = true;\n            console.log(\"✅ 样式管理器初始化完成，主题:\", this.currentTheme);\n        } catch (error) {\n            console.error(\"❌ 样式管理器初始化失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 应用主题\r\n   */ async applyTheme(theme) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not available\");\n        }\n        try {\n            // 直接使用我们的自定义主题配置，不依赖SimpleMindMap内置主题\n            const themeConfig = this.getThemeConfig(theme);\n            this.mindMapInstance.setThemeConfig(themeConfig, false);\n            this.currentTheme = theme;\n            console.log(\"✅ 自定义主题应用成功: \".concat(theme), themeConfig);\n        } catch (error) {\n            console.error(\"❌ 主题应用失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取主题配置\r\n   */ getThemeConfig(theme) {\n        switch(theme){\n            case \"dark\":\n                return this.getDarkThemeConfig();\n            case \"light\":\n                return this.getLightThemeConfig();\n            default:\n                return this.getDarkThemeConfig();\n        }\n    }\n    /**\r\n   * 获取深色主题配置 - 黑金配色\r\n   */ getDarkThemeConfig() {\n        return {\n            // 背景配置 - 深黑色\n            backgroundColor: \"#0a0a0a\",\n            backgroundImage: \"\",\n            // 连线配置 - 金色\n            lineColor: \"#FFD700\",\n            lineWidth: 2,\n            lineStyle: \"solid\",\n            lineDasharray: \"none\",\n            lineOpacity: 0.9,\n            // 根节点样式 - 黑金主题\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#1a1a1a\",\n                borderColor: \"#FFD700\",\n                borderWidth: 3,\n                borderRadius: 8,\n                fontSize: 20,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 700,\n                color: \"#FFD700\",\n                padding: [\n                    15,\n                    20\n                ],\n                margin: [\n                    0,\n                    0,\n                    0,\n                    0\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 二级节点样式 - 渐变金色\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#2a2a2a\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 6,\n                fontSize: 17,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#FFA500\",\n                padding: [\n                    10,\n                    15\n                ],\n                margin: [\n                    5,\n                    5,\n                    5,\n                    5\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 三级及以下节点样式 - 浅金色\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#1e1e1e\",\n                borderColor: \"#DAA520\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 15,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 500,\n                color: \"#DAA520\",\n                padding: [\n                    8,\n                    12\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 1,\n                textDecoration: \"none\",\n                fontStyle: \"normal\"\n            },\n            // 概要节点样式 - 金色高亮\n            generalization: {\n                shape: \"rectangle\",\n                fillColor: \"#FFD700\",\n                borderColor: \"#FFA500\",\n                borderWidth: 2,\n                borderRadius: 4,\n                fontSize: 13,\n                fontFamily: 'var(--font-family-handwritten, \"Microsoft YaHei\", \"PingFang SC\", sans-serif)',\n                fontWeight: 600,\n                color: \"#000000\",\n                padding: [\n                    6,\n                    10\n                ],\n                margin: [\n                    3,\n                    3,\n                    3,\n                    3\n                ],\n                opacity: 0.95\n            },\n            // 展开按钮样式 - 金色\n            expandBtnSize: 16,\n            expandBtnStyle: {\n                color: \"#FFD700\",\n                fillColor: \"#0a0a0a\",\n                strokeColor: \"#FFD700\",\n                strokeWidth: 2,\n                radius: 8\n            },\n            // 连接点样式 - 金色系\n            associativeLine: {\n                strokeColor: \"#DAA520\",\n                strokeWidth: 2,\n                strokeDasharray: \"5,5\"\n            },\n            // 激活状态样式 - 亮金色\n            activeNodeStyle: {\n                strokeColor: \"#FFFF00\",\n                strokeWidth: 4,\n                fillColor: \"rgba(255, 215, 0, 0.15)\"\n            },\n            // 悬停状态样式 - 金色光晕\n            hoverNodeStyle: {\n                fillColor: \"rgba(255, 215, 0, 0.1)\",\n                strokeColor: \"#FFA500\",\n                strokeWidth: 2\n            }\n        };\n    }\n    /**\r\n   * 获取浅色主题配置\r\n   */ getLightThemeConfig() {\n        return {\n            backgroundColor: \"#ffffff\",\n            backgroundImage: \"\",\n            lineColor: \"#666666\",\n            lineWidth: 1,\n            root: {\n                shape: \"rectangle\",\n                fillColor: \"#f3f4f6\",\n                borderColor: \"#374151\",\n                borderWidth: 2,\n                borderRadius: 8,\n                fontSize: 18,\n                fontWeight: 600,\n                color: \"#111827\",\n                padding: [\n                    12,\n                    16\n                ]\n            },\n            second: {\n                shape: \"rectangle\",\n                fillColor: \"#f9fafb\",\n                borderColor: \"#6b7280\",\n                borderWidth: 1,\n                borderRadius: 6,\n                fontSize: 16,\n                fontWeight: 500,\n                color: \"#374151\",\n                padding: [\n                    8,\n                    12\n                ]\n            },\n            node: {\n                shape: \"rectangle\",\n                fillColor: \"#ffffff\",\n                borderColor: \"#d1d5db\",\n                borderWidth: 1,\n                borderRadius: 4,\n                fontSize: 14,\n                fontWeight: 400,\n                color: \"#4b5563\",\n                padding: [\n                    6,\n                    10\n                ]\n            }\n        };\n    }\n    /**\r\n   * 更新主题\r\n   */ async updateTheme(theme) {\n        if (theme === this.currentTheme) return;\n        await this.applyTheme(theme);\n    }\n    /**\r\n   * 获取当前主题\r\n   */ getCurrentTheme() {\n        return this.currentTheme;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized;\n    }\n    /**\r\n   * 销毁样式管理器\r\n   */ destroy() {\n        this.isInitialized = false;\n        console.log(\"✅ 样式管理器销毁完成\");\n    }\n    constructor(mindMapInstance, theme = \"dark\"){\n        this.isInitialized = false;\n        this.mindMapInstance = mindMapInstance;\n        this.currentTheme = theme;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _SaveStatusIndicator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SaveStatusIndicator */ \"(app-pages-browser)/./src/components/EditorPanel/SaveStatusIndicator.tsx\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 SaveManager 集成 - 替换原有的定时器管理\n    const saveManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(_services_SaveManager__WEBPACK_IMPORTED_MODULE_9__.SaveManager.getInstance());\n    const [saveState, setSaveState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 SaveManager 状态变更回调\n    const handleSaveStateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId, state, error)=>{\n        // 只处理当前文件的状态变更\n        if ((file === null || file === void 0 ? void 0 : file.id) === fileId) {\n            setSaveState(state);\n            setSaveError(error);\n            if (error) {\n                console.error(\"\\uD83D\\uDCBE 保存状态错误:\", error);\n            }\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 保存重试处理\n    const handleSaveRetry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(file === null || file === void 0 ? void 0 : file.id)) return;\n        const saveManager = saveManagerRef.current;\n        try {\n            const success = await saveManager.retryFileSave(file.id);\n            if (success) {\n                console.log(\"✅ 保存重试成功:\", file.id);\n            } else {\n                console.warn(\"⚠️ 保存重试失败:\", file.id);\n            }\n        } catch (error) {\n            console.error(\"❌ 保存重试异常:\", error);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 初始化 SaveManager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        // 设置保存执行回调\n        saveManager.setSaveExecuteCallback(async (fileId, data, options)=>{\n            if (onContentChange && (file === null || file === void 0 ? void 0 : file.id) === fileId) {\n                try {\n                    onContentChange(data);\n                    return true;\n                } catch (error) {\n                    console.error(\"❌ 保存执行失败:\", error);\n                    return false;\n                }\n            }\n            return false;\n        });\n        // 添加状态变更监听器\n        saveManager.addStateChangeListener(handleSaveStateChange);\n        return ()=>{\n            // 清理监听器\n            saveManager.removeStateChangeListener(handleSaveStateChange);\n        };\n    }, [\n        onContentChange,\n        file === null || file === void 0 ? void 0 : file.id,\n        handleSaveStateChange\n    ]);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        const previousFileId = saveManager.getCurrentFileId();\n        const currentFileId = (file === null || file === void 0 ? void 0 : file.id) || null;\n        // 🔧 文件切换时使用 SaveManager 处理\n        if (previousFileId !== currentFileId) {\n            saveManager.switchFile(previousFileId, currentFileId || \"\").then(()=>{\n                console.log(\"\\uD83D\\uDD04 文件切换完成:\", previousFileId, \"->\", currentFileId);\n            }).catch((error)=>{\n                console.error(\"❌ 文件切换失败:\", error);\n            });\n        }\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        console.log(\"\\uD83D\\uDD04 开始转换思维导图数据到Markdown:\", data);\n        if (!data || !data.data || !data.data.text) {\n            console.error(\"❌ 思维导图数据无效:\", data);\n            return \"# 思维导图\\n\\n内容为空\";\n        }\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            if (!node || !node.data || !node.data.text) {\n                console.warn(\"⚠️ 跳过无效节点:\", node);\n                return \"\";\n            }\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        const markdownResult = convertNode(data).trim();\n        console.log(\"✅ 思维导图转换完成，结果长度:\", markdownResult.length, \"内容预览:\", markdownResult.substring(0, 100));\n        return markdownResult || \"# 思维导图\\n\\n内容为空\";\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数 - 使用 SaveManager\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(content) {\n        let saveType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"editor\";\n        if (!settings.autoSave || !(file === null || file === void 0 ? void 0 : file.id)) return;\n        const saveManager = saveManagerRef.current;\n        saveManager.scheduleAutoSave(file.id, content, {\n            debounceDelay: settings.autoSaveDelay,\n            saveType,\n            immediate: false\n        });\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更 - 使用 SaveManager\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", {\n            hasData: !!data,\n            dataType: typeof data,\n            fileId: file === null || file === void 0 ? void 0 : file.id,\n            isMindMapMode,\n            dataPreview: data ? JSON.stringify(data).substring(0, 200) : \"null\"\n        });\n        // 基础状态验证\n        if (!(file === null || file === void 0 ? void 0 : file.id) || !isMindMapMode || !data) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\", {\n                fileId: file === null || file === void 0 ? void 0 : file.id,\n                isMindMapMode,\n                hasData: !!data\n            });\n            return;\n        }\n        // 将思维导图数据转换为Markdown格式并保存\n        try {\n            const markdownContent = convertMindMapToMarkdown(data);\n            console.log(\"\\uD83D\\uDCBE 准备保存转换后的内容:\", {\n                fileId: file.id,\n                contentLength: markdownContent.length,\n                contentPreview: markdownContent.substring(0, 100)\n            });\n            handleSave(markdownContent, \"mindmap\");\n            console.log(\"✅ 思维导图数据已转换为Markdown并调度保存\");\n        } catch (error) {\n            console.error(\"❌ 思维导图数据转换失败:\", error);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理表格集成和资源\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, []);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 723,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 718,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 717,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 779,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 736,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SaveStatusIndicator__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        saveState: saveState,\n                                        error: saveError,\n                                        onRetry: handleSaveRetry\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 788,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 926,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 911,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 931,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 784,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 734,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 948,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 970,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 966,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 986,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 999,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1017,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1028,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1016,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1034,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1037,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1033,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1051,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1050,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 946,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 945,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1076,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1080,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1075,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1074,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1086,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1087,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1089,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1085,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1084,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1098,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1072,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1113,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1112,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1111,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1110,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1122,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1123,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1133,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1125,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1141,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1121,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1120,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    fileId: file === null || file === void 0 ? void 0 : file.id,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1151,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1169,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1170,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1171,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1168,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1167,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1108,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1189,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1190,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1192,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1187,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1186,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1178,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 732,\n        columnNumber: 5\n    }, undefined);\n}, \"yI+jkYuw0q9xdoIt2vRx4438wso=\")), \"yI+jkYuw0q9xdoIt2vRx4438wso=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/* harmony import */ var _services_SaveManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/SaveManager */ \"(app-pages-browser)/./src/services/SaveManager.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 SaveManager 集成 - 替换原有的定时器管理\n    const saveManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(_services_SaveManager__WEBPACK_IMPORTED_MODULE_9__.SaveManager.getInstance());\n    const [saveState, setSaveState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 SaveManager 状态变更回调\n    const handleSaveStateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId, state, error)=>{\n        // 只处理当前文件的状态变更\n        if ((file === null || file === void 0 ? void 0 : file.id) === fileId) {\n            setSaveState(state);\n            if (error) {\n                console.error(\"\\uD83D\\uDCBE 保存状态错误:\", error);\n            }\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 🔧 初始化 SaveManager\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        // 设置保存执行回调\n        saveManager.setSaveExecuteCallback(async (fileId, data, options)=>{\n            if (onContentChange && (file === null || file === void 0 ? void 0 : file.id) === fileId) {\n                try {\n                    onContentChange(data);\n                    return true;\n                } catch (error) {\n                    console.error(\"❌ 保存执行失败:\", error);\n                    return false;\n                }\n            }\n            return false;\n        });\n        // 添加状态变更监听器\n        saveManager.addStateChangeListener(handleSaveStateChange);\n        return ()=>{\n            // 清理监听器\n            saveManager.removeStateChangeListener(handleSaveStateChange);\n        };\n    }, [\n        onContentChange,\n        file === null || file === void 0 ? void 0 : file.id,\n        handleSaveStateChange\n    ]);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const saveManager = saveManagerRef.current;\n        const previousFileId = saveManager.getCurrentFileId();\n        const currentFileId = (file === null || file === void 0 ? void 0 : file.id) || null;\n        // 🔧 文件切换时使用 SaveManager 处理\n        if (previousFileId !== currentFileId) {\n            saveManager.switchFile(previousFileId, currentFileId || \"\").then(()=>{\n                console.log(\"\\uD83D\\uDD04 文件切换完成:\", previousFileId, \"->\", currentFileId);\n            }).catch((error)=>{\n                console.error(\"❌ 文件切换失败:\", error);\n            });\n        }\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数 - 使用 SaveManager\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(content) {\n        let saveType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"editor\";\n        if (!settings.autoSave || !(file === null || file === void 0 ? void 0 : file.id)) return;\n        const saveManager = saveManagerRef.current;\n        saveManager.scheduleAutoSave(file.id, content, {\n            debounceDelay: settings.autoSaveDelay,\n            saveType,\n            immediate: false\n        });\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        file === null || file === void 0 ? void 0 : file.id\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更 - 使用 SaveManager\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 基础状态验证\n        if (!(file === null || file === void 0 ? void 0 : file.id) || !isMindMapMode || !data) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 将思维导图数据转换为Markdown格式并保存\n        try {\n            const markdownContent = convertMindMapToMarkdown(data);\n            handleSave(markdownContent, \"mindmap\");\n            console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并调度保存 (文件ID:\", file.id, \")\");\n        } catch (error) {\n            console.error(\"❌ 思维导图数据转换失败:\", error);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        isMindMapMode,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理表格集成和资源\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, []);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 675,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 670,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 669,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    saveState !== _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.idle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 px-2 py-1 rounded text-xs\",\n                                        children: [\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.pending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-yellow-500 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400\",\n                                                        children: \"待保存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true),\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-blue-500 animate-spin border border-blue-300 border-t-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400\",\n                                                        children: \"保存中\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true),\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.saved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: \"已保存\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true),\n                                            saveState === _types__WEBPACK_IMPORTED_MODULE_10__.SaveState.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full bg-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-400\",\n                                                        children: \"保存失败\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 785,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 901,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 895,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 906,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 736,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 942,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 945,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 941,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 958,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 970,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 957,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 975,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 995,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 991,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1012,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1026,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1029,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1025,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 921,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 920,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1052,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1050,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1049,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1062,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1064,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1060,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1059,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1073,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1047,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1088,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1087,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1091,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1086,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1085,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1097,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1098,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1099,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1108,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1100,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1096,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1095,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1126,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1143,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1144,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1145,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1142,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1141,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1083,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1163,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1162,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1166,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1160,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1153,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1152,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 684,\n        columnNumber: 5\n    }, undefined);\n}, \"FnF7oxCAOboLw5khnOglgPIfw2g=\")), \"FnF7oxCAOboLw5khnOglgPIfw2g=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/DragEnhancer */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/DragEnhancer.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, fileId, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _dragEnhancerRef_current, _selectionManagerRef_current, _clipboardManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n        (_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.destroy();\n        dragEnhancerRef.current = null;\n        (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n        selectionManagerRef.current = null;\n        (_clipboardManagerRef_current = clipboardManagerRef.current) === null || _clipboardManagerRef_current === void 0 ? void 0 : _clipboardManagerRef_current.destroy();\n        clipboardManagerRef.current = null;\n        (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n        eventManagerRef.current = null;\n        (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n        styleManagerRef.current = null;\n        (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n        coreManagerRef.current = null;\n        setIsInitialized(false);\n    }, []);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_7__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 启用拖拽增强器，使用 SaveManager 统一保存机制\n            if (!readonly && enableDragEnhancement) {\n                var _dragConfig_persistence, _dragConfig_persistence1;\n                var _dragConfig_persistence_autoSave, _dragConfig_persistence_saveDelay;\n                const enhancedDragConfig = {\n                    ...dragConfig,\n                    persistence: {\n                        autoSave: (_dragConfig_persistence_autoSave = (_dragConfig_persistence = dragConfig.persistence) === null || _dragConfig_persistence === void 0 ? void 0 : _dragConfig_persistence.autoSave) !== null && _dragConfig_persistence_autoSave !== void 0 ? _dragConfig_persistence_autoSave : true,\n                        saveDelay: (_dragConfig_persistence_saveDelay = (_dragConfig_persistence1 = dragConfig.persistence) === null || _dragConfig_persistence1 === void 0 ? void 0 : _dragConfig_persistence1.saveDelay) !== null && _dragConfig_persistence_saveDelay !== void 0 ? _dragConfig_persistence_saveDelay : 1000\n                    }\n                };\n                dragEnhancerRef.current = new _managers_DragEnhancer__WEBPACK_IMPORTED_MODULE_6__.DragEnhancer(mindMapInstance, enhancedDragConfig);\n                // 设置当前文件ID\n                if (fileId) {\n                    dragEnhancerRef.current.setCurrentFileId(fileId);\n                }\n                dragEnhancerRef.current.initialize();\n                console.log(\"\\uD83C\\uDFAF DragEnhancer 已启用并集成 SaveManager，文件ID:\", fileId);\n            }\n            setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            }\n        }), [\n        resizeCanvas,\n        isInitialized\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 466,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 462,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 485,\n        columnNumber: 5\n    }, undefined);\n}, \"n9iMgar1C7HJsnxniI5k3ycy3s4=\")), \"n9iMgar1C7HJsnxniI5k3ycy3s4=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts":
/*!*********************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/ClipboardManager.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClipboardManager: function() { return /* binding */ ClipboardManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../types */ \"(app-pages-browser)/./src/types/index.ts\");\n/**\n * 剪贴板管理器\n * 负责思维导图节点的复制粘贴功能\n */ \nclass ClipboardManager {\n    /**\n   * 实现ICleanable接口 - 是否已销毁\n   */ get isDestroyed() {\n        return this._cleanupState === _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYED;\n    }\n    /**\n   * 实现ICleanable接口 - 当前清理状态\n   */ get cleanupState() {\n        return this._cleanupState;\n    }\n    /**\n   * 复制节点\n   */ copyNode(node) {\n        try {\n            if (!node || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的节点或思维导图实例\");\n                return false;\n            }\n            // 激活目标节点\n            this.mindMapInstance.renderer.clearActiveNodeList();\n            this.mindMapInstance.renderer.addNodeToActiveList(node);\n            // 使用SimpleMindMap的内部复制方法\n            const copiedData = this.mindMapInstance.renderer.copyNode();\n            if (copiedData && copiedData.length > 0) {\n                this.copiedNodeData = copiedData;\n                console.log(\"✅ 复制节点成功:\", copiedData);\n                return true;\n            } else {\n                console.warn(\"⚠️ 复制节点失败，没有获取到数据\");\n                return false;\n            }\n        } catch (error) {\n            console.error(\"❌ 复制节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 粘贴节点作为子节点\n   */ pasteAsChild(targetNode) {\n        try {\n            if (!this.hasCopiedData()) {\n                console.warn(\"⚠️ 没有可粘贴的数据\");\n                return false;\n            }\n            if (!targetNode || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的目标节点或思维导图实例\");\n                return false;\n            }\n            // 激活目标节点\n            this.mindMapInstance.renderer.clearActiveNodeList();\n            this.mindMapInstance.renderer.addNodeToActiveList(targetNode);\n            // 执行粘贴命令\n            this.mindMapInstance.execCommand(\"PASTE_NODE\", this.copiedNodeData);\n            console.log(\"✅ 粘贴为子节点成功\");\n            return true;\n        } catch (error) {\n            console.error(\"❌ 粘贴为子节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 粘贴节点作为同级节点\n   */ pasteAsSibling(targetNode) {\n        try {\n            if (!this.hasCopiedData()) {\n                console.warn(\"⚠️ 没有可粘贴的数据\");\n                return false;\n            }\n            if (!targetNode || !this.mindMapInstance) {\n                console.warn(\"⚠️ 无效的目标节点或思维导图实例\");\n                return false;\n            }\n            // 根节点不能粘贴同级节点\n            if (targetNode.isRoot) {\n                console.warn(\"⚠️ 根节点不能粘贴同级节点\");\n                return false;\n            }\n            // 激活目标节点的父节点\n            if (targetNode.parent) {\n                this.mindMapInstance.renderer.clearActiveNodeList();\n                this.mindMapInstance.renderer.addNodeToActiveList(targetNode.parent);\n                // 执行粘贴命令\n                this.mindMapInstance.execCommand(\"PASTE_NODE\", this.copiedNodeData);\n                console.log(\"✅ 粘贴为同级节点成功\");\n                return true;\n            } else {\n                console.warn(\"⚠️ 目标节点没有父节点\");\n                return false;\n            }\n        } catch (error) {\n            console.error(\"❌ 粘贴为同级节点失败:\", error);\n            return false;\n        }\n    }\n    /**\n   * 检查是否有可粘贴的数据\n   */ hasCopiedData() {\n        return this.copiedNodeData !== null && Array.isArray(this.copiedNodeData) && this.copiedNodeData.length > 0;\n    }\n    /**\n   * 获取复制的数据\n   */ getCopiedData() {\n        return this.copiedNodeData;\n    }\n    /**\n   * 清空复制的数据\n   */ clearCopiedData() {\n        this.copiedNodeData = null;\n        console.log(\"✅ 清空复制数据\");\n    }\n    /**\n   * 获取复制数据的摘要信息\n   */ getCopiedDataSummary() {\n        var _this_copiedNodeData__data, _this_copiedNodeData_;\n        if (!this.hasCopiedData()) {\n            return \"无复制数据\";\n        }\n        const nodeCount = this.copiedNodeData.length;\n        const firstNodeText = ((_this_copiedNodeData_ = this.copiedNodeData[0]) === null || _this_copiedNodeData_ === void 0 ? void 0 : (_this_copiedNodeData__data = _this_copiedNodeData_.data) === null || _this_copiedNodeData__data === void 0 ? void 0 : _this_copiedNodeData__data.text) || \"未知节点\";\n        if (nodeCount === 1) {\n            return \"已复制: \".concat(firstNodeText);\n        } else {\n            return \"已复制: \".concat(firstNodeText, \" 等 \").concat(nodeCount, \" 个节点\");\n        }\n    }\n    /**\n   * 实现ICleanable接口 - 销毁方法\n   */ async destroy() {\n        if (this._cleanupState !== _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE) {\n            console.log(\"\\uD83D\\uDD12 ClipboardManager已在清理中或已销毁，跳过重复清理\");\n            return;\n        }\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYING;\n        console.log(\"\\uD83E\\uDDF9 开始清理ClipboardManager...\");\n        try {\n            this.clearCopiedData();\n            this.mindMapInstance = null;\n            this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.DESTROYED;\n            console.log(\"✅ ClipboardManager清理完成\");\n        } catch (error) {\n            console.error(\"❌ ClipboardManager清理失败:\", error);\n            this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE; // 重置状态以允许重试\n            throw error;\n        }\n    }\n    constructor(mindMapInstance){\n        this.copiedNodeData = null;\n        this.mindMapInstance = null;\n        // 清理状态管理\n        this._cleanupState = _types__WEBPACK_IMPORTED_MODULE_0__.CleanupState.ACTIVE;\n        this.mindMapInstance = mindMapInstance;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\n"));

/***/ })

});
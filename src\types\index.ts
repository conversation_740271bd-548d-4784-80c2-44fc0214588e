/**
 * 作品展示平台 - TypeScript 类型定义
 */

// 作品内容类型
export type ArtworkContentType = 'canvas' | 'text' | 'mixed';

// 作品内容接口
export interface ArtworkContent {
  type: ArtworkContentType;
  data: any; // 具体内容数据，根据type不同而变化
  settings?: ContentSettings;
}

// 内容设置接口
export interface ContentSettings {
  fontSize?: number;
  fontFamily?: string;
  color?: string;
  backgroundColor?: string;
  [key: string]: any;
}

// 作品元数据接口
export interface ArtworkMetadata {
  fileSize: number; // 数据大小（字节）
  exportFormat?: string; // 导出格式
  lastExportAt?: number; // 最后导出时间
  importSource?: string; // 导入来源
}

// 作品接口
export interface Artwork {
  id: string; // 唯一标识符 (UUID)
  title: string; // 作品标题
  description?: string; // 作品描述（可选）
  content: ArtworkContent; // 作品内容数据
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
  tags?: string[]; // 标签数组（可选）
  metadata: ArtworkMetadata; // 元数据
}

// 字体接口
export interface Font {
  id: string; // 字体ID
  name: string; // 字体名称
  family: string; // 字体族名
  data: ArrayBuffer; // 字体文件数据
  format: string; // 字体格式 (woff2, woff, ttf等)
  uploadedAt: number; // 上传时间
  size: number; // 文件大小
}

// 导入导出数据格式
export interface ExportData {
  version: string; // 导出格式版本
  exportedAt: number; // 导出时间戳
  artworks: Artwork[]; // 作品数组
  metadata: {
    totalCount: number; // 作品总数
    platform: string; // 平台标识
  };
}

// 导入结果接口
export interface ImportResult {
  success: boolean;
  importedCount: number;
  skippedCount: number;
  errors: string[];
  conflicts?: ConflictItem[];
}

// 冲突项接口
export interface ConflictItem {
  id: string;
  title: string;
  action: 'skip' | 'overwrite' | 'rename';
}

// 数据库配置接口
export interface DatabaseConfig {
  name: string;
  version: number;
  stores: {
    [storeName: string]: {
      keyPath: string;
      indexes?: {
        [indexName: string]: {
          keyPath: string | string[];
          unique?: boolean;
        };
      };
    };
  };
}

// 文本分割相关类型定义
export interface TextSegment {
  id: string;                    // 分段唯一标识
  fileId: string;               // 所属文件ID
  content: string;              // 分段内容
  startIndex: number;           // 在原文中的起始位置
  endIndex: number;             // 在原文中的结束位置
  wordCount: number;            // 字数统计
  sentenceCount: number;        // 句子数量
  language: 'zh' | 'en' | 'mixed'; // 语言类型
  createdAt: number;            // 创建时间
}

export interface SegmentOptions {
  maxWords: number;             // 最大字数限制 (默认500)
  language: 'zh' | 'en' | 'auto'; // 语言类型
  preserveParagraphs: boolean;  // 是否保持段落完整性
  preserveCodeBlocks: boolean;  // 是否保护代码块
  minWords?: number;            // 最小字数限制 (默认50)
}

export interface SegmentationResult {
  segments: TextSegment[];      // 分割结果
  totalSegments: number;        // 总分段数
  totalWords: number;           // 总字数
  averageWordsPerSegment: number; // 平均每段字数
  processingTime: number;       // 处理耗时(ms)
}

export interface FileAssociation {
  id: string;                   // 关联记录ID
  fileId: string;               // 文件ID
  fileName: string;             // 文件名
  filePath: string;             // 文件路径
  fileType: string;             // 文件类型
  segmentCount: number;         // 分段数量
  totalWords: number;           // 总字数
  options: SegmentOptions;      // 分割选项
  createdAt: number;            // 创建时间
  updatedAt: number;            // 更新时间
}

export interface ProcessedFile {
  file: StoredFile;             // 原始文件
  segments: TextSegment[];      // 分割片段
  segmentationResult: SegmentationResult; // 分割结果
  association: FileAssociation; // 关联记录
}

// 数据库操作结果接口
export interface DatabaseResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// 搜索选项接口
export interface SearchOptions {
  query?: string;
  tags?: string[];
  dateRange?: {
    start: number;
    end: number;
  };
  sortBy?: 'createdAt' | 'updatedAt' | 'title';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// 分页结果接口
export interface PaginatedResult<T> {
  items: T[];
  total: number;
  hasMore: boolean;
  offset: number;
}

// ==================== 字体持久化相关类型定义 ====================

// 字体格式类型
export type FontFormat = 'woff2' | 'woff' | 'ttf' | 'otf';

// 字体应用范围类型
export type FontApplicationScopeType = 'global' | 'selective';

// 字体应用范围接口
export interface FontApplicationScope {
  type: FontApplicationScopeType;
  selectors?: string[]; // 选择器列表（selective时使用）
  excludeSelectors?: string[]; // 排除选择器列表
}

// 字体应用设置接口
export interface FontApplicationSettings {
  fallbackFonts: string[]; // 备用字体列表
  loadTimeout: number; // 加载超时时间（毫秒）
  enableSmoothing: boolean; // 是否启用字体平滑
  preloadOnStartup: boolean; // 是否在启动时预加载
}

// 字体应用配置接口
export interface FontApplicationConfig {
  id: string; // 配置唯一标识符
  fontId: string; // 应用的字体ID
  fontFamily: string; // 字体族名
  appliedAt: number; // 应用时间戳
  scope: FontApplicationScope; // 应用范围
  isActive: boolean; // 是否为当前活跃配置
  settings: FontApplicationSettings; // 应用设置
}

// 字体应用历史记录接口
export interface FontApplicationHistory {
  id: string; // 历史记录ID
  fontId: string; // 字体ID
  fontName: string; // 字体名称
  fontFamily: string; // 字体族名
  appliedAt: number; // 应用时间戳
  duration: number; // 使用时长（毫秒）
  config: FontApplicationConfig; // 应用配置快照
}

// 字体配置导出格式接口
export interface FontConfigExport {
  version: string; // 导出格式版本
  exportedAt: number; // 导出时间戳
  activeConfig: FontApplicationConfig; // 当前活跃配置
  history: FontApplicationHistory[]; // 历史记录
  fonts: { // 关联的字体文件信息
    id: string;
    name: string;
    family: string;
    format: string;
    checksum: string; // 文件校验和
  }[];
  metadata: {
    platform: string; // 平台标识
    userAgent: string; // 用户代理信息
    totalConfigs: number; // 配置总数
  };
}

// 字体元数据接口
export interface FontMetadata {
  weight?: number; // 字体粗细
  style?: 'normal' | 'italic'; // 字体样式
  subset?: string; // 字符子集
  license?: string; // 许可证信息
}

// 扩展的字体接口（包含元数据）
export interface ExtendedFont extends Font {
  preview?: string; // 预览文本
  metadata: FontMetadata; // 字体元数据
}

// 字体加载结果接口
export interface FontLoadResult {
  success: boolean;
  fontId: string;
  fontFamily?: string;
  loadTime?: number; // 加载时间（毫秒）
  error?: string;
}

// 字体缓存项接口
export interface FontCacheItem {
  font: Font;
  lastAccessed: number;
  accessCount: number;
  loadTime: number;
}

// 存储适配器接口
export interface StorageAdapter {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  keys(): Promise<string[]>;
}

// 存储同步结果接口
export interface StorageSyncResult {
  success: boolean;
  syncedKeys: string[];
  failedKeys: string[];
  errors: { key: string; error: string }[];
}

// 字体持久化错误类型
export type FontPersistenceErrorType =
  | 'NETWORK_ERROR'
  | 'FORMAT_ERROR'
  | 'PERMISSION_ERROR'
  | 'STORAGE_ERROR'
  | 'TIMEOUT_ERROR'
  | 'UNKNOWN_ERROR';

// 字体持久化错误接口
export interface FontPersistenceError {
  type: FontPersistenceErrorType;
  message: string;
  fontId?: string;
  timestamp: number;
  details?: any;
}

// 性能指标接口
export interface PerformanceMetrics {
  loadTimes: { fontId: string; loadTime: number; timestamp: number }[];
  errorCounts: Map<string, number>;
  cacheHitRates: number[];
}

// 性能报告接口
export interface PerformanceReport {
  averageLoadTime: number;
  errorRate: number;
  cacheEfficiency: number;
  recommendations: string[];
}

// 配置验证结果接口
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// 配置迁移结果接口
export interface MigrationResult {
  success: boolean;
  fromVersion: string;
  toVersion: string;
  migratedConfigs: number;
  errors: string[];
}

// ==================== 作品编辑器相关类型定义 ====================

// 文件树节点类型
export type FileTreeNodeType = 'file' | 'folder';

// 文件类型
export type EditorFileType = 'markdown' | 'text' | 'folder' | 'chart' | 'xmind';

// 文件树节点接口
export interface FileTreeNode {
  id: string; // 节点唯一标识符
  name: string; // 文件/文件夹名称
  type: FileTreeNodeType; // 节点类型
  parentId: string | null; // 父节点ID，根节点为null
  children?: FileTreeNode[]; // 子节点数组（仅文件夹类型）
  content?: string; // 文件内容（仅文件类型）
  path: string; // 完整路径
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
  isExpanded?: boolean; // 是否展开（仅文件夹类型）
}

// 编辑器文件接口
export interface EditorFile {
  id: string; // 文件唯一标识符
  name: string; // 文件名称
  content: string; // 文件内容
  type: EditorFileType; // 文件类型
  path: string; // 完整路径
  lastModified: number; // 最后修改时间
  isDirty: boolean; // 是否有未保存的更改
  isReadOnly?: boolean; // 是否只读
}

// 编辑器设置接口
export interface EditorSettings {
  fontSize: number; // 字体大小
  fontWeight: number; // 字体粗细
  fontFamily: string; // 字体族
  theme: 'light' | 'dark'; // 编辑器主题
  wordWrap: boolean | 'wordWrapColumn'; // 是否自动换行，支持按字符数换行
  wordWrapColumn?: number; // 换行字符数限制（当wordWrap为'wordWrapColumn'时使用）
  showRulers?: boolean; // 是否显示垂直标尺线
  rulers?: number[]; // 标尺线位置数组
  showLineNumbers: boolean; // 是否显示行号
  enablePreview: boolean; // 是否启用预览
  tabSize: number; // Tab缩进大小
  insertSpaces: boolean; // 是否使用空格代替Tab
  autoSave: boolean; // 是否启用自动保存
  autoSaveDelay: number; // 自动保存延迟（毫秒）
}

// AI消息类型 - 扩展支持developer角色
export type AIMessageType = 'user' | 'assistant' | 'system' | 'developer';

// AI消息内容类型 - 支持OpenAI API的多模态格式
export type AIMessageContent = 
  | string 
  | Array<{
      type: 'text' | 'image_url';
      text?: string;
      image_url?: {
        url: string;
        detail?: 'low' | 'high' | 'auto';
      };
    }>;

// AI消息接口
export interface AIMessage {
  id: string; // 消息唯一标识符
  type: AIMessageType; // 消息类型
  content: AIMessageContent; // 消息内容 - 支持字符串或多模态数组格式
  timestamp: number; // 时间戳
  fileReferences?: string[]; // 引用的文件ID数组
  metadata?: {
    tokens?: number; // 消息token数量
    model?: string; // 使用的AI模型
    temperature?: number; // 生成温度
  };
}

// 消息层接口（用于辅助AI回复层）
export interface MessageLayer {
  id: string; // 层级唯一标识符
  type: 'user' | 'ai' | 'helper-response' | 'integrated'; // 层级类型
  content: string; // 层级内容
  timestamp: number; // 时间戳
  metadata?: {
    helperResponses?: HelperResponse[]; // 辅助AI回复
    originalMessage?: string; // 原始消息
    humanBlock?: HumanBlockContent; // Human代码块内容
    isExpanded?: boolean; // 是否展开
    layerId?: string; // 层级ID
  };
}

// Human代码块内容接口
export interface HumanBlockContent {
  roleName: string; // 角色名称
  roleDescription: string; // 角色描述
  question: string; // 问题内容
}

// AI文件操作类型
export type AIFileActionType = 'read' | 'write' | 'create' | 'delete' | 'rename';

// AI文件操作接口
export interface AIFileAction {
  type: AIFileActionType; // 操作类型
  fileId: string; // 目标文件ID
  content?: string; // 新内容（write/create时使用）
  newName?: string; // 新名称（rename时使用）
  parentId?: string; // 父文件夹ID（create时使用）
  confirmed?: boolean; // 是否已确认操作
}

// 作品编辑器数据接口
export interface ArtworkEditorData {
  id: string; // 对应作品ID
  fileTree: FileTreeNode; // 文件树根节点
  settings: EditorSettings; // 编辑器设置
  aiHistory: AIMessage[]; // AI对话历史
  lastEditedFile: string | null; // 最后编辑的文件ID
  openFiles: string[]; // 打开的文件ID数组
  activeFileId: string | null; // 当前活跃文件ID
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

// 存储的文件接口
export interface StoredFile {
  id: string; // 文件唯一标识符
  artworkId: string; // 关联的作品ID
  name: string; // 文件名称
  content: string; // 文件内容
  type: EditorFileType; // 文件类型
  parentId: string | null; // 父文件夹ID
  path: string; // 完整路径
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

// 编辑器状态接口
export interface EditorState {
  currentFile: EditorFile | null; // 当前编辑的文件
  fileTree: FileTreeNode; // 文件树
  aiHistory: AIMessage[]; // AI对话历史
  settings: EditorSettings; // 编辑器设置
  isLoading: boolean; // 是否正在加载
  unsavedChanges: boolean; // 是否有未保存的更改
  error: string | null; // 错误信息
}

// 文件操作结果接口
export interface FileOperationResult {
  success: boolean; // 操作是否成功
  data?: any; // 返回数据
  error?: string; // 错误信息
  affectedFiles?: string[]; // 受影响的文件ID数组
}

// 编辑器布局配置接口
export interface EditorLayoutConfig {
  leftPanelWidth: number; // 左侧面板宽度（百分比）
  rightPanelWidth: number; // 右侧面板宽度（百分比）
  showLeftPanel: boolean; // 是否显示左侧面板
  showRightPanel: boolean; // 是否显示右侧面板
  isLeftPanelCollapsed: boolean; // 左侧面板是否折叠
  isRightPanelCollapsed: boolean; // 右侧面板是否折叠
}

// 键盘快捷键配置接口
export interface KeyboardShortcut {
  key: string; // 按键组合
  action: string; // 操作名称
  description: string; // 描述
  enabled: boolean; // 是否启用
}

// 编辑器主题配置接口
export interface EditorThemeConfig {
  name: string; // 主题名称
  colors: {
    background: string; // 背景色
    foreground: string; // 前景色
    selection: string; // 选中背景色
    lineHighlight: string; // 行高亮色
    cursor: string; // 光标色
    border: string; // 边框色
  };
  syntax: {
    keyword: string; // 关键字颜色
    string: string; // 字符串颜色
    comment: string; // 注释颜色
    number: string; // 数字颜色
    operator: string; // 操作符颜色
  };
}

// ==================== AI助手功能相关类型定义 ====================

/**
 * OpenAI模型类型
 */
export type OpenAIModel =
  | 'gpt-4o'
  | 'gpt-4o-mini'
  | 'gpt-4-turbo'
  | 'gpt-4'
  | 'gpt-3.5-turbo'
  | 'gpt-3.5-turbo-16k';

/**
 * AI配置接口
 * 用于存储API Key和模型参数配置，支持自定义API URL和模型
 */
export interface AIConfig {
  id: string; // 配置唯一标识符
  name: string; // 配置名称
  apiKey: string; // API Key（加密存储）
  baseURL?: string; // 自定义API基础URL（可选，默认使用OpenAI官方）
  model: string; // 使用的模型（支持自定义模型名称）
  availableModels?: string[]; // 可用模型列表（自动获取或手动配置）
  temperature: number; // 生成温度 (0-2)
  maxTokens: number; // 最大token数量
  topP: number; // Top-p采样 (0-1)
  topk: number; // Top-p采样 (0-1)
  frequencyPenalty: number; // 频率惩罚 (-2 to 2)
  presencePenalty: number; // 存在惩罚 (-2 to 2)
  customHeaders?: Record<string, string>; // 自定义请求头（用于特殊API）
  isActive: boolean; // 是否为当前活跃配置
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
  roleType?: 'main' | 'helper'; // 角色类型：主AI或辅助AI（可选，默认为main）
}

/**
 * 辅助AI配置接口
 * 继承AIConfig，专门用于BrainstormingHelper功能的辅助角色配置
 */
export interface BrainstormingHelperConfig extends AIConfig {
  roleType: 'helper'; // 强制指定为辅助角色
}

/**
 * 辅助AI请求接口
 * 定义调用辅助AI时的请求结构
 */
export interface HelperRequest {
  roleName: string; // 角色名称（从```human代码块中解析）
  roleDescription: string; // 角色背景描述（从```human代码块中解析）
  question: string; // 要解决的问题（从```human代码块中解析）
  context?: string; // 额外的上下文信息（可选）
  timestamp: number; // 请求时间戳
}

/**
 * 辅助AI回复接口
 * 定义辅助AI回复的数据结构
 */
export interface HelperResponse {
  id: string; // 回复唯一标识符
  request: HelperRequest; // 关联的请求
  config: BrainstormingHelperConfig; // 使用的配置
  content: string; // 回复内容
  tokens: number; // 使用的token数量
  processingTime: number; // 处理时间（毫秒）
  timestamp: number; // 回复时间戳
  error?: string; // 错误信息（如果有）
}

/**
 * 多辅助AI回复集合接口
 * 用于管理多个辅助AI的并发回复
 */
export interface MultiHelperResponse {
  requestId: string; // 请求批次唯一标识符
  originalRequest: HelperRequest; // 原始请求
  responses: HelperResponse[]; // 所有辅助AI的回复
  totalTokens: number; // 总token使用量
  totalProcessingTime: number; // 总处理时间（毫秒）
  completedAt: number; // 完成时间戳
  errors: string[]; // 错误列表（如果有）
}

/**
 * AI响应接口
 * OpenAI API响应数据结构
 */
export interface AIResponse {
  id: string; // 响应ID
  object: string; // 对象类型
  created: number; // 创建时间戳
  model: string; // 使用的模型
  choices: AIChoice[]; // 选择数组
  usage: AIUsage; // token使用情况
  systemFingerprint?: string; // 系统指纹
}

/**
 * AI选择接口
 */
export interface AIChoice {
  index: number; // 选择索引
  message: AIMessage; // 消息内容
  finishReason: 'stop' | 'length' | 'content_filter' | 'tool_calls' | null; // 结束原因
}

/**
 * AI使用情况接口
 */
export interface AIUsage {
  promptTokens: number; // 提示token数量
  completionTokens: number; // 完成token数量
  totalTokens: number; // 总token数量
}

/**
 * AI流式响应块接口
 * 用于处理流式响应数据
 */
export interface AIStreamChunk {
  id: string; // 块ID
  object: string; // 对象类型
  created: number; // 创建时间戳
  model: string; // 使用的模型
  choices: AIStreamChoice[]; // 流式选择数组
}

/**
 * AI流式选择接口
 */
export interface AIStreamChoice {
  index: number; // 选择索引
  delta: AIMessageDelta; // 消息增量
  finishReason: 'stop' | 'length' | 'content_filter' | 'tool_calls' | null; // 结束原因
}

/**
 * AI消息增量接口
 * 用于流式响应的增量更新
 */
export interface AIMessageDelta {
  role?: AIMessageType; // 消息角色
  content?: string; // 消息内容增量
}

/**
 * 消息模板接口
 * 用于保存和复用常用的消息序列
 */
export interface MessageTemplate {
  id: string; // 模板唯一标识符
  name: string; // 模板名称
  description?: string; // 模板描述
  category: string; // 模板分类
  messages: Omit<AIMessage, 'id' | 'timestamp'>[]; // 消息序列（不包含id和timestamp）
  isBuiltIn: boolean; // 是否为内置模板
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

/**
 * 消息模板分类类型
 */
export type MessageTemplateCategory =
  | 'writing' // 写作助手
  | 'coding' // 代码生成
  | 'translation' // 翻译
  | 'analysis' // 分析
  | 'creative' // 创意
  | 'custom'; // 自定义

/**
 * AI对话历史接口
 * 用于存储完整的对话会话
 */
export interface AIConversation {
  id: string; // 对话唯一标识符
  artworkId: string; // 关联的作品ID
  title: string; // 对话标题
  messages: AIMessage[]; // 消息列表
  model: OpenAIModel; // 使用的模型
  totalTokens: number; // 总token使用量
  timestamp: number; // 对话时间戳
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

/**
 * AI服务状态接口
 */
export interface AIServiceStatus {
  isConnected: boolean; // 是否连接到API
  isStreaming: boolean; // 是否正在流式响应
  currentModel: OpenAIModel | null; // 当前使用的模型
  lastError: string | null; // 最后的错误信息
  requestCount: number; // 请求计数
  tokenUsage: number; // token使用量
}

/**
 * AI错误类型
 */
export type AIErrorType =
  | 'API_KEY_INVALID' // API Key无效
  | 'API_KEY_MISSING' // API Key缺失
  | 'RATE_LIMIT_EXCEEDED' // 速率限制
  | 'INSUFFICIENT_QUOTA' // 配额不足
  | 'MODEL_NOT_FOUND' // 模型不存在
  | 'NETWORK_ERROR' // 网络错误
  | 'TIMEOUT_ERROR' // 超时错误
  | 'UNKNOWN_ERROR'; // 未知错误

/**
 * AI错误接口
 */
export interface AIError {
  type: AIErrorType; // 错误类型
  message: string; // 错误消息
  code?: string; // 错误代码
  timestamp: number; // 错误时间戳
  details?: any; // 错误详情
}

/**
 * 内容插入选项接口
 */
export interface ContentInsertOptions {
  position: 'cursor' | 'end' | 'start'; // 插入位置
  format: 'plain' | 'markdown' | 'code'; // 内容格式
  addNewlines: boolean; // 是否添加换行符
  preserveIndentation: boolean; // 是否保持缩进
}

/**
 * AI助手面板状态接口
 */
export interface AIAssistantPanelState {
  activeTab: 'chat' | 'config' | 'templates' | 'history'; // 当前活跃标签
  isCollapsed: boolean; // 是否折叠
  width: number; // 面板宽度
  currentConversation: string | null; // 当前对话ID
  messageBuilder: AIMessage[]; // 消息构建器中的消息
  isStreaming: boolean; // 是否正在流式响应
}

/**
 * AI配置验证结果接口
 */
export interface AIConfigValidation {
  isValid: boolean; // 是否有效
  errors: string[]; // 错误列表
  warnings: string[]; // 警告列表
  apiKeyStatus: 'valid' | 'invalid' | 'untested'; // API Key状态
}

/**
 * AI统计信息接口
 */
export interface AIStatistics {
  totalConversations: number; // 总对话数
  totalMessages: number; // 总消息数
  totalTokensUsed: number; // 总token使用量
  averageTokensPerMessage: number; // 平均每条消息token数
  mostUsedModel: OpenAIModel; // 最常用模型
  dailyUsage: { date: string; tokens: number }[]; // 每日使用量
}

// ==================== 人设管理相关类型定义 ====================

/**
 * 人设文件夹接口
 * 用于组织和分类人设
 */
export interface PersonaFolder {
  id: string; // 文件夹唯一标识符
  name: string; // 文件夹名称
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
  personaCount?: number; // 包含的人设数量（计算字段）
}

/**
 * 人设配置接口
 * 用于管理AI助手的人设和角色定义
 */
export interface PersonaConfig {
  id: string; // 人设唯一标识符
  name: string; // 人设名称
  description: string; // 人设介绍（整合所有描述内容）
  folderId: string; // 所属文件夹ID
  isActive: boolean; // 是否为当前活跃人设
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

// ==================== 受众管理相关类型定义 ====================

/**
 * 受众配置接口
 * 用于管理AI助手的目标受众设置，影响回应风格和内容调整
 */
export interface AudienceConfig {
  id: string; // 受众唯一标识符
  name: string; // 受众名称（如：网文快餐、学术研究、商务沟通等）
  description?: string; // 受众描述（可选，详细说明受众特征和偏好）
  isActive: boolean; // 是否为当前激活受众
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

/**
 * 受众变化事件类型
 */
export type AudienceChangeEventType = 'create' | 'update' | 'delete' | 'activate' | 'error';

/**
 * 受众变化事件接口
 * 用于通知受众配置的变化
 */
export interface AudienceChangeEvent {
  type: AudienceChangeEventType; // 事件类型
  audience?: AudienceConfig; // 相关的受众配置
  previousAudience?: AudienceConfig; // 之前的受众配置（用于更新和激活事件）
  error?: string; // 错误信息（仅错误事件）
  timestamp: number; // 事件时间戳
}

/**
 * 受众变化回调函数类型
 */
export type AudienceChangeCallback = (event: AudienceChangeEvent) => void;

/**
 * 受众Context值接口
 * 定义受众状态管理Context提供的值和方法
 */
export interface AudienceContextValue {
  // 当前状态
  currentAudience: AudienceConfig | null; // 当前激活的受众配置
  allAudiences: AudienceConfig[]; // 所有受众配置列表
  isLoading: boolean; // 是否正在加载
  error: string | null; // 错误信息
  
  // 操作方法
  createAudience: (audienceData: Omit<AudienceConfig, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>; // 创建受众
  updateAudience: (audienceId: string, updates: Partial<Omit<AudienceConfig, 'id' | 'createdAt'>>) => Promise<void>; // 更新受众
  deleteAudience: (audienceId: string) => Promise<void>; // 删除受众
  activateAudience: (audienceId: string) => Promise<void>; // 激活受众
  refreshAudiences: () => Promise<void>; // 刷新受众列表
  
  // 事件订阅
  onAudienceChange: (callback: AudienceChangeCallback) => () => void; // 订阅受众变化事件
}

/**
 * 受众服务初始化结果接口
 */
export interface AudienceInitializationResult {
  success: boolean; // 是否成功
  defaultAudienceId?: string; // 默认受众ID
  createdAudiences?: string[]; // 创建的受众ID列表
  error?: string; // 错误信息
  isFirstTime?: boolean; // 是否为首次初始化
  initializationTime?: number; // 初始化耗时（毫秒）
}

/**
 * 默认受众配置类型
 */
export interface DefaultAudienceConfig {
  name: string; // 受众名称
  description: string; // 受众描述
  isActive: boolean; // 是否为默认激活受众
}

// ==================== 消息标记相关类型定义 ====================

/**
 * 消息标记类型
 * 定义消息标记的不同类型
 */
export type MessageTokenType = 'sentence' | 'paragraph' | 'code_block' | 'heading' | 'list_item';

/**
 * 消息标记接口
 * 基于现有AIMessage类型扩展，用于表示文件分割后的消息单元
 */
export interface MessageToken extends Omit<AIMessage, 'type'> {
  segmentId: string; // 关联的TextSegment ID
  fileId: string; // 源文件ID
  fileName: string; // 源文件名称
  filePath: string; // 源文件路径
  sequence: number; // 消息序列号（在文件中的顺序）
  messageType: MessageTokenType; // 消息标记类型
  contextWindow: {
    before?: string; // 前置上下文
    after?: string; // 后置上下文
    windowSize?: number; // 上下文窗口大小
  };
  navigation: {
    previousId?: string; // 前一个消息标记ID
    nextId?: string; // 后一个消息标记ID
    parentId?: string; // 父级消息标记ID（用于层级结构）
  };
  position: {
    startIndex: number; // 在原文中的起始位置
    endIndex: number; // 在原文中的结束位置
    lineStart?: number; // 起始行号
    lineEnd?: number; // 结束行号
  };
  statistics: {
    wordCount: number; // 字数统计
    characterCount: number; // 字符数统计
    sentenceCount: number; // 句子数量
  };
  language: 'zh' | 'en' | 'mixed'; // 语言类型
  isProcessed: boolean; // 是否已处理
  processingMetadata?: {
    processingTime: number; // 处理耗时（毫秒）
    processingMethod: string; // 处理方法
    confidence: number; // 处理置信度（0-1）
  };
}

/**
 * 消息标记集合接口
 * 用于管理单个文件的所有消息标记
 */
export interface MessageTokenCollection {
  id: string; // 集合唯一标识符
  fileId: string; // 源文件ID
  fileName: string; // 源文件名称
  filePath: string; // 源文件路径
  tokens: MessageToken[]; // 消息标记数组
  totalTokens: number; // 消息标记总数
  statistics: {
    totalWords: number; // 总字数
    totalCharacters: number; // 总字符数
    totalSentences: number; // 总句子数
    averageWordsPerToken: number; // 平均每个标记的字数
    languageDistribution: {
      zh: number; // 中文标记数量
      en: number; // 英文标记数量
      mixed: number; // 混合语言标记数量
    };
  };
  options: MessageTokenOptions; // 生成选项
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
  version: string; // 版本号
}

/**
 * 消息标记生成选项接口
 */
export interface MessageTokenOptions {
  maxWordsPerToken: number; // 每个标记的最大字数
  minWordsPerToken: number; // 每个标记的最小字数
  language: 'zh' | 'en' | 'auto'; // 语言类型
  preserveParagraphs: boolean; // 是否保持段落完整性
  preserveCodeBlocks: boolean; // 是否保护代码块
  includeContext: boolean; // 是否包含上下文
  contextWindowSize: number; // 上下文窗口大小
  enableNavigation: boolean; // 是否启用导航链接
  splitStrategy: 'sentence' | 'paragraph' | 'semantic' | 'hybrid'; // 分割策略
  customDelimiters?: string[]; // 自定义分隔符
}

/**
 * 消息标记生成结果接口
 */
export interface MessageTokenGenerationResult {
  collection: MessageTokenCollection; // 生成的消息标记集合
  success: boolean; // 是否成功
  processingTime: number; // 处理耗时（毫秒）
  statistics: {
    totalTokensGenerated: number; // 生成的标记总数
    averageTokenLength: number; // 平均标记长度
    processingSpeed: number; // 处理速度（字符/秒）
  };
  warnings: string[]; // 警告信息
  errors: string[]; // 错误信息
}

/**
 * 消息标记导航结果接口
 */
export interface MessageTokenNavigationResult {
  currentToken: MessageToken; // 当前标记
  previousToken?: MessageToken; // 前一个标记
  nextToken?: MessageToken; // 后一个标记
  contextTokens: MessageToken[]; // 上下文标记
  totalTokens: number; // 总标记数
  currentPosition: number; // 当前位置（从1开始）
}

/**
 * 消息标记搜索选项接口
 */
export interface MessageTokenSearchOptions {
  query: string; // 搜索查询
  fileId?: string; // 限制在特定文件
  messageType?: MessageTokenType; // 限制消息类型
  language?: 'zh' | 'en' | 'mixed'; // 限制语言类型
  caseSensitive: boolean; // 是否区分大小写
  wholeWord: boolean; // 是否全词匹配
  useRegex: boolean; // 是否使用正则表达式
  includeContext: boolean; // 是否包含上下文
  maxResults: number; // 最大结果数
}

/**
 * 消息标记搜索结果接口
 */
export interface MessageTokenSearchResult {
  tokens: MessageToken[]; // 匹配的标记
  totalMatches: number; // 总匹配数
  searchTime: number; // 搜索耗时（毫秒）
  hasMore: boolean; // 是否有更多结果
  query: string; // 搜索查询
}

/**
 * 消息标记批量操作选项接口
 */
export interface MessageTokenBatchOptions {
  fileIds: string[]; // 要处理的文件ID数组
  options: MessageTokenOptions; // 生成选项
  parallel: boolean; // 是否并行处理
  maxConcurrency: number; // 最大并发数
  onProgress?: (progress: MessageTokenBatchProgress) => void; // 进度回调
}

/**
 * 消息标记批量处理进度接口
 */
export interface MessageTokenBatchProgress {
  totalFiles: number; // 总文件数
  processedFiles: number; // 已处理文件数
  currentFile: string; // 当前处理的文件
  totalTokens: number; // 总生成标记数
  elapsedTime: number; // 已用时间（毫秒）
  estimatedTimeRemaining: number; // 预计剩余时间（毫秒）
  errors: string[]; // 错误列表
}

/**
 * 消息标记批量处理结果接口
 */
export interface MessageTokenBatchResult {
  collections: MessageTokenCollection[]; // 生成的集合数组
  totalFiles: number; // 总文件数
  successfulFiles: number; // 成功处理的文件数
  failedFiles: number; // 失败的文件数
  totalTokens: number; // 总生成标记数
  totalProcessingTime: number; // 总处理时间（毫秒）
  errors: { fileId: string; fileName: string; error: string }[]; // 错误详情
  warnings: string[]; // 警告信息
}

/**
 * 消息标记导出选项接口
 */
export interface MessageTokenExportOptions {
  format: 'json' | 'csv' | 'txt' | 'markdown'; // 导出格式
  includeMetadata: boolean; // 是否包含元数据
  includeContext: boolean; // 是否包含上下文
  includeStatistics: boolean; // 是否包含统计信息
  compression: boolean; // 是否压缩
  encoding: 'utf8' | 'utf16' | 'base64'; // 编码格式
}

/**
 * 消息标记导入选项接口
 */
export interface MessageTokenImportOptions {
  format: 'json' | 'csv' | 'txt'; // 导入格式
  encoding: 'utf8' | 'utf16' | 'base64'; // 编码格式
  validateData: boolean; // 是否验证数据
  mergeStrategy: 'overwrite' | 'merge' | 'skip'; // 合并策略
  onConflict?: (existing: MessageToken, imported: MessageToken) => MessageToken; // 冲突处理
}

/**
 * 消息标记统计信息接口
 */
export interface MessageTokenStatistics {
  totalCollections: number; // 总集合数
  totalTokens: number; // 总标记数
  totalFiles: number; // 总文件数
  averageTokensPerFile: number; // 平均每文件标记数
  languageDistribution: {
    zh: number; // 中文标记数
    en: number; // 英文标记数
    mixed: number; // 混合语言标记数
  };
  typeDistribution: {
    sentence: number; // 句子类型标记数
    paragraph: number; // 段落类型标记数
    code_block: number; // 代码块类型标记数
    heading: number; // 标题类型标记数
    list_item: number; // 列表项类型标记数
  };
  sizeDistribution: {
    small: number; // 小标记数（<100字符）
    medium: number; // 中等标记数（100-500字符）
    large: number; // 大标记数（>500字符）
  };
  processingMetrics: {
    averageProcessingTime: number; // 平均处理时间
    totalProcessingTime: number; // 总处理时间
    averageTokensPerSecond: number; // 平均处理速度
  };
}
/**
 
* 文件段落选择器相关类型定义
 */

// 文件信息接口
export interface FileInfo {
  fileId: string
  fileName: string
  filePath: string
  totalSegments: number
  createdAt: number
  updatedAt: number
}

// 选中的段落接口
export interface SelectedSegment {
  segment: MessageToken
  fileInfo: FileInfo
  segmentIndex: number
}

// 段落引用格式选项
export interface SegmentReferenceFormatOptions {
  includeContent: boolean // 是否包含内容预览
  maxContentLength: number // 最大内容长度
  format: 'standard' | 'markdown' | 'plain' // 引用格式
  showStatistics: boolean // 是否显示统计信息
}

// 段落搜索过滤器
export interface SegmentSearchFilters {
  fileType?: string // 文件类型过滤
  dateRange?: {
    start: Date
    end: Date
  } // 日期范围过滤
  minWordCount?: number // 最小字数
  maxWordCount?: number // 最大字数
  messageType?: MessageTokenType // 消息类型过滤
  language?: 'zh' | 'en' | 'mixed' // 语言过滤
}

// 段落搜索结果
export interface SegmentSearchResult {
  segments: MessageToken[]
  totalMatches: number
  searchTime: number
  hasMore: boolean
  query: string
  filters?: SegmentSearchFilters
}

// ==================== df工具相关类型定义 ====================

/**
 * 差异操作类型
 */
export type DiffOperationType = 'append' | 'replace' | 'insert' | 'create';

/**
 * 差异变更类型
 */
export type DiffChangeType = 'add' | 'delete' | 'modify' | 'unchanged';

/**
 * 单个差异变更接口
 */
export interface DiffChange {
  type: DiffChangeType; // 变更类型
  lineNumber: number; // 行号
  content: string; // 变更内容
  originalContent?: string; // 原始内容（modify类型时使用）
}

/**
 * 差异统计信息接口
 */
export interface DiffStats {
  additions: number; // 新增行数
  deletions: number; // 删除行数
  modifications: number; // 修改行数
  totalChanges: number; // 总变更数
}

/**
 * 差异操作结果接口
 */
export interface DiffResult {
  fileId: string; // 文件ID
  filePath: string; // 文件路径
  operation: DiffOperationType; // 操作类型
  originalContent: string; // 原始内容
  modifiedContent: string; // 修改后内容
  changes: DiffChange[]; // 变更列表
  stats: DiffStats; // 统计信息
  timestamp: number; // 操作时间戳
}

/**
 * 正则替换参数接口
 */
export interface RegexReplaceParams {
  pattern: string; // 正则表达式模式
  replacement: string; // 替换内容
  flags?: string; // 正则标志（g, i, m等）
  global?: boolean; // 是否全局替换
}

/**
 * 文本追加参数接口
 */
export interface TextAppendParams {
  content: string; // 要追加的内容
  addNewline?: boolean; // 是否添加换行符
  position?: 'end' | 'start'; // 追加位置
}

// ==================== 媒体文件上传相关类型定义 ====================

/**
 * 媒体文件类型
 */
export type MediaFileType = 'image' | 'video';

/**
 * 支持的图片格式
 */
export type SupportedImageFormat = 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp';

/**
 * 支持的视频格式
 */
export type SupportedVideoFormat = 'video/mp4' | 'video/webm' | 'video/mov' | 'video/avi';

/**
 * 媒体文件接口
 */
export interface MediaFile {
  id: string; // 媒体文件唯一标识符
  name: string; // 文件名称
  type: MediaFileType; // 媒体类型（图片或视频）
  mimeType: SupportedImageFormat | SupportedVideoFormat; // MIME类型
  size: number; // 文件大小（字节）
  base64Data?: string; // Base64编码数据（可选，支持懒加载）
  thumbnailData?: string; // 缩略图Base64数据（可选）
  uploadedAt: number; // 上传时间戳
  artworkId: string; // 关联的作品ID
  chunkIds?: string[]; // 分块存储时的块ID列表（用于大文件）
  isChunked?: boolean; // 是否为分块存储
  originalSize?: number; // 原始文件大小（压缩前）
}

/**
 * 媒体文件分块接口（用于大文件存储）
 */
export interface MediaFileChunk {
  id: string; // 分块唯一标识符
  fileId: string; // 所属媒体文件ID
  chunkIndex: number; // 分块索引（从0开始）
  data: string; // 分块的Base64数据
  totalChunks: number; // 总分块数量
  chunkSize: number; // 当前分块大小
  createdAt: number; // 创建时间戳
}

/**
 * 扩展的AI消息接口（支持媒体文件）
 */
export interface EnhancedAIMessage extends AIMessage {
  mediaFiles?: MediaFile[]; // 关联的媒体文件列表
  mediaReferences?: string[]; // 媒体文件ID引用列表
  hasMedia?: boolean; // 是否包含媒体文件的快速标识
}

/**
 * 媒体上传配置接口
 */
export interface MediaUploadConfig {
  maxImageSize: number; // 图片最大大小（字节）
  maxVideoSize: number; // 视频最大大小（字节）
  supportedImageFormats: SupportedImageFormat[]; // 支持的图片格式
  supportedVideoFormats: SupportedVideoFormat[]; // 支持的视频格式
  chunkSize: number; // 分块大小（字节）
  enableThumbnail: boolean; // 是否启用缩略图
  thumbnailMaxSize: number; // 缩略图最大尺寸
  compressionQuality: number; // 压缩质量（0-1）
}

/**
 * 媒体上传错误类型
 */
export type MediaUploadErrorType =
  | 'FILE_TOO_LARGE' // 文件过大
  | 'INVALID_FORMAT' // 格式不支持
  | 'UPLOAD_FAILED' // 上传失败
  | 'STORAGE_ERROR' // 存储错误
  | 'ENCODING_ERROR' // 编码错误
  | 'CHUNK_ERROR' // 分块错误
  | 'THUMBNAIL_ERROR' // 缩略图生成错误
  | 'NETWORK_ERROR' // 网络错误
  | 'UNKNOWN_ERROR'; // 未知错误

/**
 * 媒体上传错误接口
 */
export interface MediaUploadError {
  type: MediaUploadErrorType; // 错误类型
  message: string; // 错误消息
  fileName?: string; // 相关文件名
  fileSize?: number; // 文件大小
  timestamp: number; // 错误时间戳
  details?: any; // 错误详情
}

/**
 * 媒体上传结果接口
 */
export interface MediaUploadResult {
  success: boolean; // 是否成功
  data?: MediaFile; // 上传成功时的媒体文件信息
  error?: MediaUploadError; // 上传失败时的错误信息
  progress?: number; // 上传进度（0-100）
}

/**
 * 媒体文件预览信息接口
 */
export interface MediaFilePreview {
  id: string; // 媒体文件ID
  name: string; // 文件名
  type: MediaFileType; // 媒体类型
  size: number; // 文件大小
  thumbnailUrl?: string; // 缩略图URL（如果有）
  duration?: number; // 视频时长（秒，仅视频文件）
  dimensions?: { // 尺寸信息
    width: number;
    height: number;
  };
  uploadedAt: number; // 上传时间
}



/**
 * 媒体文件管理操作类型
 */
export type MediaFileOperation = 'view' | 'delete' | 'rename' | 'download' | 'share';

/**
 * 媒体文件管理结果接口
 */
export interface MediaFileOperationResult {
  success: boolean; // 操作是否成功
  operation: MediaFileOperation; // 执行的操作
  fileId: string; // 操作的文件ID
  message?: string; // 操作结果消息
  error?: string; // 错误信息（如果失败）
}

// ==================== 提示词管理相关类型定义 ====================

/**
 * 提示词模板接口
 * 用于存储单个提示词的内容和元数据
 */
export interface PromptTemplate {
  id: string; // 提示词唯一标识符
  content: string; // 提示词内容（一句话）
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

/**
 * 提示词配置接口
 * 用于存储提示词的组合配置，支持多个提示词的拼接
 */
export interface PromptConfig {
  id: string; // 配置唯一标识符
  name: string; // 配置名称
  selectedPrompts: string[]; // 选中的提示词ID数组，按顺序拼接
  isActive: boolean; // 是否为当前激活配置
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后修改时间戳
}

// ==================== 统一清理接口相关类型定义 ====================

/**
 * 清理状态枚举
 * 用于跟踪组件或管理器的清理状态
 */
export enum CleanupState {
  ACTIVE = 'active',           // 活跃状态，正常运行
  DESTROYING = 'destroying',   // 正在销毁中
  DESTROYED = 'destroyed'      // 已销毁完成
}

/**
 * 清理错误类型
 */
export type CleanupErrorType =
  | 'EVENT_UNBIND_FAILED'      // 事件解绑失败
  | 'RESOURCE_CLEANUP_FAILED'  // 资源清理失败
  | 'TIMEOUT_ERROR'            // 清理超时
  | 'DEPENDENCY_ERROR'         // 依赖清理错误
  | 'UNKNOWN_ERROR';           // 未知错误

/**
 * 清理错误接口
 */
export interface CleanupError {
  type: CleanupErrorType;      // 错误类型
  message: string;             // 错误消息
  component?: string;          // 相关组件名称
  timestamp: number;           // 错误时间戳
  details?: any;               // 错误详情
}

/**
 * 统一清理接口
 * 为所有需要清理资源的类提供统一的清理方法签名
 */
export interface ICleanable {
  /**
   * 销毁方法，清理所有资源
   * @returns Promise<void> 或 void，支持同步和异步清理
   */
  destroy(): Promise<void> | void;

  /**
   * 是否已销毁的状态标识
   */
  readonly isDestroyed: boolean;

  /**
   * 当前清理状态
   */
  readonly cleanupState: CleanupState;
}

/**
 * 清理结果接口
 */
export interface CleanupResult {
  success: boolean;            // 是否成功
  component: string;           // 组件名称
  duration: number;            // 清理耗时（毫秒）
  errors: CleanupError[];      // 错误列表
  warnings: string[];          // 警告信息
  timestamp: number;           // 完成时间戳
}

/**
 * 批量清理结果接口
 */
export interface BatchCleanupResult {
  totalComponents: number;     // 总组件数
  successfulCleanups: number;  // 成功清理数
  failedCleanups: number;      // 失败清理数
  totalDuration: number;       // 总耗时（毫秒）
  results: CleanupResult[];    // 详细结果列表
  overallSuccess: boolean;     // 整体是否成功
}



// ==================== 保存管理器相关类型定义 ====================

/**
 * 保存状态枚举
 */
export enum SaveState {
  idle = 'idle',           // 空闲状态
  pending = 'pending',     // 等待保存
  saving = 'saving',       // 正在保存
  saved = 'saved',         // 保存成功
  error = 'error'          // 保存失败
}

/**
 * 保存选项接口
 */
export interface SaveOptions {
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number;
  /** 保存类型标识 */
  saveType?: 'mindmap' | 'drag' | 'editor' | 'manual';
  /** 是否强制立即保存 */
  immediate?: boolean;
  /** 重试次数 */
  retryCount?: number;
}

/**
 * 文件保存状态接口
 */
export interface FileSaveState {
  /** 文件ID */
  fileId: string;
  /** 当前保存状态 */
  state: SaveState;
  /** 保存定时器 */
  timer?: NodeJS.Timeout;
  /** 最后保存时间 */
  lastSaveTime: number;
  /** 待保存的数据 */
  pendingData?: any;
  /** 保存选项 */
  options?: SaveOptions;
  /** 错误信息 */
  error?: string;
  /** 重试次数 */
  retryCount: number;
}

/**
 * 保存状态变更回调接口
 */
export interface SaveStateChangeCallback {
  (fileId: string, state: SaveState, error?: string): void;
}

/**
 * 保存执行回调接口
 */
export interface SaveExecuteCallback {
  (fileId: string, data: any, options?: SaveOptions): Promise<boolean>;
}

// ==================== 思维导图相关类型定义 ====================

// 导出思维导图相关类型
export * from './mindmap';